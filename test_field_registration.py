#!/usr/bin/env python3
"""
测试 Arkime 字段注册和显示功能

这个脚本验证：
1. 字段是否正确注册到 ES
2. 会话数据是否包含正确的字段
3. 前端是否能正确显示字段
"""

import json
import urllib.request
import urllib.parse
import sys
from datetime import datetime

class ArkimeFieldTest:
    def __init__(self, es_url="http://localhost:9200", viewer_url="http://localhost:8005"):
        self.es_url = es_url
        self.viewer_url = viewer_url
        
    def test_field_registration(self):
        """测试字段注册"""
        print("=== 测试字段注册 ===")
        
        # 测试的字段列表
        test_fields = [
            {"id": "host.http", "expected_dbField2": "http.host"},
            {"id": "http.method", "expected_dbField2": "http.method"},
            {"id": "http.uri", "expected_dbField2": "http.uri"},
            {"id": "protocol", "expected_dbField2": "protocol"},
            {"id": "sdx.linename1", "expected_dbField2": "sdx.linename1"},
            {"id": "sdx.linename2", "expected_dbField2": "sdx.linename2"}
        ]
        
        success = True
        for field in test_fields:
            try:
                req = urllib.request.Request(f"{self.es_url}/arkime_fields/_doc/{field['id']}")
                with urllib.request.urlopen(req) as response:
                    result = response.read().decode('utf-8')
                    data = json.loads(result)
                    
                    if data.get('found'):
                        source = data['_source']
                        dbField2 = source.get('dbField2')
                        if dbField2 == field['expected_dbField2']:
                            print(f"✅ {field['id']}: 注册正确 (dbField2: {dbField2})")
                        else:
                            print(f"❌ {field['id']}: dbField2 不匹配 (期望: {field['expected_dbField2']}, 实际: {dbField2})")
                            success = False
                    else:
                        print(f"❌ {field['id']}: 字段未找到")
                        success = False
                        
            except Exception as e:
                print(f"❌ {field['id']}: 检查失败 - {e}")
                success = False
                
        return success
    
    def test_session_data(self):
        """测试会话数据"""
        print("\n=== 测试会话数据 ===")
        
        # 获取今天的索引
        date_str = datetime.now().strftime("%y%m%d")
        index_name = f"arkime_sessions3-{date_str}"
        
        try:
            # 搜索包含 HTTP 数据的会话
            req = urllib.request.Request(f"{self.es_url}/{index_name}/_search?q=http.method:*&size=1")
            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)
                
                hits = data['hits']['hits']
                if len(hits) > 0:
                    session = hits[0]['_source']
                    print(f"✅ 找到 {data['hits']['total']['value']} 个包含 HTTP 数据的会话")
                    
                    # 检查关键字段
                    fields_to_check = [
                        ('http.method', 'HTTP 方法'),
                        ('http.host', 'HTTP 主机'),
                        ('http.uri', 'HTTP URI'),
                        ('protocol', '协议')
                    ]
                    
                    for field_name, description in fields_to_check:
                        if field_name in session:
                            value = session[field_name]
                            print(f"  ✅ {description} ({field_name}): {value}")
                        else:
                            print(f"  ❌ {description} ({field_name}): 字段缺失")
                    
                    # 检查 SDX 字段（如果存在）
                    if 'sdx' in session:
                        sdx_data = session['sdx']
                        print(f"  ✅ SDX 数据: {sdx_data}")
                    
                    return True
                else:
                    print("❌ 没有找到包含 HTTP 数据的会话")
                    return False
                    
        except Exception as e:
            print(f"❌ 会话数据检查失败: {e}")
            return False
    
    def test_viewer_fields(self):
        """测试 Viewer 字段加载"""
        print("\n=== 测试 Viewer 字段加载 ===")
        
        try:
            # 测试字段 API
            req = urllib.request.Request(f"{self.viewer_url}/api/fields")
            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)
                
                if isinstance(data, list):
                    # 查找我们注册的字段
                    test_fields = ['host.http', 'http.method', 'http.uri', 'protocol']
                    found_fields = []
                    
                    for field in data:
                        if field.get('exp') in test_fields or field.get('dbField') in test_fields:
                            found_fields.append(field)
                    
                    print(f"✅ Viewer 加载了 {len(data)} 个字段")
                    print(f"✅ 找到 {len(found_fields)} 个测试字段:")
                    
                    for field in found_fields:
                        exp = field.get('exp', 'N/A')
                        dbField = field.get('dbField', 'N/A')
                        friendlyName = field.get('friendlyName', 'N/A')
                        print(f"  - {exp} (dbField: {dbField}, 名称: {friendlyName})")
                    
                    return len(found_fields) > 0
                else:
                    print(f"❌ Viewer 字段 API 返回格式错误: {type(data)}")
                    return False
                    
        except Exception as e:
            print(f"❌ Viewer 字段检查失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("Arkime 字段注册和显示功能测试")
        print("=" * 60)
        
        success = True
        
        # 测试字段注册
        success &= self.test_field_registration()
        
        # 测试会话数据
        success &= self.test_session_data()
        
        # 测试 Viewer 字段加载
        success &= self.test_viewer_fields()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ 所有测试通过！")
            print("\n字段注册和显示功能正常工作。")
            print("现在可以：")
            print("1. 访问 Arkime Web 界面: http://localhost:8005")
            print("2. 登录 (用户名: admin, 密码: admin)")
            print("3. 查看 Sessions 页面，info 列应该显示协议、HTTP 字段等信息")
            print("4. 在 SPI View 中查看字段统计")
        else:
            print("❌ 部分测试失败，请检查错误信息")
        
        return success


def main():
    tester = ArkimeFieldTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
