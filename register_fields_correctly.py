#!/usr/bin/env python3
"""
正确注册 SDX 和 HTTP 字段到 Arkime
"""

import json
import urllib.request
import urllib.parse
from datetime import datetime

def clear_es_data():
    """清除 ES 数据"""
    print("=== 清除 ES 数据 ===")
    
    # 删除字段索引
    try:
        req = urllib.request.Request("http://localhost:9200/arkime_fields_v30", method='DELETE')
        with urllib.request.urlopen(req) as response:
            print(f"删除字段索引: {response.status}")
    except Exception as e:
        print(f"删除字段索引失败: {e}")
    
    # 删除会话索引
    try:
        req = urllib.request.Request("http://localhost:9200/arkime_sessions3-*", method='DELETE')
        with urllib.request.urlopen(req) as response:
            print(f"删除会话索引: {response.status}")
    except Exception as e:
        print(f"删除会话索引失败: {e}")
    
    # 删除文件索引
    try:
        req = urllib.request.Request("http://localhost:9200/arkime_files_v30", method='DELETE')
        with urllib.request.urlopen(req) as response:
            print(f"删除文件索引: {response.status}")
    except Exception as e:
        print(f"删除文件索引失败: {e}")

def register_fields():
    """注册字段定义"""
    print("\n=== 注册字段定义 ===")
    
    # 完整的字段定义，包含所有必要属性
    fields = [
        # SDX 字段
        {
            "field_id": "sdx.linename1",
            "definition": {
                "friendlyName": "SDX Line Name 1",
                "group": "sdx",
                "help": "SDX protocol line name 1",
                "dbField": "sdx.linename1",
                "dbField2": "sdx.linename1", 
                "type": "termfield",
                "category": ["sdx"],
                "transform": "none"
            }
        },
        {
            "field_id": "sdx.linename2", 
            "definition": {
                "friendlyName": "SDX Line Name 2",
                "group": "sdx",
                "help": "SDX protocol line name 2",
                "dbField": "sdx.linename2",
                "dbField2": "sdx.linename2",
                "type": "termfield", 
                "category": ["sdx"],
                "transform": "none"
            }
        },
        # HTTP 字段
        {
            "field_id": "http.method",
            "definition": {
                "friendlyName": "HTTP Method",
                "group": "http",
                "help": "HTTP request method",
                "dbField": "http.method",
                "dbField2": "http.method",
                "type": "termfield",
                "category": ["http"],
                "transform": "none"
            }
        },
        {
            "field_id": "http.host",
            "definition": {
                "friendlyName": "HTTP Host",
                "group": "http", 
                "help": "HTTP host header",
                "dbField": "http.host",
                "dbField2": "http.host",
                "type": "termfield",
                "category": ["http", "host"],
                "transform": "removeProtocolAndURI"
            }
        },
        {
            "field_id": "http.uri",
            "definition": {
                "friendlyName": "HTTP URI",
                "group": "http",
                "help": "HTTP request URI",
                "dbField": "http.uri", 
                "dbField2": "http.uri",
                "type": "termfield",
                "category": ["http"],
                "transform": "removeProtocol"
            }
        },
        # 协议字段
        {
            "field_id": "protocols",
            "definition": {
                "friendlyName": "Protocols",
                "group": "general",
                "help": "Protocols used in session",
                "dbField": "protocols",
                "dbField2": "protocols",
                "type": "termfield",
                "category": ["protocol"],
                "transform": "none"
            }
        }
    ]
    
    # 构建 bulk 请求
    bulk_data = []
    for field in fields:
        # 索引操作
        index_op = {"index": {"_index": "arkime_fields_v30", "_id": field["field_id"]}}
        bulk_data.append(json.dumps(index_op))
        bulk_data.append(json.dumps(field["definition"]))
    
    bulk_body = "\n".join(bulk_data) + "\n"
    
    # 发送到 ES
    try:
        req = urllib.request.Request(
            "http://localhost:9200/_bulk",
            data=bulk_body.encode('utf-8'),
            headers={'Content-Type': 'application/x-ndjson'}
        )
        
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            data = json.loads(result)
            
            print(f"字段注册结果: {response.status}")
            if data.get('errors'):
                print("注册过程中有错误:")
                for item in data['items']:
                    if 'error' in item.get('index', {}):
                        print(f"  - {item['index']['error']}")
            else:
                print(f"成功注册 {len(fields)} 个字段")
                
    except Exception as e:
        print(f"字段注册失败: {e}")

def create_session_data():
    """创建会话数据"""
    print("\n=== 创建会话数据 ===")
    
    # 当前日期的索引
    date_str = datetime.now().strftime("%y%m%d")
    index_name = f"arkime_sessions3-{date_str}"
    
    # 会话数据
    session_data = {
        "@timestamp": int(datetime.now().timestamp() * 1000),
        "firstPacket": 1041342931300,
        "lastPacket": 1041342932300,
        "length": 1000,
        "ipProtocol": 6,
        "node": "localhost",
        "source": {
            "ip": "********",
            "port": 3267,
            "bytes": 207,
            "packets": 1,
            "mac": ["00:09:6b:88:f5:c9"]
        },
        "destination": {
            "ip": "*************", 
            "port": 80,
            "bytes": 0,
            "packets": 0,
            "mac": ["00:e0:81:00:b0:28"]
        },
        "network": {
            "packets": 1,
            "bytes": 207
        },
        "packetPos": [-1, 24],
        "fileId": [1],
        "protocols": ["tcp", "http", "sdx"],
        "http": {
            "method": ["HEAD"],
            "host": ["windowsupdate.microsoft.com"],
            "uri": ["/v4/iuident.cab"]
        },
        "sdx": {
            "linename1": "11111",
            "linename2": "22222"
        }
    }
    
    # 发送会话数据
    try:
        bulk_body = json.dumps({"index": {"_index": index_name}}) + "\n"
        bulk_body += json.dumps(session_data) + "\n"
        
        req = urllib.request.Request(
            "http://localhost:9200/_bulk",
            data=bulk_body.encode('utf-8'),
            headers={'Content-Type': 'application/x-ndjson'}
        )
        
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            data = json.loads(result)
            
            print(f"会话数据发送结果: {response.status}")
            if data.get('errors'):
                print("发送过程中有错误:")
                for item in data['items']:
                    if 'error' in item.get('index', {}):
                        print(f"  - {item['index']['error']}")
            else:
                print(f"成功创建会话数据到索引: {index_name}")
                
    except Exception as e:
        print(f"会话数据发送失败: {e}")

def create_file_data():
    """创建文件数据"""
    print("\n=== 创建文件数据 ===")
    
    file_data = {
        "num": 1,
        "name": "/opt/arkime/raw/test-http.pcap",
        "first": int(datetime.now().timestamp() * 1000),
        "node": "localhost",
        "filesize": 247,
        "locked": 0
    }
    
    try:
        req = urllib.request.Request(
            "http://localhost:9200/arkime_files_v30/_doc/localhost-1",
            data=json.dumps(file_data).encode('utf-8'),
            headers={'Content-Type': 'application/json'},
            method='PUT'
        )
        
        with urllib.request.urlopen(req) as response:
            print(f"文件数据发送结果: {response.status}")
            
    except Exception as e:
        print(f"文件数据发送失败: {e}")

def main():
    """主函数"""
    print("Arkime 字段正确注册工具")
    print("=" * 50)
    
    # 清除现有数据
    clear_es_data()
    
    # 等待 ES 处理删除操作
    import time
    time.sleep(2)
    
    # 注册字段
    register_fields()
    
    # 创建数据
    create_session_data()
    create_file_data()
    
    print("\n" + "=" * 50)
    print("✅ 字段注册完成！")
    print("请重启 Arkime Viewer 服务以加载新的字段定义:")
    print("  sudo systemctl restart arkimeviewer")
    print()
    print("然后访问 http://localhost:8005 查看效果")

if __name__ == "__main__":
    main()
