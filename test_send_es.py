import json
import urllib.request
import urllib.parse
import re
import os
from datetime import datetime
import time

def create_minimal_session():
    """根据解析的数据创建最小字段集的会话数据"""
    print("正在创建最小字段集会话数据...")

    # 使用当前时间戳而不是PCAP中的历史时间戳
    current_time = int(time.time() * 1000)
    timestamp = current_time

    # 创建最小字段集 - 支持前端所有功能的必需字段
    session = {
        # 核心时间字段 (必需)
        "@timestamp": 1752149929057,
        "firstPacket": 1041342931300,
        "lastPacket": 1041342931300 + 1000,  # 假设会话持续1秒
        "length": 1000,  # 会话持续时间(毫秒)

        # 网络基础信息 (必需)
        "ipProtocol": 6,  # 6=TCP
        "node": "localhost",  # 捕获节点名称

        # 源和目标信息 (必需)
        "source": {
            "ip": "********",
            "port": 3267,
            "bytes": 207,
            "packets": 1 ,
            "mac": [
                "00:09:6b:88:f5:c9"
            ]
        },
        "destination": {
            "ip": "*************",
            "port": 80,
            "bytes": 0,
            "packets": 0,
            "mac-cnt": 1,
            "mac": [
                "00:e0:81:00:b0:28"
            ]
        },

        # 网络统计 (必需)
        "network": {
            "packets": 1,
            "bytes": 207
        },
        # PCAP文件关联 (必需 - 用于Session Detail)
        "packetPos": [-1,24],  # 数据包在PCAP文件中的位置 (跳过24字节的PCAP文件头)
        "fileId": [1],     # 文件ID

        "protocolCnt": 2,
        "protocols": ["tcp", "http","sdx"],

        # 协议特定字段 (HTTP)
        "http": {
        "methodCnt": 1,
            "method": [
                "HEAD"
            ],
        "hostCnt": 1,
            "host": [
                "windowsupdate.microsoft.com"
            ],
        "method-HEAD": 1,
        "pathCnt": 1,
        "path": [
            "/v4/iuident.cab"
        ],
        "requestHeaderCnt": 4,
        "requestHeader": [
            "user-agent",
            "host",
            "connection",
            "accept"
        ],
    },
    "sdx":{
      "linename1" : "11111",
      "linename2" : "22222",
    }
    }

    print(f"创建的会话数据: {json.dumps(session, indent=2)}")
    return session

def send_session_to_es(session_data, es_url="http://localhost:9200"):
    """发送会话数据到 Elasticsearch"""
    print("正在发送会话数据到ES...")

    # 构建索引名称 - 使用当前日期
    from datetime import datetime
    date_str = datetime.now().strftime("%y%m%d")
    index_name = f"arkime_sessions3-{date_str}"

    print(f"目标索引: {index_name}")
    print(f"会话数据:")
    print(json.dumps(session_data, indent=2, ensure_ascii=False))

    # 构建 bulk 请求
    bulk_data = []

    # 添加索引操作
    index_op = {"index": {"_index": index_name}}
    bulk_data.append(json.dumps(index_op))
    bulk_data.append(json.dumps(session_data))

    # 发送请求
    bulk_body = '\n'.join(bulk_data) + '\n'

    print(f"Bulk请求体:")
    print(bulk_body)

    # 使用urllib发送请求
    try:
        url = f"{es_url}/_bulk"
        print(f"发送URL: {url}")
        print(f"发送方法: POST")
        print(f"发送头部: Content-Type: application/x-ndjson")

        req = urllib.request.Request(
            url,
            data=bulk_body.encode('utf-8'),
            headers={
                'Content-Type': 'application/x-ndjson',
                'User-Agent': 'custom-arkime-capture'
            },
            method='POST'
        )

        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            print(f"发送结果: {response.status}")
            print(f"ES响应: {result}")
            if response.status != 200:
                print(f"错误详情: {result}")
            return response.status == 200

    except Exception as e:
        print(f"发送失败: {e}")
        return False

def register_fields_to_es(es_url="http://localhost:9200"):
    """注册字段定义到ES"""
    print("正在注册字段定义到ES...")

    # 字段定义
    fields = [
        {
            "field_name": "sdx.linename1",
            "definition": {
                "friendlyName": "SDX Line Name 1",
                "group": "sdx",
                "help": "SDX line name 1",
                "dbField2": "sdx.linename1",
                "type": "termfield",
                "category": "sdx"
            }
        },
        {
            "field_name": "sdx.linename2",
            "definition": {
                "friendlyName": "SDX Line Name 2",
                "group": "sdx",
                "help": "SDX line name 2",
                "dbField2": "sdx.linename2",
                "type": "termfield",
                "category": "sdx"
            }
        },
        {
            "field_name": "mac.src",
            "definition": {
                "friendlyName": "Src MAC",
                "group": "general",
                "help": "Source ethernet mac addresses set for session",
                "dbField2": "source.mac",
                "type": "lotermfield",
                "category": "mac",
                "transform": "dash2Colon"
            }
        },
        {
            "field_name": "mac.dst",
            "definition": {
                "friendlyName": "Dst MAC",
                "group": "general",
                "help": "Destination ethernet mac addresses set for session",
                "dbField2": "destination.mac",
                "type": "lotermfield",
                "category": "mac",
                "transform": "dash2Colon"
            }
        }
    ]

    # 构建bulk请求
    bulk_data = []
    print("准备注册的字段:")
    for field in fields:
        print(f"  - {field['field_name']}: {field['definition']}")
        # 添加索引操作 - 使用正确的字段索引名称
        index_op = {"index": {"_index": "arkime_fields_v30", "_id": field["field_name"]}}
        bulk_data.append(json.dumps(index_op))
        bulk_data.append(json.dumps(field["definition"]))

    bulk_body = '\n'.join(bulk_data) + '\n'
    print(f"Bulk请求体:")
    print(bulk_body)

    try:
        url = f"{es_url}/_bulk"
        print(f"发送URL: {url}")
        print(f"发送方法: POST")
        print(f"发送头部: Content-Type: application/x-ndjson")

        req = urllib.request.Request(
            url,
            data=bulk_body.encode('utf-8'),
            headers={
                'Content-Type': 'application/x-ndjson',
                'User-Agent': 'custom-arkime-capture'
            },
            method='POST'
        )

        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            print(f"字段注册结果: {response.status}")
            print(f"ES响应: {result}")
            if response.status != 200:
                print(f"错误详情: {result}")
            return response.status == 200

    except Exception as e:
        print(f"字段注册失败: {e}")
        return False

def register_node_config_to_es(es_url="http://localhost:9200"):
    """注册节点配置到ES（实际上节点配置通过配置文件管理，这里仅作演示）"""
    print("正在注册节点配置到ES...")
    print("注意：Arkime的节点配置实际上通过配置文件管理，不是通过ES")

    # 节点配置信息（仅作演示）
    node_config = {
        "localhost": {
            "viewUrl": "http://localhost:8005",
            "hostname": "localhost.localdomain",
            "description": "测试节点配置"
        }
    }

    print(f"准备发送的节点配置数据:")
    print(json.dumps(node_config, indent=2, ensure_ascii=False))

    try:
        # 发送到arkime_config索引（仅作演示）
        data = json.dumps(node_config).encode('utf-8')
        req = urllib.request.Request(
            f"{es_url}/arkime_config/_doc/nodes",
            data=data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )

        print(f"发送URL: {es_url}/arkime_config/_doc/nodes")
        print(f"发送方法: POST")
        print(f"发送头部: Content-Type: application/json")

        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            print(f"节点配置注册结果: {response.status}")
            print(f"ES响应: {result}")
            return response.status in [200, 201]

    except Exception as e:
        print(f"节点配置注册失败: {e}")
        return False

def send_file_info_to_es(pcap_file, es_url="http://localhost:9200"):
    """发送PCAP文件信息到ES"""
    print("正在发送文件信息到ES...")

    # 使用Arkime可以访问的路径
    arkime_pcap_path = "/opt/arkime/raw/test-http.pcap"

    file_info = {
        "num": 1,
        "name": arkime_pcap_path,
        "first": int(time.time() * 1000),
        "node": "localhost",
        "filesize": os.path.getsize(arkime_pcap_path) if os.path.exists(arkime_pcap_path) else 247,
        "locked": 0
    }

    print(f"准备发送的文件信息:")
    print(json.dumps(file_info, indent=2, ensure_ascii=False))
    print(f"PCAP文件路径: {arkime_pcap_path}")
    print(f"文件是否存在: {os.path.exists(arkime_pcap_path)}")
    if os.path.exists(arkime_pcap_path):
        print(f"文件大小: {os.path.getsize(arkime_pcap_path)} bytes")

    # 发送到arkime_files_v30索引
    try:
        data = json.dumps(file_info).encode('utf-8')
        url = f"{es_url}/arkime_files_v30/_doc/localhost-1"

        print(f"发送URL: {url}")
        print(f"发送方法: PUT")
        print(f"发送头部: Content-Type: application/json")
        print(f"发送数据: {data.decode('utf-8')}")

        req = urllib.request.Request(
            url,
            data=data,
            headers={'Content-Type': 'application/json'},
            method='PUT'  # 使用PUT确保ID为1
        )

        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            print(f"文件信息发送结果: {response.status}")
            print(f"ES响应: {result}")
            return response.status in [200, 201]

    except Exception as e:
        print(f"文件信息发送失败: {e}")
        return False

def main():
    """主函数 - 执行完整的测试流程"""
    print("=== Arkime ES最小字段测试 ===\n")

    es_url = "http://localhost:9200"
    http_txt_file = "pcap/http.txt"
    pcap_file = "pcap/http.pcap"

    # 1. 清空ES数据
    # clear_arkime_data(es_url)

    # 3. 注册字段定义到ES
    register_fields_to_es(es_url)

    # 5. 创建最小字段集会话数据
    session_data = create_minimal_session()

    # 6. 发送文件信息到ES
    send_file_info_to_es(pcap_file, es_url)

    # 7. 发送会话数据到ES
    success = send_session_to_es(session_data, es_url)

    if success:
        print("\n=== 测试完成 ===")
        print("数据已成功发送到ES，可以在Arkime前端查看")
        print(f"索引名称: arkime_sessions3-{datetime.fromtimestamp(session_data['lastPacket']/1000).strftime('%Y%m%d')}")
    else:
        print("\n=== 测试失败 ===")
        print("数据发送失败，请检查ES连接和配置")

if __name__ == "__main__":
    main()
