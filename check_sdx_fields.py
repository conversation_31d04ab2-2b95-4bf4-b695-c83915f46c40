#!/usr/bin/env python3
"""
检查 SDX 字段是否正确注册和显示的脚本
"""

import json
import urllib.request
import urllib.parse

def check_es_fields():
    """检查 ES 中的字段定义"""
    print("=== 检查 ES 中的 SDX 字段定义 ===")
    
    try:
        url = "http://localhost:9200/arkime_fields_v30/_search?q=group:sdx&pretty"
        req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            data = json.loads(result)
            
            print(f"找到 {data['hits']['total']['value']} 个 SDX 字段:")
            for hit in data['hits']['hits']:
                field_id = hit['_id']
                source = hit['_source']
                print(f"  - ID: {field_id}")
                print(f"    friendlyName: {source['friendlyName']}")
                print(f"    group: {source['group']}")
                print(f"    dbField2: {source['dbField2']}")
                print(f"    type: {source['type']}")
                print()
                
    except Exception as e:
        print(f"检查 ES 字段失败: {e}")

def check_session_data():
    """检查会话数据中的 SDX 字段"""
    print("=== 检查会话数据中的 SDX 字段 ===")

    try:
        # 使用当前日期的索引
        from datetime import datetime
        date_str = datetime.now().strftime("%y%m%d")
        url = f"http://localhost:9200/arkime_sessions3-{date_str}/_search?q=protocols:sdx&pretty"
        req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            data = json.loads(result)
            
            print(f"找到 {data['hits']['total']['value']} 个包含 SDX 协议的会话:")
            for hit in data['hits']['hits']:
                source = hit['_source']
                if 'sdx' in source:
                    print(f"  会话 ID: {hit['_id']}")
                    print(f"  SDX 数据: {json.dumps(source['sdx'], indent=4)}")
                    print(f"  协议列表: {source.get('protocols', [])}")
                    print()
                
    except Exception as e:
        print(f"检查会话数据失败: {e}")

def check_arkime_api():
    """尝试检查 Arkime API（可能需要认证）"""
    print("=== 尝试检查 Arkime API ===")
    
    try:
        # 尝试获取字段列表
        url = "http://localhost:8005/api/fields?array=true"
        req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            fields = json.loads(result)
            
            sdx_fields = [f for f in fields if f.get('group') == 'sdx']
            print(f"Arkime API 中找到 {len(sdx_fields)} 个 SDX 字段:")
            for field in sdx_fields:
                print(f"  - {field.get('exp', 'unknown')}: {field.get('friendlyName', 'unknown')}")
                
    except urllib.error.HTTPError as e:
        if e.code == 401:
            print("Arkime API 需要认证，无法直接访问")
        else:
            print(f"访问 Arkime API 失败: HTTP {e.code}")
    except Exception as e:
        print(f"检查 Arkime API 失败: {e}")

def main():
    """主函数"""
    print("SDX 字段检查工具")
    print("=" * 50)
    
    check_es_fields()
    check_session_data()
    check_arkime_api()
    
    print("=" * 50)
    print("检查完成！")
    print()
    print("如果字段已正确注册但前端不显示，可能的原因：")
    print("1. 需要重启 Arkime Viewer 服务")
    print("2. 需要在 SPI View 页面手动添加 SDX 字段")
    print("3. 需要清除浏览器缓存")
    print("4. 字段定义格式不正确")

if __name__ == "__main__":
    main()
