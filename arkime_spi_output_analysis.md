# Arkime Capture 模块 SPI 输出分析文档

## 1. 概述

Arkime capture 模块通过 SPI (Session Processing Interface) 将网络流量数据处理后输出到 Elasticsearch。本文档详细分析了从数据包捕获到 ES 存储的完整流程、调用关系和数据结构。

## 2. 整体架构流程

### 2.1 主要组件

- **数据包读取器**: 支持多种数据源（pcap、pfring、snf、tzsp等）
- **数据包批处理**: 高效的批量处理机制
- **会话管理**: 基于五元组的会话跟踪
- **协议解析器**: 多层协议解析和字段提取
- **写入器**: PCAP文件存储
- **数据库模块**: ES文档构建和批量发送

### 2.2 数据流转过程

```
数据包捕获 → 批处理队列 → 数据包线程 → 会话管理 → 协议解析 → SPI保存 → ES存储
```

## 3. 核心调用流程

### 3.1 主要调用链

```
g_main_loop_run()
├── 数据包读取器
│   ├── arkime_packet_batch()
│   └── arkime_packet_batch_flush()
├── arkime_packet_thread()
│   ├── arkime_packet_process()
│   │   ├── mProtocols.createSessionId()
│   │   ├── arkime_session_find_or_create()
│   │   ├── mProtocols.preProcess()
│   │   ├── arkime_writer_write()
│   │   ├── 协议解析器调用
│   │   └── mProtocols.process()
│   └── arkime_session_process_commands()
└── 会话保存流程
    ├── arkime_session_mid_save()
    ├── arkime_session_save()
    ├── arkime_db_save_session()
    ├── JSON序列化
    ├── arkime_db_send_bulk()
    └── HTTP POST 到 Elasticsearch
```

### 3.2 数据包处理流程

1. **arkime_packet_batch()**: 将数据包加入批处理队列
2. **arkime_packet_batch_flush()**: 刷新批处理队列到处理线程
3. **arkime_packet_process()**: 在专用线程中处理数据包
4. **arkime_session_find_or_create()**: 根据五元组查找或创建会话
5. **arkime_writer_write()**: 写入PCAP文件
6. **协议解析器调用**: 提取应用层信息
7. **arkime_db_save_session()**: 保存会话数据到ES

### 3.3 会话保存触发条件

- 会话正常结束（TCP FIN/RST）
- 会话超时
- 中期保存（mid-save）
- 手动刷新

## 4. 传递给 Elasticsearch 的数据结构

### 4.1 索引结构

```json
{
  "_index": "arkime_sessions3-YYYYMMDD",
  "_type": "_doc", 
  "_id": "session_id"
}
```

### 4.2 文档结构（遵循ECS标准）

#### 基础时间信息
```json
{
  "@timestamp": 1625097600000,           // 当前时间戳 (毫秒)
  "firstPacket": 1625097600000,          // 第一个数据包时间戳
  "lastPacket": 1625097610000,           // 最后一个数据包时间戳
  "length": 10000                        // 会话持续时间 (毫秒)
}
```

#### 网络层信息
```json
{
  "ipProtocol": 6,                       // IP协议号 (6=TCP, 17=UDP, 1=ICMP)
  "ethertype": 2048                      // 以太网类型 (可选)
}
```

#### 源地址信息 (ECS格式)
```json
{
  "source": {
    "ip": "*************",               // 源IP地址
    "port": 12345,                       // 源端口
    "bytes": 1024,                       // 源发送字节数
    "packets": 10,                       // 源发送包数
    "geo": {                             // 地理位置信息
      "country_iso_code": "US"
    },
    "as": {                              // AS信息
      "number": 15169,
      "full": "AS15169 Google LLC",
      "organization": {
        "name": "Google LLC"
      }
    },
    "mac": ["aa:bb:cc:dd:ee:ff"]         // MAC地址
  }
}
```

#### 目标地址信息 (ECS格式)
```json
{
  "destination": {
    "ip": "********",                    // 目标IP地址
    "port": 80,                          // 目标端口
    "bytes": 2048,                       // 目标发送字节数
    "packets": 8,                        // 目标发送包数
    "geo": {                             // 地理位置信息
      "country_iso_code": "CN"
    },
    "as": {                              // AS信息
      "number": 4134,
      "full": "AS4134 China Telecom",
      "organization": {
        "name": "China Telecom"
      }
    },
    "mac": ["ff:ee:dd:cc:bb:aa"]         // MAC地址
  }
}
```

#### 网络统计信息 (ECS格式)
```json
{
  "network": {
    "packets": 18,                       // 总包数
    "bytes": 3072,                       // 总字节数
    "community_id": "1:hash_value",      // 社区ID哈希
    "vlan": {                            // VLAN信息
      "id-cnt": 1,
      "id": [100]
    }
  }
}
```

#### 客户端/服务器统计
```json
{
  "client": {
    "bytes": 512                         // 客户端数据字节数
  },
  "server": {
    "bytes": 1024                        // 服务器数据字节数
  }
}
```

#### TCP特定信息 (仅TCP协议)
```json
{
  "tcpflags": {
    "syn": 1,                            // SYN标志计数
    "syn-ack": 1,                        // SYN-ACK标志计数
    "ack": 15,                           // ACK标志计数
    "psh": 8,                            // PSH标志计数
    "fin": 2,                            // FIN标志计数
    "rst": 0,                            // RST标志计数
    "urg": 0,                            // URG标志计数
    "srcZero": 0,                        // 源零窗口计数
    "dstZero": 0                         // 目标零窗口计数
  },
  "initRTT": 25,                         // 初始RTT (毫秒)
  "tcpseq": {                            // TCP序列号
    "src": 1234567890,
    "dst": 9876543210
  }
}
```

#### 载荷信息
```json
{
  "srcPayload8": "474554202f20485454",   // 源方向前8字节载荷 (十六进制)
  "dstPayload8": "485454502f312e3120"    // 目标方向前8字节载荷 (十六进制)
}
```

#### 元数据
```json
{
  "totDataBytes": 1536,                  // 总数据字节数
  "segmentCnt": 1,                       // 会话段数
  "node": "arkime-node-01",              // 捕获节点名称
  "rootId": "session_root_id"            // 根会话ID
}
```

#### 数据包位置信息
```json
{
  "packetPos": [0, 1500, 3000, 4500],    // 数据包在PCAP文件中的位置
  "packetLen": [66, 1514, 1514, 128],    // 数据包长度数组
  "fileId": [1, 1, 1, 1]                // 文件编号数组
}
```

#### RIR信息
```json
{
  "srcRIR": "ARIN",                      // 源IP的RIR
  "dstRIR": "APNIC"                      // 目标IP的RIR
}
```

#### 协议特定字段

```json
{
  "protocols": ["tcp", "http"],          // 检测到的协议列表
  "tags": ["tag1", "tag2"]               // 标签列表
}
```

### 4.3 协议解析器添加的字段示例

#### HTTP字段

```json
{
  "http": {
    "method": ["GET"],
    "uri": ["/index.html"],
    "host": ["example.com"],
    "user-agent": ["Mozilla/5.0..."],
    "statuscode": [200],
    "version": ["1.1"]
  }
}
```

#### DNS字段

```json
{
  "dns": {
    "host": ["example.com"],
    "opcode": [0],
    "qc": [1],
    "ac": [1]
  }
}
```

#### TLS字段

```json
{
  "tls": {
    "version": ["1.2"],
    "cipher": ["TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"],
    "ja3": ["hash_value"],
    "ja3s": ["hash_value"]
  }
}
```

## 5. 关键代码分析

### 5.1 会话保存函数

```c
void arkime_db_save_session(ArkimeSession_t *session, int final)
{
    // 插件回调处理
    if (pluginsCbs & ARKIME_PLUGIN_SAVE)
        arkime_plugins_cb_save(session, final);

    // 检查是否停止SPI数据保存
    if (session->stopSPI)
        return;

    // 估算JSON大小
    jsonSize = 1300 + session->filePosArray->len * 17 + 11 * session->fileNumArray->len;

    // 构建JSON文档
    BSB_EXPORT_sprintf(jbsb,
                       "{\"firstPacket\":%" PRIu64 ","
                       "\"lastPacket\":%" PRIu64 ","
                       "\"length\":%u,"
                       "\"ipProtocol\":%u,",
                       ((uint64_t)session->firstPacket.tv_sec) * 1000,
                       ((uint64_t)session->lastPacket.tv_sec) * 1000,
                       timediff,
                       session->ipProtocol);
}
```

### 5.2 批量发送机制

```c
LOCAL void arkime_db_send_bulk(char *json, int len)
{
    if (config.debug > 4)
        LOG("Sending Bulk:>%.*s<", len, json);
    arkime_http_schedule(esServer, "POST", esBulkQuery, esBulkQueryLen,
                        json, len, NULL, ARKIME_HTTP_PRIORITY_NORMAL,
                        arkime_db_send_bulk_cb, NULL);
}
```

## 6. 性能优化机制

### 6.1 批量处理

- 数据包批处理队列减少线程切换开销
- ES批量API减少HTTP请求次数
- JSON缓冲区复用避免频繁内存分配

### 6.2 多线程架构

- 独立的数据包处理线程
- 异步的ES写入
- 线程间队列通信

### 6.3 内存管理

- 对象池减少内存分配
- 延迟释放机制
- 缓冲区预分配

## 7. 配置参数

### 7.1 ES相关配置

- `elasticsearch`: ES服务器地址
- `dbBulkSize`: 批量请求大小
- `maxESConns`: 最大ES连接数
- `maxESRequests`: 最大ES请求数

### 7.2 性能调优参数

- `packetThreads`: 数据包处理线程数
- `maxPackets`: 最大数据包队列长度
- `dbFlushTimeout`: 数据库刷新超时

## 8. 监控和调试

### 8.1 统计信息

- 数据包处理速率
- 会话创建/销毁统计
- ES写入成功/失败率
- 队列长度监控

### 8.2 调试选项

- `debug`: 调试级别设置
- 详细的日志输出
- 性能计数器

## 9. 调用图

### 9.1 主要调用流程图

```mermaid
graph TD
    A[主循环 g_main_loop_run] --> B[数据包读取器]
    B --> C[arkime_packet_batch]
    C --> D[arkime_packet_batch_flush]
    D --> E[数据包队列 packetQ]
    E --> F[arkime_packet_thread]
    F --> G[arkime_packet_process]

    G --> H[mProtocols.createSessionId]
    G --> I[arkime_session_find_or_create]
    G --> J[mProtocols.preProcess]
    G --> K[arkime_writer_write]
    G --> L[arkime_parsers_process]
    G --> M[mProtocols.process]

    I --> N[Session 哈希表查找/创建]
    N --> O[新会话初始化]
    O --> P[arkime_plugins_cb_new]

    K --> Q[Writer 模块]
    Q --> R[writer_simple_write]
    Q --> S[writer_s3_write]
    Q --> T[writer_null_write]

    L --> U[协议解析器]
    U --> V[HTTP/DNS/TLS 等解析]
    V --> W[字段提取和标记]

    W --> X[Session 数据累积]
    X --> Y[arkime_session_mid_save]
    X --> Z[arkime_session_save]

    Y --> AA[arkime_db_save_session]
    Z --> AA
    AA --> BB[JSON 序列化]
    BB --> CC[Bulk API 缓冲]
    CC --> DD[arkime_db_send_bulk]
    DD --> EE[HTTP POST 到 Elasticsearch]
```

### 9.2 数据流转时序图

```mermaid
sequenceDiagram
    participant Reader as 数据包读取器
    participant Batch as 批处理队列
    participant Thread as 数据包线程
    participant Session as 会话管理
    participant Parser as 协议解析器
    participant Writer as 写入器
    participant DB as 数据库模块
    participant ES as Elasticsearch

    Reader->>Batch: arkime_packet_batch()
    Note over Batch: 数据包缓存到队列

    Batch->>Thread: arkime_packet_batch_flush()
    Note over Thread: 唤醒处理线程

    Thread->>Thread: arkime_packet_process()
    Note over Thread: 解析IP/TCP/UDP头

    Thread->>Session: arkime_session_find_or_create()
    Note over Session: 根据五元组查找/创建会话

    Thread->>Writer: arkime_writer_write()
    Note over Writer: 写入PCAP文件

    Thread->>Parser: 协议解析器调用
    Note over Parser: HTTP/DNS/TLS等解析

    Parser->>Session: 添加字段和标签
    Note over Session: 累积会话数据

    Session->>DB: arkime_db_save_session()
    Note over DB: 会话结束或中期保存

    DB->>DB: JSON序列化
    Note over DB: 构建ES文档结构

    DB->>DB: 批量缓冲
    Note over DB: 累积到bulk缓冲区

    DB->>ES: arkime_db_send_bulk()
    Note over ES: HTTP POST /_bulk API

    ES-->>DB: 响应确认
    Note over DB: 处理响应和错误
```

## 10. 总结

Arkime capture 模块通过高效的多线程架构和批量处理机制，将网络流量数据实时转换为结构化的ES文档。其SPI输出遵循ECS标准，支持丰富的网络分析和安全监控功能。关键特性包括：

1. **高性能**: 多线程并行处理，批量API优化
2. **可扩展**: 插件化的协议解析器架构
3. **标准化**: 遵循ECS标准的数据格式
4. **可靠性**: 完善的错误处理和监控机制

该架构设计确保了 Arkime 能够在高流量环境下稳定运行，同时提供丰富的网络可视性和分析能力。

## 11. 自定义 Capture 模块实现指南

如果要替换 Arkime 的 capture 模块为自己实现的版本，需要向 Elasticsearch 发送符合 Arkime 期望格式的数据。以下是详细的实现指南。

### 11.1 Elasticsearch 索引和文档格式

#### 索引命名规范

```text
{prefix}sessions3-{YYYYMMDD}
```

- `prefix`: 配置的前缀，默认为 `arkime_`
- `sessions3`: 固定的会话索引名称
- `YYYYMMDD`: 基于会话最后一个数据包时间的日期

示例：`arkime_sessions3-20231208`

#### Bulk API 格式

Arkime 使用 Elasticsearch 的 Bulk API 批量写入数据：

```json
{"index":{"_index":"arkime_sessions3-20231208","_id":"session_id"}}
{session_document}
{"index":{"_index":"arkime_sessions3-20231208","_id":"session_id2"}}
{session_document2}
```

### 11.2 必需字段 (Mandatory Fields)

以下字段是 Arkime viewer 正常工作的最低要求：

#### 核心时间字段

```json
{
  "@timestamp": 1625097600000,           // 必需：当前时间戳 (毫秒)
  "firstPacket": 1625097600000,          // 必需：第一个数据包时间戳 (毫秒)
  "lastPacket": 1625097610000,           // 必需：最后一个数据包时间戳 (毫秒)
  "length": 10000                        // 必需：会话持续时间 (毫秒)
}
```

#### 网络基础信息

```json
{
  "ipProtocol": 6,                       // 必需：IP协议号 (6=TCP, 17=UDP, 1=ICMP)
  "node": "capture-node-01"              // 必需：捕获节点名称
}
```

#### 源和目标信息 (ECS 格式)

```json
{
  "source": {
    "ip": "*************",               // 必需：源IP地址
    "port": 12345,                       // 必需：源端口 (TCP/UDP)
    "bytes": 1024,                       // 必需：源发送字节数
    "packets": 10                        // 必需：源发送包数
  },
  "destination": {
    "ip": "********",                    // 必需：目标IP地址
    "port": 80,                          // 必需：目标端口 (TCP/UDP)
    "bytes": 2048,                       // 必需：目标发送字节数
    "packets": 8                         // 必需：目标发送包数
  }
}
```

#### 网络统计信息

```json
{
  "network": {
    "packets": 18,                       // 必需：总包数
    "bytes": 3072                        // 必需：总字节数
  }
}
```

#### 数据包位置信息 (必需)

```json
{
  "packetPos": [0, 1500, 3000, 4500],    // 必需：数据包在PCAP文件中的位置
  "fileId": [1, 1, 1, 1]                // 必需：文件编号数组
}
```

### 11.3 推荐字段 (Recommended Fields)

#### 客户端/服务器统计 (推荐)

```json
{
  "client": {
    "bytes": 512                         // 推荐：客户端数据字节数
  },
  "server": {
    "bytes": 1024                        // 推荐：服务器数据字节数
  },
  "totDataBytes": 1536,                  // 推荐：总数据字节数
  "segmentCnt": 1                        // 推荐：会话段数
}
```

#### 地理位置和AS信息

```json
{
  "source": {
    "geo": {
      "country_iso_code": "US"           // 推荐：源IP地理位置
    },
    "as": {
      "number": 15169,                   // 推荐：AS号码
      "full": "AS15169 Google LLC"       // 推荐：完整AS信息
    }
  },
  "destination": {
    "geo": {
      "country_iso_code": "CN"           // 推荐：目标IP地理位置
    },
    "as": {
      "number": 4134,                    // 推荐：AS号码
      "full": "AS4134 China Telecom"    // 推荐：完整AS信息
    }
  }
}
```

#### 社区ID

```json
{
  "network": {
    "community_id": "1:hash_value"       // 推荐：网络社区ID
  }
}
```

### 11.4 协议特定字段

#### TCP 协议字段

```json
{
  "tcpflags": {
    "syn": 1,                            // TCP SYN标志计数
    "syn-ack": 1,                        // TCP SYN-ACK标志计数
    "ack": 15,                           // TCP ACK标志计数
    "psh": 8,                            // TCP PSH标志计数
    "fin": 2,                            // TCP FIN标志计数
    "rst": 0                             // TCP RST标志计数
  },
  "initRTT": 25                          // 初始RTT (毫秒)
}
```

#### 载荷信息 (可选)

```json
{
  "srcPayload8": "474554202f20485454",   // 源方向前8字节载荷 (十六进制)
  "dstPayload8": "485454502f312e3120"    // 目标方向前8字节载荷 (十六进制)
}
```

### 11.5 HTTP 请求格式

#### 请求头

```http
POST /_bulk HTTP/1.1
Host: elasticsearch:9200
Content-Type: application/x-ndjson
User-Agent: arkime
Content-Length: {content_length}
```

#### 请求体示例

```json
{
  "index": {
    "_index": "arkime_sessions3-20231208",
    "_id": "20231208-node01-12345"
  }
}
{
  "@timestamp": 1625097600000,
  "firstPacket": 1625097600000,
  "lastPacket": 1625097610000,
  "length": 10000,
  "ipProtocol": 6,
  "source": {
    "ip": "*************",
    "port": 12345,
    "bytes": 1024,
    "packets": 10
  },
  "destination": {
    "ip": "********",
    "port": 80,
    "bytes": 2048,
    "packets": 8
  },
  "network": {
    "packets": 18,
    "bytes": 3072
  },
  "node": "capture-node-01",
  "packetPos": [0,1500,3000,4500],
  "fileId": [1,1,1,1]
}
```

### 11.6 文档ID生成策略

Arkime 支持多种文档ID生成策略：

#### 自动生成 (autoGenerateId=1)

```json
{"index":{"_index":"arkime_sessions3-20231208"}}
```

ES 自动生成文档ID。

#### 基于位置生成 (autoGenerateId=2)

```json
{"index":{"_index":"arkime_sessions3-20231208","_id":"20231208-node01-1-1500"}}
```

格式：`{date}-{node}-{fileId}-{packetPos}`

#### 自定义ID (autoGenerateId=0)

```json
{"index":{"_index":"arkime_sessions3-20231208","_id":"custom-session-id"}}
```

使用自定义的会话ID。

### 11.7 字段类型要求

| 字段名 | 类型 | 说明 |
|--------|------|------|
| @timestamp | long | Unix时间戳 (毫秒) |
| firstPacket | long | Unix时间戳 (毫秒) |
| lastPacket | long | Unix时间戳 (毫秒) |
| length | integer | 会话持续时间 (毫秒) |
| ipProtocol | integer | IP协议号 |
| source.ip | ip | IPv4/IPv6地址 |
| source.port | integer | 端口号 |
| source.bytes | long | 字节数 |
| source.packets | integer | 包数 |
| destination.* | 同source | 目标信息 |
| network.packets | integer | 总包数 |
| network.bytes | long | 总字节数 |
| node | keyword | 节点名称 |
| packetPos | long[] | 数据包位置数组 |
| fileId | integer[] | 文件编号数组 |

### 11.8 实现示例

#### Python 实现示例

```python
import json
import requests
from datetime import datetime

def send_session_to_es(session_data, es_url="http://localhost:9200"):
    """发送会话数据到 Elasticsearch"""

    # 构建索引名称
    date_str = datetime.fromtimestamp(session_data['lastPacket']/1000).strftime('%Y%m%d')
    index_name = f"arkime_sessions3-{date_str}"

    # 构建 bulk 请求
    bulk_data = []

    # 添加索引操作
    index_op = {"index": {"_index": index_name}}
    bulk_data.append(json.dumps(index_op))
    bulk_data.append(json.dumps(session_data))

    # 发送请求
    bulk_body = '\n'.join(bulk_data) + '\n'

    headers = {
        'Content-Type': 'application/x-ndjson',
        'User-Agent': 'custom-arkime-capture'
    }

    response = requests.post(
        f"{es_url}/_bulk",
        data=bulk_body,
        headers=headers
    )

    return response.status_code == 200

# 使用示例
session = {
    "@timestamp": 1625097600000,
    "firstPacket": 1625097600000,
    "lastPacket": 1625097610000,
    "length": 10000,
    "ipProtocol": 6,
    "source": {
        "ip": "*************",
        "port": 12345,
        "bytes": 1024,
        "packets": 10
    },
    "destination": {
        "ip": "********",
        "port": 80,
        "bytes": 2048,
        "packets": 8
    },
    "network": {
        "packets": 18,
        "bytes": 3072
    },
    "node": "custom-capture-01",
    "packetPos": [0, 1500, 3000, 4500],
    "fileId": [1, 1, 1, 1],
}

send_session_to_es(session)
```

### 11.9 验证和测试

#### 验证必需字段

确保所有必需字段都存在且类型正确：

```bash
# 检查文档结构
curl -X GET "localhost:9200/arkime_sessions3-20231208/_search?pretty" \
  -H 'Content-Type: application/json' \
  -d '{"size": 1}'
```

#### 测试 Viewer 兼容性

1. 确保 Arkime viewer 能正常显示会话列表
2. 验证会话详情页面能正确加载
3. 测试搜索和过滤功能
4. 检查 SPI 图表是否正常工作

### 11.10 注意事项

1. **时间戳精度**: 所有时间戳必须是毫秒级别的 Unix 时间戳
2. **IP地址格式**: 支持 IPv4 和 IPv6，使用标准格式
3. **数组字段**: packetPos 和 fileId 必须是数组，即使只有一个元素
4. **字段命名**: 严格遵循 ECS 标准的字段命名规范
5. **批量大小**: 建议每次批量请求不超过 1000 个文档
6. **错误处理**: 实现适当的重试机制和错误处理逻辑

通过遵循以上规范，自定义的 capture 模块可以与 Arkime 的 viewer 和其他组件完全兼容。

## 12. Session Detail 页面数据处理流程分析

当用户在 Arkime 页面上点击 detail 按钮时，会触发一系列复杂的数据处理流程。以下详细分析这个过程中发生的事情。

### 12.1 点击 Detail 按钮的触发流程

#### 前端触发过程

1. **用户点击 Detail 按钮**
   - 前端 Vue 组件触发 `getDetailData()` 方法
   - 发起 AJAX 请求到 `/api/session/{nodeName}/{id}/detail`

2. **Session Detail 组件初始化**
   - 加载会话元数据（从 ES 获取）
   - 初始化数据包显示参数
   - 准备数据包解码器

#### 后端处理流程

```javascript
// 主要处理函数调用链
SessionAPIs.getDetail()
├── SessionAPIs.#localSessionDetail()
├── SessionAPIs.processSessionId()
├── SessionAPIs.#processSessionIdDisk()
└── pcap.decode() // 实时解码
```

### 12.2 数据包重新解析过程

**重要发现：是的，会发生重新解析！**

当点击 detail 按钮时，Arkime 会：

1. **从 ES 获取会话元数据**
   - 包括 `packetPos` 数组（数据包在 PCAP 文件中的位置）
   - 包括 `fileId` 数组（对应的文件编号）
   - 包括已解析的协议字段（如 HTTP headers、DNS 查询等）

2. **实时读取和解析 PCAP 数据包**
   - 根据 `packetPos` 从 PCAP 文件中读取原始数据包
   - 对每个数据包调用 `pcap.decode()` 进行实时解析
   - 重新组装 TCP 流数据

### 12.3 HTTP ASCII 信息的生成时机

#### Capture 阶段的解析

在 capture 模块中，HTTP 解析器会：

```c
// HTTP 解析器在 capture 时的工作
LOCAL int http_parse(ArkimeSession_t *session, void *uw, const uint8_t *data, int remaining, int which)
{
    // 解析 HTTP 头部和元数据
    http_parser_execute(&http->parsers[http->which], &parserSettings, (char *)data, remaining);

    // 提取字段信息存储到 ES
    arkime_field_string_add(methodField, session, http_method_str(parser->method), -1, TRUE);
    arkime_field_string_add(hostField, session, host, hostLen, TRUE);
    // ... 其他字段
}
```

**Capture 阶段存储的信息：**

- HTTP 方法、状态码、Host、User-Agent 等头部字段
- URL 路径
- 内容类型
- 但**不存储完整的 HTTP 载荷内容**

#### Viewer 阶段的重新解析

当显示 detail 页面时：

```javascript
// 实时解码过程
SessionAPIs.processSessionId(req.params.id, !req.packetsOnly, null, (pcap, buffer, cb, i) => {
  let obj = {};
  if (buffer.length > 16) {
    try {
      pcap.decode(buffer, obj);  // 重新解码数据包
    } catch (e) {
      obj = { ip: { p: 'Error decoding' + e } };
    }
  }
  packets[i] = obj;
  cb(null);
}, (err, session) => {
  // 处理解码后的数据包
  if (req.query.base === 'ascii') {
    // 生成 ASCII 显示
  }
});
```

**Viewer 阶段重新生成的信息：**

- 完整的数据包内容（十六进制、ASCII、UTF-8 等格式）
- TCP 流重组
- HTTP 请求/响应的完整载荷
- 实时的协议解析和显示

### 12.4 数据处理的两个阶段对比

| 阶段 | Capture 模块 | Viewer 模块 |
|------|-------------|-------------|
| **时机** | 数据包捕获时 | 用户查看详情时 |
| **目的** | 提取元数据用于搜索和统计 | 提供完整的数据包内容显示 |
| **存储** | 字段信息存储到 ES | 不存储，实时生成 |
| **HTTP 解析** | 提取头部字段和元数据 | 重新解析完整的 HTTP 流 |
| **性能考虑** | 高效提取关键信息 | 按需解析，支持多种显示格式 |

### 12.5 详细的数据包处理流程

#### 步骤 1: 获取会话信息

```javascript
// 从 ES 获取会话元数据
const session = await Db.getWithOptions(Db.sid2Index(id), 'session', Db.sid2Id(id), options);
```

#### 步骤 2: 读取 PCAP 文件

```javascript
// 根据 packetPos 读取数据包
function processFile(pcap, pos, i, nextCb) {
  pcap.readPacket(pos, (packet) => {
    // packet 是从 PCAP 文件读取的原始数据
    packetCb(pcap, packet, nextCb, i);
  });
}
```

#### 步骤 3: 实时解码

```javascript
// 对每个数据包进行解码
pcap.decode(buffer, obj);
// obj 现在包含解析后的协议信息
```

#### 步骤 4: 流重组和显示

```javascript
// TCP 流重组
if (req.query.showFrames) {
  Pcap.packetFlow(session, packets, +req.query.packets || 200, callback);
} else {
  Pcap.reassemble_generic_ether(packets, +req.query.packets || 200, callback);
}
```

### 12.6 HTTP ASCII 显示的生成

HTTP ASCII 信息是在 viewer 阶段实时生成的：

1. **数据包读取**: 从 PCAP 文件读取原始数据包
2. **协议解析**: 重新解析 TCP/HTTP 协议栈
3. **流重组**: 将分片的 TCP 数据包重组为完整的 HTTP 流
4. **格式转换**: 根据用户选择的显示格式（ASCII、HEX、UTF-8）进行转换
5. **HTML 生成**: 生成带有语法高亮的 HTML 显示

### 12.7 性能优化机制

1. **按需解析**: 只有在用户查看详情时才进行完整解析
2. **缓存机制**: 解码结果在会话期间可能被缓存
3. **分页加载**: 支持限制显示的数据包数量
4. **格式选择**: 用户可以选择不同的显示格式以优化性能

### 12.8 总结

**关键发现：**

1. **双重解析架构**: Arkime 采用了两阶段解析策略
   - Capture 阶段：快速提取元数据用于搜索和索引
   - Viewer 阶段：完整解析用于详细显示

2. **HTTP ASCII 信息生成时机**:
   - **元数据**（如 HTTP 头部字段）在 capture 时解析并存储到 ES
   - **完整内容**（如 HTTP 载荷的 ASCII 显示）在 viewer 时实时生成

3. **实时重新解析**: 每次点击 detail 按钮都会：
   - 从 PCAP 文件重新读取原始数据包
   - 重新进行协议解析和流重组
   - 根据用户选择的格式生成显示内容

4. **性能权衡**: 这种设计在存储效率和显示灵活性之间取得了平衡
   - ES 中只存储搜索需要的元数据
   - 完整的数据包内容按需从 PCAP 文件重新解析

这种架构确保了 Arkime 既能提供高效的搜索能力，又能在需要时提供完整的数据包级别的详细信息。

## 13. HTTP Session Detail 页面信息来源详细分析

以 HTTP 协议为例，当点击 Session Detail 时会显示两个主要部分：`General` 和 `HTTP`，以及原始报文的各种格式输出。以下详细分析每个字段的数据来源。

### 13.1 General 部分 - 来自 ES 存储的元数据

`General` 部分的信息**完全来自 Elasticsearch 存储**，在 capture 阶段就已经解析并存储：

#### 时间信息 (ES 存储)
```json
{
  "Time": "2023-12-08 10:30:00 - 2023-12-08 10:30:15",  // firstPacket - lastPacket
  "Id": "session_id",                                    // 会话ID
  "Root Id": "root_session_id",                          // 根会话ID (可选)
  "Community Id": "1:hash_value"                         // 网络社区ID (可选)
}
```

#### 网络基础信息 (ES 存储)
```json
{
  "Node": "capture-node-01",                             // 捕获节点
  "Protocols": ["tcp", "http"],                          // 检测到的协议
  "Ethertype": 2048,                                     // 以太网类型
  "IP Protocol": 6                                       // IP协议号 (TCP=6)
}
```

#### 源/目标地址信息 (ES 存储)
```json
{
  "Source": {
    "IP": "*************:12345",                         // 源IP:端口
    "Packets": 10,                                       // 源发送包数
    "Bytes": 1024,                                       // 源发送字节数
    "Country": "US",                                     // 地理位置
    "ASN": "AS15169 Google LLC",                         // AS信息
    "RIR": "ARIN"                                        // RIR信息
  },
  "Destination": {
    "IP": "********:80",                                 // 目标IP:端口
    "Packets": 8,                                        // 目标发送包数
    "Bytes": 2048,                                       // 目标发送字节数
    "Country": "CN",                                     // 地理位置
    "ASN": "AS4134 China Telecom",                       // AS信息
    "RIR": "APNIC"                                       // RIR信息
  }
}
```

### 13.2 HTTP 部分 - 来自 ES 存储的协议元数据

`HTTP` 部分的信息也**主要来自 Elasticsearch 存储**，在 capture 阶段由 HTTP 解析器提取：

#### HTTP 基础信息 (ES 存储)
```json
{
  "Method": ["GET", "POST"],                             // HTTP 方法
  "Status code": [200, 404],                            // HTTP 状态码
  "Hosts": ["example.com", "api.example.com"],          // Host 头部
  "User Agents": ["Mozilla/5.0..."],                    // User-Agent 头部
  "Client Versions": ["1.1"],                           // HTTP 客户端版本
  "Server Versions": ["1.1"]                            // HTTP 服务器版本
}
```

#### HTTP 头部信息 (ES 存储)
```json
{
  "Request Headers": ["accept", "authorization", "content-type"],     // 请求头部名称列表
  "Response Headers": ["content-type", "set-cookie", "server"],       // 响应头部名称列表
  "accept Header": ["application/json"],                              // 具体头部值
  "content-type Header": ["application/json"],                        // 具体头部值
  "authorization Header": ["Bearer token123"]                         // 具体头部值
}
```

#### HTTP 扩展信息 (ES 存储)
```json
{
  "Body MD5s": ["d41d8cd98f00b204e9800998ecf8427e"],    // HTTP 响应体 MD5
  "Body SHA256s": ["e3b0c44298fc1c149afbf4c8996fb924..."], // HTTP 响应体 SHA256
  "QS Keys": ["param1", "param2"],                       // 查询字符串键
  "Cookie Keys": ["sessionid", "csrftoken"],             // Cookie 键
  "User": ["admin"],                                     // 认证用户
  "Auth Type": ["basic"],                                // 认证类型
  "JA4h": ["hash_value"],                                // JA4h 指纹
  "JA4h_r": ["hash_value"]                               // JA4h_r 指纹
}
```

### 13.3 原始报文显示 - 来自 PCAP 实时解析

原始报文的各种格式输出（Raw UTF-8、Hex Dump 等）**完全来自 PCAP 文件的实时解析**：

#### 数据包读取和解码过程
```javascript
// 从 PCAP 文件读取原始数据包
SessionAPIs.processSessionId(req.params.id, !req.packetsOnly, null, (pcap, buffer, cb, i) => {
  let obj = {};
  if (buffer.length > 16) {
    try {
      pcap.decode(buffer, obj);  // 实时解码数据包
    } catch (e) {
      obj = { ip: { p: 'Error decoding' + e } };
    }
  }
  packets[i] = obj;
  cb(null);
}, (err, session) => {
  // 处理解码后的数据包
});
```

#### 原始报文格式生成 (PCAP 实时解析)
- **Raw UTF-8**: 将 TCP 载荷转换为 UTF-8 文本显示
- **Hex Dump**: 将原始字节转换为十六进制显示
- **ASCII**: 将字节转换为 ASCII 字符显示
- **Natural**: 智能格式化显示（JSON、XML 等）

### 13.4 数据来源对比表

| 显示部分 | 数据来源 | 存储位置 | 解析时机 | 内容类型 |
|----------|----------|----------|----------|----------|
| **General 部分** | ES 存储 | Elasticsearch | Capture 阶段 | 会话元数据 |
| **HTTP 部分** | ES 存储 | Elasticsearch | Capture 阶段 | 协议元数据 |
| **Raw UTF-8** | PCAP 解析 | PCAP 文件 | Viewer 阶段 | 完整载荷 |
| **Hex Dump** | PCAP 解析 | PCAP 文件 | Viewer 阶段 | 原始字节 |
| **ASCII** | PCAP 解析 | PCAP 文件 | Viewer 阶段 | 字符显示 |
| **Natural** | PCAP 解析 | PCAP 文件 | Viewer 阶段 | 格式化内容 |

### 13.5 具体字段的存储机制

#### ES 中存储的 HTTP 字段定义
```c
// capture/parsers/http.c 中的字段定义
hostField = arkime_field_define("http", "lotermfield",
                                "host.http", "Hostname", "http.host",
                                "HTTP host header field",
                                ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_CNT,
                                ...);

uaField = arkime_field_define("http", "termfield",
                              "http.user-agent", "Useragent", "http.useragent",
                              "User-Agent Header",
                              ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_CNT,
                              ...);

methodField = arkime_field_define("http", "termfield",
                                  "http.method", "Request Method", "http.method",
                                  "HTTP Request Method",
                                  ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_CNT,
                                  ...);
```

#### Capture 阶段的字段提取
```c
// HTTP 解析器提取字段到 ES
LOCAL int http_parse(ArkimeSession_t *session, void *uw, const uint8_t *data, int remaining, int which)
{
    // 解析 HTTP 头部
    http_parser_execute(&http->parsers[http->which], &parserSettings, (char *)data, remaining);

    // 提取字段存储到会话
    arkime_field_string_add(methodField, session, http_method_str(parser->method), -1, TRUE);
    arkime_field_string_add(hostField, session, host, hostLen, TRUE);
    arkime_field_string_add(uaField, session, userAgent, userAgentLen, TRUE);
    // ... 其他字段提取
}
```

### 13.6 原始报文内容的生成

#### TCP 流重组 (PCAP 实时处理)
```javascript
// viewer/apiSessions.js
if (req.query.showFrames) {
  Pcap.packetFlow(session, packets, +req.query.packets || 200, callback);
} else {
  Pcap.reassemble_generic_ether(packets, +req.query.packets || 200, callback);
}
```

#### 格式化显示生成
- **时间戳**: 从数据包头部实时提取
- **TCP 标志**: 从 TCP 头部实时解析
- **字节数**: 从数据包长度实时计算
- **载荷内容**: 从 TCP 载荷实时提取和格式化

### 13.7 性能考虑

#### ES 存储的优势
- **快速搜索**: 基于索引的高效查询
- **聚合统计**: 支持复杂的统计分析
- **字段过滤**: 只返回需要的字段
- **存储效率**: 只存储提取的元数据

#### PCAP 解析的优势
- **完整性**: 保留所有原始数据
- **灵活性**: 支持多种显示格式
- **准确性**: 避免解析过程中的信息丢失
- **可扩展性**: 支持新的协议和格式

### 13.8 总结

**关键发现：**

1. **双层架构设计**：
   - **元数据层** (ES)：快速搜索和统计
   - **原始数据层** (PCAP)：完整内容显示

2. **HTTP 信息分布**：
   - **General 和 HTTP 部分**：100% 来自 ES 存储
   - **原始报文显示**：100% 来自 PCAP 实时解析

3. **数据处理时机**：
   - **Capture 阶段**：提取协议元数据存储到 ES
   - **Viewer 阶段**：从 PCAP 重新解析完整内容

4. **显示内容差异**：
   - **ES 数据**：结构化的协议字段和统计信息
   - **PCAP 数据**：完整的原始载荷和格式化显示

这种设计确保了 Arkime 既能提供高效的搜索和分析能力，又能在需要时提供完整的网络流量详细信息，满足不同层次的网络分析需求。

## 14. 添加新协议解析器指南

当需要为 Arkime 添加新的协议支持时，需要对 Elasticsearch 字段映射和 capture 模块的协议解析器进行相应的操作。以下是详细的实现指南。

### 14.1 Elasticsearch 字段操作

#### 14.1.1 动态字段映射机制

Arkime 使用**动态字段映射**机制，**无需手动修改 ES 索引结构**：

```json
{
  "dynamic": "true",
  "dynamic_templates": [
    {
      "template_ip_end": {
        "match": "*Ip",
        "mapping": {
          "type": "ip"
        }
      }
    },
    {
      "template_string": {
        "match_mapping_type": "string",
        "mapping": {
          "type": "keyword"
        }
      }
    }
  ]
}
```

**关键特性：**
- **自动类型推断**: 根据字段名称和数据类型自动映射
- **模板匹配**: 使用模式匹配自动应用合适的字段类型
- **无需重建索引**: 新字段会自动添加到现有索引

#### 14.1.2 字段定义自动同步

当 capture 模块定义新字段时，会自动同步到 ES：

```c
// capture 模块中定义字段
int myProtocolField = arkime_field_define("myprotocol", "termfield",
                                          "myprotocol.command", "Command", "myprotocol.command",
                                          "Protocol command field",
                                          ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_CNT,
                                          (char *)NULL);
```

**自动同步过程：**
1. `arkime_field_define()` 在内存中注册字段
2. `arkime_db_add_field()` 将字段定义发送到 ES fields 索引
3. ES 动态模板自动处理新字段类型映射

#### 14.1.3 字段类型映射规则

| Arkime 字段类型 | ES 字段类型 | 说明 |
|----------------|-------------|------|
| `ARKIME_FIELD_TYPE_STR_HASH` | `keyword` | 字符串，支持精确匹配 |
| `ARKIME_FIELD_TYPE_STR_ARRAY` | `keyword` | 字符串数组 |
| `ARKIME_FIELD_TYPE_INT_HASH` | `long` | 整数 |
| `ARKIME_FIELD_TYPE_IP_GHASH` | `ip` | IP地址 |
| `ARKIME_FIELD_TYPE_FLOAT` | `float` | 浮点数 |

### 14.2 Capture 模块协议解析器操作

#### 14.2.1 创建协议解析器文件

基于模板创建新的协议解析器：

```c
// capture/parsers/myprotocol.c
#include <string.h>
#include <ctype.h>
#include "arkime.h"

// 协议信息结构体
typedef struct {
    uint16_t    version;
    uint32_t    sessionId;
    // 其他协议特定字段
} MyProtocolInfo_t;

// 字段定义 (全局变量)
static int commandField;
static int versionField;
static int statusField;

/******************************************************************************/
// 协议解析函数
void myprotocol_parser(ArkimeSession_t *session, void *uw, const unsigned char *data, int remaining)
{
    MyProtocolInfo_t *info = uw;

    // 解析协议头部
    if (remaining < 8) return;

    uint16_t version = ntohs(*(uint16_t*)data);
    uint16_t command = ntohs(*(uint16_t*)(data + 2));
    uint32_t status = ntohl(*(uint32_t*)(data + 4));

    // 添加字段到会话
    arkime_field_int_add(versionField, session, version);
    arkime_field_int_add(commandField, session, command);
    arkime_field_int_add(statusField, session, status);

    // 添加协议标签
    arkime_nids_add_tag(session, ARKIME_FIELD_TAGS, "protocol:myprotocol");
}

/******************************************************************************/
// 释放协议信息
void myprotocol_free(ArkimeSession_t UNUSED(*session), void *uw)
{
    MyProtocolInfo_t *info = uw;
    ARKIME_TYPE_FREE(MyProtocolInfo_t, info);
}

/******************************************************************************/
// 协议分类函数
void myprotocol_classify(ArkimeSession_t *session, const unsigned char *data, int len)
{
    // 避免重复分类
    if (arkime_nids_has_tag(session, ARKIME_FIELD_TAGS, "protocol:myprotocol"))
        return;

    // 协议识别逻辑
    if (len < 4) return;

    // 检查协议魔数
    if (data[0] == 0x4D && data[1] == 0x50) { // "MP" 魔数
        arkime_nids_add_tag(session, ARKIME_FIELD_TAGS, "protocol:myprotocol");

        MyProtocolInfo_t *info = ARKIME_TYPE_ALLOC0(MyProtocolInfo_t);
        arkime_parsers_register(session, myprotocol_parser, info, myprotocol_free);
    }
}

/******************************************************************************/
// 解析器初始化函数
void arkime_parser_init()
{
    // 注册端口分类器 (TCP 端口 8080)
    arkime_parsers_classifier_register_tcp("myprotocol", 0, 8080, myprotocol_classify);

    // 注册 UDP 端口分类器
    arkime_parsers_classifier_register_udp("myprotocol", 0, 8080, myprotocol_classify);

    // 注册基于内容的分类器
    arkime_parsers_classifier_register_tcp("myprotocol", 0, 0, "\x4D\x50", 2, myprotocol_classify);

    // 定义协议字段
    commandField = arkime_field_define("myprotocol", "integer",
                                       "myprotocol.command", "Command", "myprotocol.command",
                                       "Protocol command field",
                                       ARKIME_FIELD_TYPE_INT_HASH, ARKIME_FIELD_FLAG_CNT,
                                       (char *)NULL);

    versionField = arkime_field_define("myprotocol", "integer",
                                       "myprotocol.version", "Version", "myprotocol.version",
                                       "Protocol version field",
                                       ARKIME_FIELD_TYPE_INT_HASH, ARKIME_FIELD_FLAG_CNT,
                                       (char *)NULL);

    statusField = arkime_field_define("myprotocol", "integer",
                                      "myprotocol.status", "Status", "myprotocol.status",
                                      "Protocol status field",
                                      ARKIME_FIELD_TYPE_INT_HASH, ARKIME_FIELD_FLAG_CNT,
                                      (char *)NULL);
}
```

#### 14.2.2 协议注册方式

Arkime 支持多种协议注册方式：

##### 基于端口的注册
```c
// TCP 端口注册
arkime_parsers_classifier_register_tcp("myprotocol", 0, 8080, myprotocol_classify);

// UDP 端口注册
arkime_parsers_classifier_register_udp("myprotocol", 0, 8080, myprotocol_classify);

// 端口范围注册
arkime_parsers_classifier_register_port("myprotocol", 0, 8080, 8090,
                                        ARKIME_PARSERS_PORT_TCP, myprotocol_classify);
```

##### 基于内容的注册
```c
// 基于魔数的识别
arkime_parsers_classifier_register_tcp("myprotocol", 0, 0, "\x4D\x50", 2, myprotocol_classify);

// 基于字符串的识别
arkime_parsers_classifier_register_tcp("myprotocol", 0, 0, "MYPROTO", 7, myprotocol_classify);
```

##### 基于 IP 协议的注册
```c
// 注册 IP 协议号
arkime_packet_set_ip_cb(IPPROTO_MYPROTOCOL, myprotocol_packet_enqueue);

// 注册以太网类型
arkime_packet_set_ethernet_cb(0x88CC, myprotocol_packet_enqueue);
```

#### 14.2.3 字段定义最佳实践

```c
// 字符串字段 (支持搜索和统计)
hostField = arkime_field_define("myprotocol", "termfield",
                                "myprotocol.host", "Host", "myprotocol.host",
                                "Protocol host field",
                                ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_CNT,
                                "category", "host",
                                (char *)NULL);

// IP 地址字段
ipField = arkime_field_define("myprotocol", "ip",
                              "myprotocol.server", "Server IP", "myprotocol.serverIp",
                              "Protocol server IP",
                              ARKIME_FIELD_TYPE_IP_GHASH, ARKIME_FIELD_FLAG_CNT,
                              "category", "ip",
                              (char *)NULL);

// 数组字段
arrayField = arkime_field_define("myprotocol", "termfield",
                                 "myprotocol.commands", "Commands", "myprotocol.commands",
                                 "Protocol commands array",
                                 ARKIME_FIELD_TYPE_STR_ARRAY, ARKIME_FIELD_FLAG_CNT,
                                 (char *)NULL);

// 仅用于搜索的字段 (不存储)
searchField = arkime_field_define("myprotocol", "termfield",
                                  "myprotocol.search", "Search", "myprotocol.search",
                                  "Search only field",
                                  ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_FAKE,
                                  (char *)NULL);
```

### 14.3 编译和集成

#### 14.3.1 修改 Makefile

```makefile
# 在 capture/Makefile 中添加
PARSERS_O += parsers/myprotocol.o

# 或者在 capture/parsers/Makefile 中添加
OBJS += myprotocol.o
```

#### 14.3.2 重新编译

```bash
cd capture
make clean
make
```

### 14.4 创建 Viewer 显示模板

#### 14.4.1 创建详情显示模板

```jade
// capture/parsers/myprotocol.detail.jade
if (session.myprotocol)
  div.sessionDetailMeta.bold MyProtocol
  dl.sessionDetailMeta(suffix="myprotocol")
    +arrayList(session.myprotocol, "command", "Commands", "myprotocol.command")
    +arrayList(session.myprotocol, "version", "Versions", "myprotocol.version")
    +arrayList(session.myprotocol, "status", "Status", "myprotocol.status")
    +arrayList(session.myprotocol, "host", "Hosts", "myprotocol.host")
```

### 14.5 测试和验证

#### 14.5.1 验证字段定义

```bash
# 检查字段是否正确注册
curl -X GET "localhost:9200/arkime_fields/_search?q=myprotocol*&pretty"
```

#### 14.5.2 验证数据解析

```bash
# 检查会话数据
curl -X GET "localhost:9200/arkime_sessions3-*/_search?q=protocols:myprotocol&pretty"
```

### 14.6 高级特性

#### 14.6.1 协议状态机

```c
typedef enum {
    MYPROTOCOL_STATE_INIT,
    MYPROTOCOL_STATE_HANDSHAKE,
    MYPROTOCOL_STATE_DATA,
    MYPROTOCOL_STATE_CLOSE
} MyProtocolState;

typedef struct {
    MyProtocolState state;
    uint32_t        sequenceNum;
    // 其他状态信息
} MyProtocolInfo_t;
```

#### 14.6.2 多阶段解析

```c
void myprotocol_parser(ArkimeSession_t *session, void *uw, const unsigned char *data, int remaining)
{
    MyProtocolInfo_t *info = uw;

    switch (info->state) {
    case MYPROTOCOL_STATE_INIT:
        // 处理初始化阶段
        break;
    case MYPROTOCOL_STATE_HANDSHAKE:
        // 处理握手阶段
        break;
    case MYPROTOCOL_STATE_DATA:
        // 处理数据阶段
        break;
    }
}
```

#### 14.6.3 协议链式解析

```c
void myprotocol_parser(ArkimeSession_t *session, void *uw, const unsigned char *data, int remaining)
{
    // 解析当前协议
    // ...

    // 识别并注册下一层协议
    if (next_protocol_detected) {
        arkime_parsers_classifier_register_tcp("nextprotocol", 0, 0,
                                               next_protocol_signature, sig_len,
                                               nextprotocol_classify);
    }
}
```

### 14.7 总结

**ES 字段操作：**
- ✅ **无需手动操作**：动态字段映射自动处理
- ✅ **自动同步**：字段定义自动同步到 ES
- ✅ **类型推断**：根据 Arkime 字段类型自动映射 ES 类型

**Capture 模块操作：**
- ✅ **创建解析器文件**：实现协议解析逻辑
- ✅ **注册协议分类器**：基于端口、内容或协议号
- ✅ **定义协议字段**：使用 `arkime_field_define()`
- ✅ **修改编译配置**：更新 Makefile
- ✅ **创建显示模板**：为 viewer 创建 Jade 模板

这种设计确保了添加新协议的过程简单高效，无需复杂的 ES 索引操作，专注于协议解析逻辑的实现即可。

## 15. 文件存储机制和文件编号系统

Arkime 使用复杂的文件存储机制来管理 PCAP 文件，并通过精确的文件编号系统来跟踪数据包位置。以下详细分析这些机制的工作原理。

### 15.1 文件存储架构

#### 15.1.1 文件命名规范

Arkime 使用标准化的文件命名规范：

```text
{nodeName}-{YYMMDD}-{fileNumber}.{extension}
```

**示例：**
```text
capture-node-01-231208-00001234.pcap
capture-node-01-231208-00001235.pcap.gz
capture-node-01-231208-00001236.pcap.zst
```

**命名组件：**
- `nodeName`: 捕获节点名称（配置中的 `nodeName`）
- `YYMMDD`: 基于第一个数据包时间的日期
- `fileNumber`: 全局唯一的文件编号（8位数字）
- `extension`: 文件扩展名（`.pcap`, `.pcap.gz`, `.pcap.zst`, `.arkime`）

#### 15.1.2 文件创建过程

```c
// 文件创建的核心函数
char *arkime_db_create_file_full(const struct timeval *firstPacket, const char *name,
                                 uint64_t size, int locked, uint32_t *id, ...)
{
    // 获取全局唯一文件编号
    snprintf(key, sizeof(key), "fn-%s", config.nodeName);
    if (nextFileNum == 0) {
        num = arkime_db_get_sequence_number_sync(key);  // 同步获取
    } else {
        num = nextFileNum;                              // 使用预分配的编号
        nextFileNum = 0;
        arkime_db_get_sequence_number(key, arkime_db_fn_seq_cb, 0);  // 异步获取下一个
    }

    // 构建文件名
    struct tm tmp;
    gmtime_r(&firstPacket->tv_sec, &tmp);
    snprintf(filename, sizeof(filename), "/%s-%02d%02d%02d-%08u%s",
             config.nodeName, tmp.tm_year % 100, tmp.tm_mon + 1, tmp.tm_mday, num, name);

    // 创建 ES 文件记录
    BSB_EXPORT_sprintf(jbsb, "{\"num\":%d, \"name\":\"%s\", \"first\":%" PRIu64
                       ", \"node\":\"%s\", \"locked\":%d",
                       num, filename, fp, config.nodeName, locked);

    // 发送到 ES files 索引
    key_len = snprintf(key, sizeof(key), "/%sfiles/_doc/%s-%u?refresh=true",
                       config.prefix, config.nodeName, num);
    arkime_http_schedule(esServer, "POST", key, key_len, json, BSB_LENGTH(jbsb),
                         NULL, ARKIME_HTTP_PRIORITY_BEST, NULL, NULL);
}
```

### 15.2 文件编号系统

#### 15.2.1 全局序列号机制

Arkime 使用 Elasticsearch 的版本控制机制来生成全局唯一的文件编号：

```javascript
// ES 序列号生成
POST /{prefix}sequence/_doc/fn-{nodeName}
{}

// 响应包含版本号作为文件编号
{
  "_version": 1234,  // 这个版本号就是文件编号
  "result": "created"
}
```

**序列号特性：**
- **全局唯一**: 跨所有节点的唯一编号
- **递增序列**: 严格递增，无重复
- **原子操作**: ES 保证并发安全
- **持久化**: 存储在 ES 中，重启后继续

#### 15.2.2 文件编号分配策略

```c
// 实时捕获模式
if (!config.pcapReadOffline) {
    // 预分配下一个文件编号
    snprintf(key, sizeof(key), "fn-%s", config.nodeName);
    nextFileNum = arkime_db_get_sequence_number_sync(key);
}

// 文件创建时的编号分配
ARKIME_LOCK(nextFileNum);
if (nextFileNum == 0) {
    // 离线模式或异步调用未返回，同步获取
    num = arkime_db_get_sequence_number_sync(key);
} else {
    // 实时模式，使用预分配的编号
    num = nextFileNum;
    nextFileNum = 0;  // 清空，避免重用
    // 异步获取下一个编号
    arkime_db_get_sequence_number(key, arkime_db_fn_seq_cb, 0);
}
ARKIME_UNLOCK(nextFileNum);
```

### 15.3 文件轮转机制

#### 15.3.1 轮转触发条件

Arkime 支持多种文件轮转条件：

```c
// 文件大小轮转
if (info->file->pos >= config.maxFileSizeB) {
    writer_simple_process_buf(thread, 1);  // 强制轮转
}

// 时间轮转
if (config.maxFileTimeM > 0 &&
    now.tv_sec - fileAge[thread].tv_sec >= config.maxFileTimeM * 60) {
    writer_simple_process_buf(thread, 1);  // 时间轮转
}

// 缓冲区轮转
if (now.tv_sec - lastSave[thread].tv_sec >= 10 &&
    currentInfo[thread]->bufpos >= pageSize) {
    writer_simple_process_buf(thread, 0);  // 缓冲区轮转
}
```

**轮转类型：**
- **大小轮转**: 文件达到 `maxFileSizeG` 配置的大小
- **时间轮转**: 文件存在时间达到 `maxFileTimeM` 分钟
- **缓冲区轮转**: 缓冲区数据达到阈值且超过10秒未写入

#### 15.3.2 轮转配置选项

```ini
# 配置文件示例
maxFileSizeG=12          # 文件最大12GB
maxFileTimeM=120         # 文件最长120分钟
pcapWriteSize=262144     # 写入缓冲区大小256KB
```

### 15.4 数据包位置跟踪

#### 15.4.1 位置信息记录

每个会话维护详细的数据包位置信息：

```c
// 会话结构中的位置数组
typedef struct arkime_session {
    GArray    *filePosArray;    // 数据包在文件中的位置
    GArray    *fileNumArray;    // 数据包对应的文件编号
    GArray    *fileLenArray;    // 数据包长度（可选）
    uint32_t   lastFileNum;     // 最后使用的文件编号
} ArkimeSession_t;

// 数据包写入时的位置记录
if (session->lastFileNum != packet->writerFileNum) {
    // 新文件，记录文件编号
    session->lastFileNum = packet->writerFileNum;
    g_array_append_val(session->fileNumArray, packet->writerFileNum);

    // 记录文件分隔符（负数表示新文件开始）
    int64_t pos = -1LL * packet->writerFileNum;
    g_array_append_val(session->filePosArray, pos);
}

// 记录数据包位置
g_array_append_val(session->filePosArray, packet->writerFilePos);

// 可选：记录数据包长度
if (config.enablePacketLen) {
    uint16_t len = 16 + packet->pktlen;  // PCAP头部(16字节) + 数据包长度
    g_array_append_val(session->fileLenArray, len);
}
```

#### 15.4.2 位置数组结构

```json
{
  "packetPos": [
    -1234,        // 负数：文件编号1234的开始标记
    0,            // 第一个数据包在文件中的位置
    1500,         // 第二个数据包在文件中的位置
    3000,         // 第三个数据包在文件中的位置
    -1235,        // 负数：文件编号1235的开始标记
    0,            // 新文件中第一个数据包的位置
    1400          // 新文件中第二个数据包的位置
  ],
  "fileId": [
    1234,         // 对应的文件编号
    1234,
    1234,
    1235,
    1235
  ],
  "packetLen": [  // 可选，需要 enablePacketLen=true
    82,           // 第一个数据包总长度（16+66）
    1530,         // 第二个数据包总长度（16+1514）
    1530,         // 第三个数据包总长度（16+1514）
    144,          // 第四个数据包总长度（16+128）
    1416          // 第五个数据包总长度（16+1400）
  ]
}
```

### 15.5 文件存储后端

#### 15.5.1 支持的存储后端

Arkime 支持多种存储后端：

```c
// Writer 类型
enum {
    ARKIME_WRITER_SIMPLE,     // 本地文件系统
    ARKIME_WRITER_S3,         // Amazon S3
    ARKIME_WRITER_INPLACE,    // 原地处理
    ARKIME_WRITER_NULL        // 空写入器（测试用）
};
```

**Simple Writer (本地存储):**
```c
// 文件路径构建
snprintf(filename, sizeof(filename), "%s/%s-%02d%02d%02d-%08u%s",
         config.pcapDir[config.pcapDirPos],  // PCAP目录
         config.nodeName,                    // 节点名
         tmp.tm_year % 100,                  // 年
         tmp.tm_mon + 1,                     // 月
         tmp.tm_mday,                        // 日
         num,                                // 文件编号
         name);                              // 扩展名
```

**S3 Writer (云存储):**
```c
// S3 路径构建
snprintf(filename, sizeof(filename), "s3://%s/%s/%s-%02d%02d%02d-%08u%s",
         s3Bucket,                           // S3存储桶
         s3Region,                           // S3区域
         config.nodeName,                    // 节点名
         tmp.tm_year % 100,                  // 年
         tmp.tm_mon + 1,                     // 月
         tmp.tm_mday,                        // 日
         num,                                // 文件编号
         extension[compressionMode]);        // 扩展名
```

#### 15.5.2 压缩支持

```c
// 支持的压缩格式
enum {
    ARKIME_COMPRESSION_NONE,    // 无压缩 (.pcap)
    ARKIME_COMPRESSION_GZIP,    // GZIP压缩 (.pcap.gz)
    ARKIME_COMPRESSION_ZSTD     // ZSTD压缩 (.pcap.zst)
};

// 压缩配置
simpleCompression=gzip          # 启用GZIP压缩
simpleGzipLevel=6              # GZIP压缩级别
simpleZstdLevel=3              # ZSTD压缩级别
simpleCompressionBlockSize=64000  # 压缩块大小
```

## 16. 压缩与编码机制

### 16.1 ZSTD压缩机制

#### 16.1.1 压缩算法概述

Arkime支持多种压缩算法，其中ZSTD是默认推荐的压缩方式：

```c
typedef enum {
    ARKIME_COMPRESSION_NONE,    // 无压缩
    ARKIME_COMPRESSION_GZIP,    // GZIP压缩
    ARKIME_COMPRESSION_ZSTD     // ZSTD压缩（默认）
} CompressionMode;
```

#### 16.1.2 ZSTD压缩配置

**配置参数：**
```ini
# 压缩相关配置
simpleCompression=zstd              # 压缩算法：none/gzip/zstd
simpleZstdLevel=3                   # ZSTD压缩级别 (0-22)
simpleCompressionBlockSize=64000    # 压缩块大小 (8191-1048575)
```

**压缩级别说明：**
- **0**: 默认级别，平衡压缩率和速度
- **1-3**: 快速压缩，适合实时处理
- **4-9**: 标准压缩，推荐用于生产环境
- **10-22**: 高压缩率，适合存储优化

#### 16.1.3 压缩块设计

ZSTD压缩采用**分块压缩**设计：

```c
/*
 * 压缩设计说明：
 * - 压缩文件由多个压缩块组成
 * - 只能从块的开始位置读取压缩文件
 * - 块大小可变，最大未压缩数据由simpleCompressionBlockSize控制
 * - uncompressedBits计算以容纳simpleCompressionBlockSize
 * - 每个数据包的文件位置由两部分组成：
 *   X: 压缩块在文件中的起始位置（左移uncompressedBits位）
 *   Y: 数据包在未压缩块内的位置
 */
```

**位置编码公式：**
```c
// 压缩模式下的位置计算
packet->writerFilePos = (blockStart << uncompressedBits) + posInBlock;

// 解压时的位置解析
insideOffset = pos & (uncompressedBitsSize - 1);
realPos = Math.floor(pos / uncompressedBitsSize);
```

#### 16.1.4 压缩性能特征

| 压缩算法 | 压缩率 | 压缩速度 | 解压速度 | 内存使用 |
|---------|--------|----------|----------|----------|
| none    | 1.0x   | 最快     | 最快     | 最低     |
| gzip    | 3-5x   | 中等     | 快       | 中等     |
| zstd    | 3-6x   | 快       | 最快     | 中等     |

### 16.2 Gap编码机制

#### 16.2.1 Gap编码原理

Gap编码是Arkime用于优化packetPos存储的压缩算法：

```javascript
// Gap编码逻辑
if (config.gapPacketPos) {
    /* 简单的间隔编码：
     * - 与前一个间隔相同的用0表示
     * - 负数和负数后的数字不编码
     * - 可减少50%以上的存储空间
     */
    let last = 0;
    let lastgap = 0;
    for (let i = 0; i < packetPosArray.length; i++) {
        let fpos = packetPosArray[i];
        if (fpos < 0) {
            // 负数：新文件开始标记
            last = 0;
            lastgap = 0;
            output(fpos);  // 直接输出负数
        } else {
            if (fpos - last == lastgap) {
                output(0);  // 相同间隔用0表示
            } else {
                lastgap = fpos - last;
                output(lastgap);  // 输出间隔差值
            }
            last = fpos;
        }
    }
}
```

#### 16.2.2 Gap编码示例

**原始位置数据：**
```
实际位置: [24, 90, 156, 222, 288, 354]
间隔:     [-, 66, 66, 66, 66, 66]
```

**Gap编码后：**
```
存储值: [24, 66, 0, 0, 0, 0]
节省: 66% 的存储空间
```

**解码过程：**
```javascript
// Gap解码逻辑
let last = 0;
let lastgap = 0;
for (let i = 0; i < fields.packetPos.length; i++) {
    if (fields.packetPos[i] < 0) {
        last = 0;  // 新文件，重置
    } else {
        if (fields.packetPos[i] === 0) {
            fields.packetPos[i] = last + lastgap;  // 使用上次间隔
        } else {
            lastgap = fields.packetPos[i];
            fields.packetPos[i] += last;  // 累加计算实际位置
        }
        last = fields.packetPos[i];
    }
}
```

#### 16.2.3 Gap编码配置

**启用/禁用Gap编码：**
```ini
# 启用Gap编码（默认）
gapPacketPos=true

# 禁用Gap编码
gapPacketPos=false
```

**禁用Gap编码的场景：**
- 调试packetPos问题时
- 需要直接访问原始位置值
- 与第三方工具集成时
- 本地索引模式（localPcapIndex=true时自动禁用）

### 16.3 压缩与编码组合配置

#### 16.3.1 推荐配置组合

**生产环境（高压缩率）：**
```ini
simpleCompression=zstd
simpleZstdLevel=6
simpleCompressionBlockSize=64000
gapPacketPos=true
```

**调试环境（无压缩）：**
```ini
simpleCompression=none
gapPacketPos=false
```

**高性能环境（快速压缩）：**
```ini
simpleCompression=zstd
simpleZstdLevel=1
simpleCompressionBlockSize=32000
gapPacketPos=true
```

#### 16.3.2 配置影响分析

| 配置组合 | 文件大小 | 写入性能 | 读取性能 | 调试难度 |
|---------|----------|----------|----------|----------|
| zstd+gap | 最小 | 中等 | 中等 | 困难 |
| gzip+gap | 小 | 慢 | 快 | 困难 |
| none+nogap | 最大 | 最快 | 最快 | 简单 |

### 16.4 Session必须字段规范

#### 16.4.1 Session核心字段

**时间字段：**
```json
{
  "@timestamp": 1752149929057,      // 毫秒时间戳，session处理时间
  "firstPacket": 1041342931300,     // session第一包时间戳（毫秒）
  "lastPacket": 1041342932300,      // session最后一包时间戳（毫秒）
  "length": 1000                    // 会话持续时间（毫秒）
}
```

**网络协议字段：**
```json
{
  "ipProtocol": 6,                  // IP协议号：6=TCP, 17=UDP, 1=ICMP
  "protocols": ["tcp", "http"],     // 协议栈数组
  "node": "localhost"               // 捕获节点名称
}
```

**网络端点字段：**
```json
{
  "source": {
    "ip": "*************",         // 源IP地址
    "port": 3267,                  // 源端口
    "bytes": 207,                  // 源方向字节数
    "packets": 1,                  // 源方向数据包数
    "mac": ["00:09:6b:88:f5:c9"]   // 源MAC地址数组
  },
  "destination": {
    "ip": "*************",         // 目标IP地址
    "port": 80,                    // 目标端口
    "bytes": 1024,                 // 目标方向字节数
    "packets": 2,                  // 目标方向数据包数
    "mac-cnt": 1,                  // MAC地址计数
    "mac": ["00:e0:81:00:b0:28"]   // 目标MAC地址数组
  }
}
```

**统计字段：**
```json
{
  "network": {
    "packets": 3,                  // 总数据包数
    "bytes": 1231                  // 总字节数
  }
}
```

#### 16.4.2 数据包位置字段

**packetPos字段结构：**
```json
{
  "packetPos": [
    -102,                          // 负数：文件ID标记
    1900544,                       // 第一个数据包位置
    66,                            // Gap编码：间隔值或0
    0,                             // Gap编码：使用上次间隔
    0                              // Gap编码：使用上次间隔
  ],
  "fileId": [102]                  // 对应文件ID数组
}
```

**packetLen字段（可选）：**
```json
{
  "packetLen": [
    0,                             // 文件标记对应长度
    82,                            // 第一个数据包长度（含16字节头）
    66,                            // 第二个数据包长度
    74                             // 第三个数据包长度
  ]
}
```

#### 16.4.3 协议特定字段

**HTTP协议字段：**
```json
{
  "http": {
    "method": ["GET"],             // HTTP方法
    "uri": ["/index.html"],        // 请求URI
    "statuscode": [200],           // 响应状态码
    "useragent": ["Mozilla/5.0"]   // User-Agent
  }
}
```

**DNS协议字段：**
```json
{
  "dns": {
    "query": ["example.com"],      // DNS查询域名
    "queryType": ["A"],            // 查询类型
    "answer": ["*******"]          // DNS响应
  }
}
```

### 16.5 Files索引必须字段

#### 16.5.1 文件元数据结构

```json
{
  "num": 102,                              // 文件ID（唯一标识）
  "name": "/opt/arkime/raw/test.pcap",     // 文件完整路径
  "first": 1752150698995,                  // 文件首包时间戳
  "node": "localhost",                     // 捕获节点名称
  "filesize": 1048576,                     // 文件大小（字节）
  "locked": 0,                             // 锁定状态：1=处理中，0=完成
  "packetPosEncoding": "gap0",             // 位置编码方式
  "compression": "zstd",                   // 压缩算法
  "compressionBlockSize": 64000            // 压缩块大小
}
```

#### 16.5.2 字段定义索引

**arkime_fields_v30索引结构：**
```json
{
  "_index": "arkime_fields_v30",
  "_id": "http.method",                    // 字段标识符
  "_source": {
    "friendlyName": "HTTP Method",         // 显示名称
    "group": "http",                       // 字段分组
    "help": "HTTP request method",         // 帮助信息
    "dbField2": "http.method",             // 数据库字段名
    "type": "termfield",                   // 字段类型
    "category": "protocol"                 // 字段类别
  }
}
```

### 16.6 压缩与编码最佳实践

#### 16.6.1 配置选择指南

**场景1：高吞吐量实时分析**
```ini
# 优先考虑性能
simpleCompression=zstd
simpleZstdLevel=1
simpleCompressionBlockSize=32000
gapPacketPos=true
pcapWriteSize=524288
```

**场景2：长期存储优化**
```ini
# 优先考虑存储空间
simpleCompression=zstd
simpleZstdLevel=9
simpleCompressionBlockSize=128000
gapPacketPos=true
```

**场景3：调试和开发**
```ini
# 优先考虑可读性
simpleCompression=none
gapPacketPos=false
```

#### 16.6.2 性能调优建议

**内存使用优化：**
- 较小的compressionBlockSize减少内存使用
- 较大的compressionBlockSize提高压缩率
- 推荐范围：32KB-128KB

**I/O性能优化：**
- pcapWriteSize应大于compressionBlockSize
- 使用SSD存储提高随机读取性能
- 考虑RAID配置提高并发性能

**CPU使用优化：**
- 低CPU使用：zstdLevel=1-3
- 平衡模式：zstdLevel=3-6
- 高压缩率：zstdLevel=6-9

#### 16.6.3 故障排除

**常见问题及解决方案：**

1. **packetPos超出文件边界**
   ```bash
   # 检查gap编码设置
   grep gapPacketPos /opt/arkime/etc/config.ini

   # 临时禁用gap编码重新处理
   gapPacketPos=false
   ```

2. **压缩文件损坏**
   ```bash
   # 检查压缩设置
   grep simpleCompression /opt/arkime/etc/config.ini

   # 验证zstd库版本
   /opt/arkime/bin/capture --version
   ```

3. **读取性能问题**
   ```bash
   # 检查块大小设置
   grep simpleCompressionBlockSize /opt/arkime/etc/config.ini

   # 考虑减小块大小提高随机访问性能
   simpleCompressionBlockSize=32000
   ```

### 16.7 文件元数据管理

#### 16.7.1 ES Files 索引结构

**arkime_files_v30索引映射：**
```json
{
  "mappings": {
    "properties": {
      "num": {
        "type": "integer"                    // 文件ID
      },
      "name": {
        "type": "keyword"                    // 文件路径
      },
      "first": {
        "type": "date",                      // 首包时间
        "format": "epoch_millis"
      },
      "node": {
        "type": "keyword"                    // 节点名称
      },
      "filesize": {
        "type": "long"                       // 文件大小
      },
      "locked": {
        "type": "integer"                    // 锁定状态
      },
      "packetPosEncoding": {
        "type": "keyword"                    // 编码方式
      },
      "compression": {
        "type": "keyword"                    // 压缩算法
      },
      "compressionBlockSize": {
        "type": "integer"                    // 压缩块大小
      }
    }
  }
}
```

#### 16.7.2 文件生命周期管理

**文件状态转换：**
```
创建 -> 写入中(locked=1) -> 完成(locked=0) -> 归档 -> 删除
```

**状态检查命令：**
```bash
# 检查正在处理的文件
curl -X GET "localhost:9200/arkime_files_v30/_search" -H 'Content-Type: application/json' -d'
{
  "query": {
    "term": {
      "locked": 1
    }
  }
}'

# 检查文件大小分布
curl -X GET "localhost:9200/arkime_files_v30/_search" -H 'Content-Type: application/json' -d'
{
  "aggs": {
    "size_stats": {
      "stats": {
        "field": "filesize"
      }
    }
  }
}'
```

### 16.8 Session数据结构详解

#### 16.8.1 完整Session示例

```json
{
  "index": {
    "_index": "arkime_sessions3-20241214"
  }
}
{
  "@timestamp": 1734134400000,
  "firstPacket": 1734134395000,
  "lastPacket": 1734134396500,
  "length": 1500,
  "ipProtocol": 6,
  "node": "capture-node-01",

  "source": {
    "ip": "*************",
    "port": 54321,
    "bytes": 1024,
    "packets": 5,
    "mac": ["aa:bb:cc:dd:ee:ff"],
    "geo": {
      "country_code": "CN",
      "asn": "AS4134"
    }
  },

  "destination": {
    "ip": "************",
    "port": 80,
    "bytes": 2048,
    "packets": 6,
    "mac": ["11:22:33:44:55:66"],
    "geo": {
      "country_code": "US",
      "asn": "AS15169"
    }
  },

  "network": {
    "packets": 11,
    "bytes": 3072,
    "community_id": "1:abc123def456..."
  },

  "packetPos": [-102, 24, 66, 0, 0, 132, 0],
  "packetLen": [0, 82, 66, 74, 58, 90, 70],
  "fileId": [102],

  "protocols": ["ethernet", "ip", "tcp", "http"],

  "http": {
    "method": ["GET"],
    "uri": ["/api/data"],
    "version": ["1.1"],
    "statuscode": [200],
    "useragent": ["curl/7.68.0"],
    "host": ["api.example.com"],
    "requestBody": [""],
    "responseBody": [""]
  },

  "tcp": {
    "flags": ["syn", "ack", "fin"],
    "seq": [1000, 2000],
    "ack": [1001, 2001]
  },

  "tls": {
    "version": ["1.3"],
    "cipher": ["TLS_AES_256_GCM_SHA384"],
    "sni": ["api.example.com"],
    "ja3": ["abc123..."],
    "ja3s": ["def456..."]
  },

  "tags": ["internal-traffic", "api-call"],
  "cert": [
    {
      "issuer": "CN=Let's Encrypt Authority X3",
      "subject": "CN=api.example.com",
      "serial": "03:ab:cd:ef...",
      "validFrom": 1734000000000,
      "validTo": 1765536000000
    }
  ]
}
```

#### 16.8.2 字段类型说明

**基础数据类型：**
- `integer`: 整数类型（端口、协议号等）
- `long`: 长整数类型（时间戳、字节数等）
- `keyword`: 关键字类型（IP地址、协议名等）
- `text`: 文本类型（用户代理、URI等）
- `boolean`: 布尔类型（标志位等）

**数组字段：**
- 大多数字段支持数组格式
- 用于存储多个值（如多个HTTP请求）
- 数组索引对应数据包序号

**嵌套对象：**
- `source/destination`: 网络端点信息
- `geo`: 地理位置信息
- `cert`: 证书信息
- `dns`: DNS查询信息

### 16.9 集成开发指南

#### 16.9.1 自定义协议解析器

**解析器注册：**
```c
// 注册新协议解析器
void arkime_parser_init() {
    arkime_parsers_register(session, ARKIME_PARSERS_PORT, "tcp", 8080, my_protocol_parser);
}

// 解析器实现
int my_protocol_parser(ArkimeSession_t *session, void *uw, const uint8_t *data, int len) {
    // 解析协议数据
    // 添加字段到session
    arkime_field_string_add(myProtocolHostField, session, "example.com", -1, TRUE);
    return 0;
}
```

**字段定义：**
```c
// 定义新字段
int myProtocolHostField;

void arkime_plugin_init() {
    myProtocolHostField = arkime_field_define("myprotocol", "host",
        "myprotocol.host", "Host", "t",
        "Host field for my protocol",
        ARKIME_FIELD_TYPE_STR_HASH, ARKIME_FIELD_FLAG_CNT,
        NULL);
}
```

#### 16.9.2 输出格式定制

**JSON输出定制：**
```c
// 自定义JSON输出
void my_custom_json_output(ArkimeSession_t *session, BSB *jbsb) {
    BSB_EXPORT_cstr(jbsb, "\"customField\":");
    BSB_EXPORT_sprintf(jbsb, "\"%s\",", session->customData);
}

// 注册输出回调
arkime_plugins_cb_pre_save_register(my_custom_json_output);
```

这个详细的文档涵盖了Arkime的压缩机制、Gap编码、Session必须字段以及实际应用指南。文档包含了配置示例、性能调优建议和故障排除方法，可以作为开发和运维的参考手册。

```json
{
  "_index": "arkime_files_v30",
  "_id": "capture-node-01-1234",
  "_source": {
    "num": 1234,                           // 文件编号
    "name": "/data/capture-node-01-231208-00001234.pcap",  // 完整路径
    "node": "capture-node-01",             // 节点名称
    "first": 1625097600,                   // 第一个数据包时间戳（秒）
    "last": 1625097900,                    // 最后一个数据包时间戳（秒）
    "filesize": **********,                // 文件大小（字节）
    "locked": 0,                           // 锁定状态（0=未锁定，1=锁定）
    "startTimestamp": 1625097600000,       // 文件创建时间戳（毫秒）
    "firstTimestamp": 1625097600000,       // 第一个数据包时间戳（毫秒）
    "finishTimestamp": 1625097900000,      // 文件完成时间戳（毫秒）
    "lastTimestamp": 1625097900000,        // 最后一个数据包时间戳（毫秒）
    "packetPosEncoding": "gap0",           // 位置编码方式
    "compression": "gzip",                 // 压缩方式
    "indexFilename": "/data/capture-node-01-00001234.index"  // 索引文件路径
  }
}
```

#### 15.6.2 文件状态管理

```c
// 文件锁定机制
if (!config.noLockPcap) {
    locked = 1;  // 创建时锁定文件
}

// 文件完成时解锁
arkime_db_update_filesize(info->file->id, info->file->pos, 0);  // locked=0
```

**文件状态：**
- **locked=1**: 文件正在写入，不可删除
- **locked=0**: 文件写入完成，可以删除或归档
- **filesize=-1**: 文件大小未知（正在写入）
- **filesize>0**: 文件最终大小

### 15.7 性能优化机制

#### 15.7.1 缓冲写入

```c
// 写入缓冲区管理
typedef struct {
    uint8_t  *buf;              // 写入缓冲区
    uint32_t  bufpos;           // 当前缓冲区位置
    uint32_t  bufsize;          // 缓冲区大小
} ArkimeSimple_t;

// 批量写入磁盘
if (info->bufpos >= config.pcapWriteSize) {
    writer_simple_process_buf(thread, 0);  // 刷新缓冲区
}
```

#### 15.7.2 异步写入

```c
// 写入队列
DLL_PUSH_TAIL(simple_, &simpleQ, info);
ARKIME_COND_SIGNAL(simpleQ);  // 通知写入线程

// 专用写入线程
while (1) {
    ARKIME_LOCK(simpleQ);
    while (DLL_COUNT(simple_, &simpleQ) == 0) {
        ARKIME_COND_WAIT(simpleQ);
    }
    info = DLL_POP_HEAD(simple_, &simpleQ);
    ARKIME_UNLOCK(simpleQ);

    // 执行实际的磁盘写入
    writer_simple_write_to_disk(info);
}
```

### 15.8 总结

**文件存储机制特点：**

1. **全局唯一编号**: 使用 ES 版本控制生成唯一文件编号
2. **标准化命名**: 统一的文件命名规范便于管理
3. **精确位置跟踪**: 记录每个数据包在文件中的精确位置
4. **多种存储后端**: 支持本地文件系统、S3等存储
5. **智能轮转**: 基于大小、时间、缓冲区的灵活轮转策略
6. **压缩支持**: 支持多种压缩格式节省存储空间
7. **元数据管理**: 完整的文件元数据存储在ES中
8. **性能优化**: 缓冲写入和异步I/O提高性能

这种设计确保了 Arkime 能够高效、可靠地存储大量网络流量数据，同时提供精确的数据包定位能力，支持快速的数据检索和分析。

## 16. PCAP 上传处理实际案例分析

基于实际的 capture 命令执行输出，以下详细分析 Arkime 处理上传 PCAP 文件的完整流程。

### 16.1 执行命令和输出分析

#### 16.1.1 执行的命令
```bash
/opt/arkime/bin/capture --copy -r /home/<USER>/pcap/HTTP_PIPELINE.pcap -c /opt/arkime/etc/config.ini
```

**命令参数解析：**
- `--copy`: 复制模式，处理离线 PCAP 文件
- `-r`: 指定要读取的 PCAP 文件路径
- `-c`: 指定配置文件路径

#### 16.1.2 初始化阶段输出分析

```text
Jul  9 05:09:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/_template/arkime_sessions3_template?filter_path=**._meta 0/96 1ms 4ms
```

**分析：**
- **时间戳**: `Jul 9 05:09:22` - 处理开始时间
- **源文件**: `http.c:318` - HTTP 客户端模块
- **函数**: `arkime_http_send_sync()` - 同步 HTTP 请求函数
- **请求**: `GET /_template/arkime_sessions3_template` - 检查 ES 模板
- **状态**: `200` - 成功响应
- **数据量**: `0/96` - 发送0字节，接收96字节
- **耗时**: `1ms 4ms` - 网络耗时1ms，总耗时4ms

**目的**: 验证 Elasticsearch 会话索引模板是否存在和正确配置。

```text
Jul  9 05:09:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 0/126 0ms 4ms
```

**分析：**
- **请求**: `POST /arkime_sequence/_doc/fn-localhost` - 获取文件序列号
- **目的**: 为新的 PCAP 文件获取全局唯一的文件编号
- **响应**: 126字节包含新的文件编号信息

```text
Jul  9 05:09:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_stats/_doc/localhost 0/970 0ms 13ms
```

**分析：**
- **请求**: `POST /arkime_stats/_doc/localhost` - 更新节点统计信息
- **数据量**: 发送970字节的统计数据
- **目的**: 记录节点的处理状态和统计信息

```text
Jul  9 05:09:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_fields/_search?size=3000 0/102785 0ms 8ms
```

**分析：**
- **请求**: `GET /arkime_fields/_search?size=3000` - 获取字段定义
- **数据量**: 接收102,785字节的字段定义数据
- **目的**: 加载所有已定义的协议字段，用于数据解析

#### 16.1.3 警告信息分析

```text
Jul  9 05:09:22 db.c:2844 arkime_db_init(): WARNING - No Geo Country file could be loaded, see https://arkime.com/settings#geolite2country
Jul  9 05:09:22 db.c:2855 arkime_db_init(): WARNING - No Geo ASN file could be loaded, see https://arkime.com/settings#geolite2asn
```

**分析：**
- **警告类型**: 地理位置数据库缺失
- **影响**: IP 地址的地理位置和 ASN 信息无法解析
- **解决方案**: 下载 MaxMind GeoLite2 数据库文件

### 16.2 文件处理阶段

#### 16.2.1 健康检查和初始化

```text
Jul  9 05:09:22 http.c:406 arkime_http_curlm_check_multi_info(): 1/1 ASYNC 200 http://localhost:9200/_cat/health?format=json 0/197 1ms 3ms
```

**分析：**
- **请求类型**: `ASYNC` - 异步请求
- **目的**: 检查 Elasticsearch 集群健康状态
- **响应**: 197字节的健康状态 JSON 数据

```text
Jul  9 05:09:22 writer-simple.c:1002 writer_simple_init(): INFO: Reseting pcapWriteSize to 262144 since it must be a multiple of 4096
```

**分析：**
- **模块**: `writer-simple.c` - 简单文件写入器
- **配置调整**: 将 `pcapWriteSize` 调整为262,144字节（256KB）
- **原因**: 必须是4096的倍数以优化磁盘I/O性能

#### 16.2.2 PCAP 文件处理开始

```text
Jul  9 05:09:22 reader-libpcap-file.c:209 reader_libpcapfile_process(): Processing /home/<USER>/pcap/HTTP_PIPELINE.pcap
```

**分析：**
- **模块**: `reader-libpcap-file.c` - PCAP 文件读取器
- **函数**: `reader_libpcapfile_process()` - 处理 PCAP 文件的主函数
- **文件**: `/home/<USER>/pcap/HTTP_PIPELINE.pcap` - 正在处理的源文件

### 16.3 文件创建和元数据记录

#### 16.3.1 文件序列号更新

```text
Jul  9 05:09:22 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 2/180 0ms 9ms
```

**分析：**
- **数据量**: 发送2字节，接收180字节
- **目的**: 更新文件序列号，为下一个文件预分配编号

#### 16.3.2 文件元数据创建

```text
Jul  9 05:09:22 db.c:2225 arkime_db_create_file_full(): Creating file 109 with key >/arkime_files/_doc/localhost-109?refresh=true< using >{"num":109, "name":"/opt/arkime/raw/localhost-200513-00000109.pcap.zst", "first":**********, "node":"localhost", "locked":0, "packetPosEncoding": "gap0", "uncompressedBits": 16, "compression": "zstd", "startTimestamp":**********995, "firstTimestamp":**********354}<
```

**详细分析：**

**文件编号**: `109` - 全局唯一的文件编号

**文件路径**: `/opt/arkime/raw/localhost-200513-00000109.pcap.zst`
- `localhost`: 节点名称
- `200513`: 日期（2020年5月13日）
- `00000109`: 8位文件编号
- `.pcap.zst`: ZSTD 压缩的 PCAP 文件

**元数据字段**:
- `"num": 109` - 文件编号
- `"name": "/opt/arkime/raw/localhost-200513-00000109.pcap.zst"` - 完整文件路径
- `"first": **********` - 第一个数据包时间戳（Unix秒）
- `"node": "localhost"` - 捕获节点名称
- `"locked": 0` - 文件锁定状态（0=未锁定）
- `"packetPosEncoding": "gap0"` - 数据包位置编码方式
- `"uncompressedBits": 16` - 未压缩位数
- `"compression": "zstd"` - 压缩算法
- `"startTimestamp": **********995` - 文件创建时间戳（毫秒）
- `"firstTimestamp": **********354` - 第一个数据包时间戳（毫秒）

#### 16.3.3 文件记录确认

```text
Jul  9 05:09:23 http.c:406 arkime_http_curlm_check_multi_info(): 1/1 ASYNC 201 http://localhost:9200/arkime_files/_doc/localhost-109?refresh=true 264/168 0ms 17ms
```

**分析：**
- **状态码**: `201` - 创建成功
- **请求**: `POST /arkime_files/_doc/localhost-109?refresh=true` - 创建文件记录
- **数据量**: 发送264字节，接收168字节
- **refresh=true**: 立即刷新索引，确保数据可搜索

### 16.4 数据包处理统计

```text
Jul  9 05:09:23 packet.c:694 arkime_packet_log(): packets: 197 current sessions: 0/1 oldest: 0 - recv: 197 drop: 0 (0.00) queue: 0 disk: 0 packet: 0 close: 1 ns: 0 frags: 0/0 pstats: 197/0/0/0/0/0/78 ver: 5.7.1-GIT mem: 3.62%
```

**详细统计分析：**

**数据包统计**:
- `packets: 197` - 总共处理了197个数据包
- `recv: 197` - 接收了197个数据包
- `drop: 0 (0.00)` - 丢弃0个数据包，丢包率0%

**会话统计**:
- `current sessions: 0/1` - 当前活跃会话0个，总会话1个
- `oldest: 0` - 最老会话的年龄
- `close: 1` - 关闭了1个会话

**队列统计**:
- `queue: 0` - 队列中待处理数据包0个
- `disk: 0` - 磁盘写入队列0个
- `packet: 0` - 数据包处理队列0个

**其他统计**:
- `ns: 0` - 纳秒级统计
- `frags: 0/0` - IP分片统计：0个分片/0个重组
- `pstats: 197/0/0/0/0/0/78` - 详细的数据包统计
- `ver: 5.7.1-GIT` - Arkime 版本
- `mem: 3.62%` - 内存使用率3.62%

### 16.5 批量数据写入阶段

#### 16.5.1 统计数据写入

```text
Jul  9 05:09:23 http.c:406 arkime_http_curlm_check_multi_info(): 5/5 ASYNC 200 http://localhost:9200/arkime_dstats/_doc/localhost-832-5 838/162 3ms 8ms
Jul  9 05:09:23 http.c:406 arkime_http_curlm_check_multi_info(): 4/5 ASYNC 201 http://localhost:9200/arkime_dstats/_doc/localhost-1269-60 839/161 2ms 8ms
Jul  9 05:09:23 http.c:406 arkime_http_curlm_check_multi_info(): 3/5 ASYNC 200 http://localhost:9200/arkime_stats/_doc/localhost 838/160 3ms 10ms
Jul  9 05:09:23 http.c:406 arkime_http_curlm_check_multi_info(): 2/5 ASYNC 200 http://localhost:9200/arkime_dstats/_doc/localhost-1134-600 840/163 2ms 11ms
```

**分析：**
- **并发请求**: 5个异步请求同时处理
- **dstats**: 详细统计数据（按时间段分组）
- **stats**: 节点总体统计数据
- **高效批处理**: 多个请求并行执行，提高写入效率

#### 16.5.2 会话数据批量写入

```text
Jul  9 05:09:23 http.c:406 arkime_http_curlm_check_multi_info(): 1/5 ASYNC 200 http://localhost:9200/_bulk 3235/220 0ms 462ms
```

**关键分析：**
- **请求类型**: `/_bulk` - Elasticsearch 批量 API
- **数据量**: 发送3,235字节的批量数据，接收220字节响应
- **耗时**: 462ms - 批量写入耗时较长，说明包含大量会话数据
- **内容**: 包含解析后的会话记录，协议字段，统计信息等

#### 16.5.3 文件状态更新

```text
Jul  9 05:09:23 http.c:406 arkime_http_curlm_check_multi_info(): 1/5 ASYNC 200 http://localhost:9200/arkime_files/_update/localhost-109 132/158 0ms 54ms
```

**分析：**
- **请求**: `POST /arkime_files/_update/localhost-109` - 更新文件记录
- **目的**: 更新文件的最终状态（大小、结束时间等）
- **数据量**: 发送132字节更新数据，接收158字节响应

### 16.6 最终同步和清理

#### 16.6.1 最终批量写入

```text
Jul  9 05:09:23 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/_bulk 744/638 0ms 9ms
```

**分析：**
- **同步请求**: 确保所有数据都已写入
- **数据量**: 发送744字节，接收638字节
- **快速响应**: 9ms，说明数据量较小，可能是最后的清理数据

#### 16.6.2 统计信息最终更新

```text
Jul  9 05:09:23 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_stats/_doc/localhost 821/180 0ms 6ms
```

**分析：**
- **最终统计**: 更新节点的最终统计信息
- **数据量**: 发送821字节统计数据

#### 16.6.3 索引刷新

```text
Jul  9 05:09:23 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_*/_refresh 0/51 0ms 116ms
```

**分析：**
- **请求**: `POST /arkime_*/_refresh` - 刷新所有 Arkime 索引
- **目的**: 确保所有写入的数据立即可搜索
- **耗时**: 116ms - 刷新所有索引需要较长时间

### 16.7 处理流程时序分析

#### 16.7.1 时间线分析

```text
05:09:22 - 初始化和模板检查 (4ms)
05:09:22 - 获取文件序列号 (4ms)
05:09:22 - 更新节点统计 (13ms)
05:09:22 - 加载字段定义 (8ms)
05:09:22 - 健康检查 (3ms)
05:09:22 - 开始处理 PCAP 文件
05:09:22 - 创建文件记录 (17ms)
05:09:23 - 数据包处理完成 (1秒内)
05:09:23 - 批量写入会话数据 (462ms)
05:09:23 - 最终同步和刷新 (116ms)
```

**总处理时间**: 约1秒，处理197个数据包

#### 16.7.2 性能分析

**高效处理特点**:
1. **并行处理**: 多个异步请求同时执行
2. **批量写入**: 使用 `/_bulk` API 减少网络开销
3. **内存处理**: 数据包在内存中处理，避免频繁磁盘I/O
4. **压缩存储**: 使用 ZSTD 压缩节省存储空间

**性能瓶颈**:
1. **批量写入**: 462ms 是最大耗时操作
2. **索引刷新**: 116ms 用于确保数据可搜索
3. **网络延迟**: ES 请求的网络往返时间

### 16.8 数据流转总结

#### 16.8.1 完整的数据处理链

```mermaid
graph TD
    A[PCAP文件] --> B[libpcap读取器]
    B --> C[数据包解析]
    C --> D[协议识别]
    D --> E[字段提取]
    E --> F[会话管理]
    F --> G[PCAP写入器]
    G --> H[压缩存储]
    F --> I[ES批量写入]
    I --> J[索引刷新]

    K[文件元数据] --> L[ES files索引]
    M[统计数据] --> N[ES stats索引]
    O[字段定义] --> P[ES fields索引]
```

#### 16.8.2 关键成果

**文件处理结果**:
- **源文件**: `/home/<USER>/pcap/HTTP_PIPELINE.pcap`
- **目标文件**: `/opt/arkime/raw/localhost-200513-00000109.pcap.zst`
- **文件编号**: 109
- **压缩方式**: ZSTD
- **数据包数**: 197个

**ES 数据写入**:
- **会话记录**: 写入 `arkime_sessions3-*` 索引
- **文件记录**: 写入 `arkime_files` 索引
- **统计数据**: 写入 `arkime_stats` 和 `arkime_dstats` 索引
- **字段定义**: 更新 `arkime_fields` 索引

**处理效率**:
- **处理速度**: 197包/秒
- **无丢包**: 0% 丢包率
- **内存使用**: 3.62%
- **总耗时**: 约1秒

这个实际案例完美展示了 Arkime 的高效数据处理能力和完整的数据流转机制。

## 17. Arkime DB.c 中所有 Elasticsearch 接口分析

基于对 `capture/db.c` 源码的深入分析，以下详细总结了所有向 Elasticsearch 发送信息的接口和 JSON 格式。

### 17.1 ES 接口函数分类

#### 17.1.1 HTTP 请求函数类型

**同步请求函数**:
- `arkime_http_send_sync()` - 同步发送HTTP请求，等待响应
- `arkime_http_get()` - 同步GET请求，获取数据

**异步请求函数**:
- `arkime_http_schedule()` - 异步调度HTTP请求
- `arkime_http_get_buffer()` - 获取HTTP缓冲区

#### 17.1.2 请求优先级

```c
// 优先级定义
ARKIME_HTTP_PRIORITY_BEST     // 最高优先级
ARKIME_HTTP_PRIORITY_NORMAL   // 普通优先级
ARKIME_HTTP_PRIORITY_DROPABLE // 可丢弃优先级
```

### 17.2 主要 ES 接口详细分析

#### 17.2.1 会话数据批量写入接口

**函数**: `arkime_db_send_bulk()`
**调用**: `arkime_http_schedule(esServer, "POST", esBulkQuery, esBulkQueryLen, json, len, NULL, ARKIME_HTTP_PRIORITY_NORMAL, arkime_db_send_bulk_cb, NULL)`

**请求格式**:
```http
POST /_bulk
Content-Type: application/json
```

**JSON 格式**:
```json
{"index":{"_index":"arkime_sessions3-YYMMDD","_id":"session_id"}}
{
  "firstPacket": **********354,
  "lastPacket": **********354,
  "length": 1000,
  "ipProtocol": 6,
  "ethertype": 2048,
  "tcpflags": {
    "syn": 1,
    "syn-ack": 1,
    "ack": 10,
    "psh": 5,
    "fin": 1,
    "rst": 0,
    "urg": 0,
    "srcZero": 0,
    "dstZero": 0
  },
  "initRTT": 15,
  "tcpseq": {
    "src": 1234567890,
    "dst": 9876543210
  },
  "srcPayload8": "474554202f20485454502f312e31",
  "dstPayload8": "485454502f312e3120323030204f4b",
  "@timestamp": **********995,
  "source": {
    "ip": "*************",
    "port": 80,
    "bytes": 1024,
    "packets": 10,
    "geo": {
      "country_iso_code": "US"
    },
    "as": {
      "number": 15169,
      "full": "AS15169 Google LLC",
      "organization": {
        "name": "Google LLC"
      }
    }
  },
  "destination": {
    "ip": "********",
    "port": 443,
    "bytes": 2048,
    "packets": 15,
    "geo": {
      "country_iso_code": "CN"
    },
    "as": {
      "number": 4134,
      "full": "AS4134 China Telecom",
      "organization": {
        "name": "China Telecom"
      }
    }
  },
  "srcRIR": "ARIN",
  "dstRIR": "APNIC",
  "network": {
    "packets": 25,
    "bytes": 3072,
    "community_id": "1:LQU9qZlK+B5F3KDmev6m5PMibrg=",
    "vlan": {
      "id-cnt": 1,
      "id": [100]
    }
  },
  "client": {"bytes": 1024},
  "server": {"bytes": 2048},
  "totDataBytes": 3072,
  "segmentCnt": 1,
  "node": "localhost",
  "rootId": "session_root_id",
  "packetPos": [0, 1500, 3000, 4500],
  "packetLen": [1500, 1500, 1500, 572],
  "fileId": [109],
  "event": {
    "provider": "arkime",
    "dataset": "network"
  },
  "protocols": ["tcp", "http"],
  "http": {
    "method": ["GET"],
    "uri": ["/index.html"],
    "version": ["1.1"],
    "statuscode": [200],
    "user-agent": ["Mozilla/5.0"]
  }
}
```

#### 17.2.2 统计数据更新接口

**函数**: `arkime_db_update_stats()`
**调用**: `arkime_http_schedule(esServer, "POST", stats_key, stats_key_len, json, json_len, NULL, priority, NULL, NULL)`

**请求格式**:
```http
POST /arkime_stats/_doc/localhost?version_type=external&version=12345
Content-Type: application/json
```

**JSON 格式**:
```json
{
  "ver": "5.7.1-GIT",
  "nodeName": "localhost",
  "hostname": "arkime-server",
  "interval": 1,
  "currentTime": **********,
  "usedSpaceM": 1024,
  "freeSpaceM": 50000,
  "freeSpaceP": 98.00,
  "monitoring": 1,
  "memory": **********,
  "memoryP": 3.62,
  "cpu": 1500,
  "diskQueue": 0,
  "esQueue": 2,
  "packetQueue": 0,
  "fragsQueue": 0,
  "frags": 0,
  "needSave": 5,
  "closeQueue": 0,
  "totalPackets": 1000000,
  "totalK": 1048576,
  "totalSessions": 50000,
  "totalDropped": 0,
  "tcpSessions": 30000,
  "udpSessions": 15000,
  "icmpSessions": 100,
  "sctpSessions": 0,
  "espSessions": 0,
  "otherSessions": 4900,
  "deltaPackets": 197,
  "deltaBytes": 202752,
  "deltaWrittenBytes": 180000,
  "deltaUnwrittenBytes": 22752,
  "deltaSessions": 1,
  "deltaSessionBytes": 3072,
  "deltaDropped": 0,
  "deltaFragsDropped": 0,
  "deltaOverloadDropped": 0,
  "deltaESDropped": 0,
  "deltaDupDropped": 0,
  "esHealthMS": 15,
  "deltaMS": 1000,
  "startTime": **********
}
```

#### 17.2.3 详细统计数据接口

**函数**: `arkime_db_update_stats()` (dstats)
**调用**: `arkime_http_schedule(esServer, "POST", key, key_len, json, json_len, NULL, ARKIME_HTTP_PRIORITY_DROPABLE, NULL, NULL)`

**请求格式**:
```http
POST /arkime_dstats/_doc/localhost-832-5
Content-Type: application/json
```

**JSON 格式**: 与 stats 相同，但按时间间隔分组存储

#### 17.2.4 文件创建接口

**函数**: `arkime_db_create_file_full()`
**调用**: `arkime_http_schedule(esServer, "POST", key, key_len, json, BSB_LENGTH(jbsb), NULL, ARKIME_HTTP_PRIORITY_BEST, NULL, NULL)`

**请求格式**:
```http
POST /arkime_files/_doc/localhost-109?refresh=true
Content-Type: application/json
```

**JSON 格式**:
```json
{
  "num": 109,
  "name": "/opt/arkime/raw/localhost-200513-00000109.pcap.zst",
  "first": **********,
  "node": "localhost",
  "filesize": 0,
  "locked": 0,
  "packetPosEncoding": "gap0",
  "uncompressedBits": 16,
  "compression": "zstd",
  "startTimestamp": **********995,
  "firstTimestamp": **********354
}
```

#### 17.2.5 文件更新接口

**函数**: `arkime_db_update_file()`
**调用**: `arkime_http_schedule(esServer, "POST", key, key_len, json, BSB_LENGTH(jbsb), NULL, ARKIME_HTTP_PRIORITY_DROPABLE, NULL, NULL)`

**请求格式**:
```http
POST /arkime_files/_update/localhost-109
Content-Type: application/json
```

**JSON 格式**:
```json
{
  "doc": {
    "filesize": 1048576,
    "packetsSize": 1000000,
    "packets": 197,
    "finishTimestamp": 1752008963995,
    "lastTimestamp": **********354
  }
}
```

#### 17.2.6 序列号获取接口

**函数**: `arkime_db_get_sequence_number()` / `arkime_db_get_sequence_number_sync()`
**调用**: `arkime_http_schedule(esServer, "POST", key, key_len, json, json_len, NULL, ARKIME_HTTP_PRIORITY_BEST, arkime_db_get_sequence_number_cb, r)`

**请求格式**:
```http
POST /arkime_sequence/_doc/fn-localhost
Content-Type: application/json
```

**JSON 格式**:
```json
{}
```

**响应格式**:
```json
{
  "_index": "arkime_sequence",
  "_type": "_doc",
  "_id": "fn-localhost",
  "_version": 110,
  "result": "updated",
  "_shards": {
    "total": 1,
    "successful": 1,
    "failed": 0
  }
}
```

#### 17.2.7 健康检查接口

**函数**: `arkime_db_health_check()`
**调用**: `arkime_http_schedule(esServer, "GET", "/_cat/health?format=json", -1, NULL, 0, NULL, ARKIME_HTTP_PRIORITY_DROPABLE, arkime_db_health_check_cb, user_data)`

**请求格式**:
```http
GET /_cat/health?format=json
```

**响应格式**:
```json
[{
  "epoch": "**********",
  "timestamp": "05:09:22",
  "cluster": "elasticsearch",
  "status": "green",
  "node.total": "1",
  "node.data": "1",
  "shards": "15",
  "pri": "15",
  "relo": "0",
  "init": "0",
  "unassign": "0",
  "pending_tasks": "0",
  "max_task_wait_time": "-",
  "active_shards_percent": "100.0%"
}]
```

#### 17.2.8 字段定义接口

**函数**: `arkime_db_add_field()` / `arkime_db_delete_field()` / `arkime_db_update_field()`
**调用**: `arkime_http_schedule(esServer, "POST", "/_bulk", 6, (char *)fieldBSB.buf, BSB_LENGTH(fieldBSB), NULL, ARKIME_HTTP_PRIORITY_BEST, NULL, NULL)`

**请求格式**:
```http
POST /_bulk
Content-Type: application/json
```

**添加字段 JSON 格式**:
```json
{"index": {"_index": "arkime_fields", "_id": "http.uri"}}
{"friendlyName": "URI", "group": "http", "help": "HTTP URI", "dbField2": "http.uri", "type": "termfield", "category": "url"}
```

**删除字段 JSON 格式**:
```json
{"delete": {"_index": "arkime_fields", "_id": "http.uri"}}
```

**更新字段 JSON 格式**:
```json
{"update": {"_index": "arkime_fields", "_id": "http.uri"}}
{"doc": {"category": ["url", "web"]}}
```

### 17.3 查询接口分析

#### 17.3.1 模板检查接口

**函数**: `arkime_db_check()`
**调用**: `arkime_http_get(esServer, key, key_len, &data_len)`

**请求格式**:
```http
GET /_template/arkime_sessions3_template?filter_path=**._meta
```

#### 17.3.2 字段加载接口

**函数**: `arkime_db_load_fields()`
**调用**: `arkime_http_get(esServer, key, key_len, &data_len)`

**请求格式**:
```http
GET /arkime_fields/_search?size=3000
```

#### 17.3.3 统计数据加载接口

**函数**: `arkime_db_load_stats()`
**调用**: `arkime_http_get(esServer, stats_key, stats_key_len, &data_len)`

**请求格式**:
```http
GET /arkime_stats/_doc/localhost
```

#### 17.3.4 文件存在检查接口

**函数**: `arkime_db_file_exists()`
**调用**: `arkime_http_get(esServer, key, key_len, &data_len)`

**请求格式**:
```http
GET /arkime_files/_search?rest_total_hits_as_int&size=1&sort=num:desc&q=node:localhost+AND+name:"/path/to/file.pcap"
```

#### 17.3.5 索引刷新接口

**函数**: `arkime_db_exit()`
**调用**: `arkime_http_get(esServer, path, -1, NULL)`

**请求格式**:
```http
GET /arkime_*/_refresh
```

### 17.4 ES 索引结构总结

#### 17.4.1 主要索引

1. **arkime_sessions3-YYMMDD** - 会话数据索引
   - 存储解析后的网络会话信息
   - 按日期轮转
   - 包含协议字段、地理位置、统计信息等

2. **arkime_files** - 文件元数据索引
   - 存储 PCAP 文件的元信息
   - 文件路径、大小、时间戳等

3. **arkime_stats** - 节点统计索引
   - 存储各节点的实时统计信息
   - 性能指标、资源使用情况等

4. **arkime_dstats** - 详细统计索引
   - 按时间间隔存储的详细统计
   - 支持不同时间粒度（1s, 5s, 60s, 600s）

5. **arkime_sequence** - 序列号索引
   - 管理全局唯一序列号
   - 文件编号分配

6. **arkime_fields** - 字段定义索引
   - 存储协议字段的元数据
   - 字段类型、描述、分组等信息

#### 17.4.2 数据流转模式

```mermaid
graph TD
    A[数据包捕获] --> B[协议解析]
    B --> C[会话管理]
    C --> D[批量缓冲]
    D --> E[ES批量写入]

    F[文件管理] --> G[文件元数据]
    G --> H[ES文件索引]

    I[统计收集] --> J[性能指标]
    J --> K[ES统计索引]

    L[字段定义] --> M[协议字段]
    M --> N[ES字段索引]
```

这个分析展示了 Arkime 与 Elasticsearch 交互的完整架构，包括所有数据类型的 JSON 格式和 API 接口，为理解和扩展 Arkime 提供了详细的技术参考。
