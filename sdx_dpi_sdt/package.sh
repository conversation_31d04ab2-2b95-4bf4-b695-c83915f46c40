#!/bin/bash
# 项目依赖安装脚本
# 数组中每个元素的格式为：项目名 项目gitlab地址 项目分支名
# 例如：project1 url_1 branch_1
# 项目名和项目分支名可以随意定义，但是项目gitlab地址必须是有效的gitlab地址
# 目前只支持 cmake 构建的项目，如果项目不是 cmake 构建的，需要自行修改脚本

# get shell exec path
SHELL_FOLDER=$(cd "$(dirname "$0")";pwd)

# define an array of dependencies to install, include project name and gitlab url and branch name
declare -a dependenci=(
    "yaBasicUtils git@192.168.20.98:infra/libyaBasicUtils.git master"
    "yaconfigweb git@192.168.20.98:dev_dpi/yaconfigweb.git master"
    "yasdxwatch git@192.168.20.98:sdx/yasdxwatch.git master"
    "yaFtypes git@192.168.20.98:infra/libyaftypes.git master"
    "yaProtoRecord git@192.168.20.98:infra/libyaprotorecord.git master"
    "libsdt git@192.168.20.98:sdx/libsdt.git develop"
)

# 定义颜色代码
RED="\033[0;31m"
YELLOW="\033[0;33m"
GREEN="\033[0;32m"
NC="\033[0m"  # 恢复默认颜色

# 自定义函数用于打印不同级别的日志消息
log_debug() {
    local message="$1"
    echo -e "${YELLOW}[DEBUG] $message${NC}"
}

log_warn() {
    local message="$1"
    echo -e "${RED}[WARNING] $message${NC}"
}

log_info() {
    local message="$1"
    echo -e "${GREEN}[INFO] $message${NC}"
}

function command()
{
  # exec input command and debug command
  command=$1
  log_debug "exec command: $command"
  eval $command
}

function get_cpu_core_num()
{
  cpu_core_num=$(cat /proc/cpuinfo | grep "processor" | wc -l)
  if [ $cpu_core_num -gt 1 ]; then
    cpu_core_num=$(($cpu_core_num - 1))
  fi
  echo $cpu_core_num
}

# define a function to install dependencies
function install_dependencies() {
  # argument debug release 
  build_type=$1
    # loop through the array and install dependencies
    for i in "${dependenci[@]}"
    do
        # split the string into array
        IFS=' ' read -r -a array <<< "$i"
        # get project name
        project_name=${array[0]}
        # get project url
        project_url=${array[1]}
        # get project branch
        project_branch=${array[2]}
        # print all the information into one line
        echo "project name: $project_name, url: $project_url, branch: $project_branch"

        # get a random name for the tmp folder, and join with the project name
        tmp_folder_name=$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1)_$project_name

        # clone the project to a tmp folder with a random name
        command "git clone -b $project_branch $project_url /tmp/$tmp_folder_name"
        cd /tmp/$tmp_folder_name
        command "cmake3 -B build -S . -DCMAKE_BUILD_TYPE=$build_type"
        command "cmake3 --build build --target install -j $(get_cpu_core_num)"
        cd ..
        command "rm -rf $tmp_folder_name"
    done
}

function build()
{
  build_type=$1
  cd $SHELL_FOLDER
  rm build_$build_type -rf
  ./cmake_build.sh $build_type
  command "cmake3 --build build_$build_type --target package"
}


function main() {
  echo "this is main function" 
  # install dependencies
  # echo cpu core 
  # 示例：输出不同级别的日志
  echo shell folder is $SHELL_FOLDER
  install_dependencies release
  build release
  install_dependencies debug
  build debug
}
.
main