/****************************************************************************************
 * 文 件 名 : dpi_tftp.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/08/14
编码: wangy            2018/08/14
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>
#include <ctype.h>

#include <arpa/inet.h>
#include <rte_mempool.h>


#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_sdp.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "dpi_typedefs.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;


/* add by liugh */
extern int session_protocol_st_size[PROTOCOL_MAX];

#define TFTP_RRQ        1        //读请求
#define TFTP_WRQ        2        //写请求
#define TFTP_DATA       3        //数据
#define TFTP_ACK        4        //应答
#define TFTP_ERROR      5        //错误
#define TFTP_OACK       6        //选项应答
#define TFTP_INFO     255

#define TFTP_DATA_HEADER 4 // add by liugh


static const struct int_to_string tftp_opcode_vals[] = {
  { TFTP_RRQ,   "Read Request" },
  { TFTP_WRQ,   "Write Request" },
  { TFTP_DATA,  "Data Packet" },
  { TFTP_ACK,   "Acknowledgement" },
  { TFTP_ERROR, "Error Code" },
  { TFTP_OACK,  "Option Acknowledgement" },
  { TFTP_INFO,  "Information (MSDP)" },
  { 0,          NULL }
};


struct tftp_info
{
    uint16_t    opt_code;    //opccode码
    const char *opt_str;
    char        opt_value[COMMON_FILE_NAME];

    char transmode[COMMON_SOME_TYPE];
    char filename[COMMON_FILE_NAME];
    char filepath[COMMON_FILE_PATH];
    int  blocksize;
    int  blockseq;        //块号
    int  timeout;
    int  filesize;
    char srcfile[1024];        //源文件
    char desfile[1024];        //目的文
    char filetype[10];

    char        option[512];
};


typedef enum _tftp_index_em{

    EM_TFTP_OPERATIONTYPE,
    EM_TFTP_OPCODE,
    EM_TFTP_TRANSMODE,
    EM_TFTP_BLOCKSIZE,
    EM_TFTP_BLOCKSEQ,
    EM_TFTP_TIMEOUT,
    EM_TFTP_FILESIZE,
    EM_TFTP_FILENAME,
    EM_TFTP_FILETYPE,
    EM_TFTP_FILEPATH,
    EM_TFTP_SRCFILE,
    EM_TFTP_DESFILE,
    EM_TFTP_OPTION,
    EM_TFTP_OPTION_KEY,
    EM_TFTP_OPTION_VAL,
    EM_TFTP_FILE_MD5,
    EM_TFTP_MAX
}tftp_index_em;


static dpi_field_table  tftp_field_array[] = {

    DPI_FIELD_D(EM_TFTP_OPERATIONTYPE,            EM_F_TYPE_STRING,         "OperationType"),
    DPI_FIELD_D(EM_TFTP_OPCODE,                     EM_F_TYPE_UINT16,         "Opcode"),
    DPI_FIELD_D(EM_TFTP_TRANSMODE,                 EM_F_TYPE_STRING,         "TransMode"),
    DPI_FIELD_D(EM_TFTP_BLOCKSIZE,                 EM_F_TYPE_UINT16,         "BlockSize"),
    DPI_FIELD_D(EM_TFTP_BLOCKSEQ,                 EM_F_TYPE_UINT32,         "BlockSequence"),
    DPI_FIELD_D(EM_TFTP_TIMEOUT,                 EM_F_TYPE_UINT32,         "TimeOut"),
    DPI_FIELD_D(EM_TFTP_FILESIZE,                 EM_F_TYPE_UINT32,         "FileSize"),
    DPI_FIELD_D(EM_TFTP_FILENAME,                 EM_F_TYPE_STRING,         "FileName"),
    DPI_FIELD_D(EM_TFTP_FILETYPE,                 EM_F_TYPE_STRING,         "FileType"),
    DPI_FIELD_D(EM_TFTP_FILEPATH,                 EM_F_TYPE_STRING,         "FilePath"),
    DPI_FIELD_D(EM_TFTP_SRCFILE,                 EM_F_TYPE_STRING,         "SourceFile"),
    DPI_FIELD_D(EM_TFTP_DESFILE,                 EM_F_TYPE_STRING,         "DestinationFile"),
    DPI_FIELD_D(EM_TFTP_OPTION,                 EM_F_TYPE_STRING,         "Option"),
    DPI_FIELD_D(EM_TFTP_OPTION_KEY,             EM_F_TYPE_STRING,         "OptionName"),
    DPI_FIELD_D(EM_TFTP_OPTION_VAL,             EM_F_TYPE_STRING,         "OptionValue"),
    DPI_FIELD_D(EM_TFTP_FILE_MD5,               EM_F_TYPE_STRING,         "FileMd5"),
};

static dpi_field_table  tftp_field_array_sdt[] = {

    DPI_FIELD_D(EM_TFTP_OPERATIONTYPE,             YV_FT_BYTES,         "OperationType"),
    DPI_FIELD_D(EM_TFTP_OPCODE,                    YV_FT_UINT16,         "Opcode"),
    DPI_FIELD_D(EM_TFTP_TRANSMODE,                 YV_FT_BYTES,         "TransMode"),
    DPI_FIELD_D(EM_TFTP_BLOCKSIZE,                 YV_FT_UINT16,        "BlockSize"),
    DPI_FIELD_D(EM_TFTP_BLOCKSEQ,                  YV_FT_UINT32,        "BlockSequence"),
    DPI_FIELD_D(EM_TFTP_TIMEOUT,                   YV_FT_UINT32,        "TimeOut"),
    DPI_FIELD_D(EM_TFTP_FILESIZE,                  YV_FT_UINT32,        "FileSize"),
    DPI_FIELD_D(EM_TFTP_FILENAME,                  YV_FT_BYTES,         "FileName"),
    DPI_FIELD_D(EM_TFTP_FILETYPE,                  YV_FT_BYTES,         "FileType"),
    DPI_FIELD_D(EM_TFTP_FILEPATH,                  YV_FT_BYTES,         "FilePath"),
    DPI_FIELD_D(EM_TFTP_SRCFILE,                   YV_FT_BYTES,         "SourceFile"),
    DPI_FIELD_D(EM_TFTP_DESFILE,                   YV_FT_BYTES,         "DestinationFile"),
    DPI_FIELD_D(EM_TFTP_OPTION,                    YV_FT_BYTES,         "Option"),
    DPI_FIELD_D(EM_TFTP_OPTION_KEY,                YV_FT_BYTES,         "OptionName"),
    DPI_FIELD_D(EM_TFTP_OPTION_VAL,                YV_FT_BYTES,         "OptionValue"),
    DPI_FIELD_D(EM_TFTP_FILE_MD5,                  YV_FT_BYTES,         "FileMd5"),
};



static int tftp_data_conv(const char *data, uint32_t size, char *conv,struct tftp_session *session) _U_;
static int tftp_data_conv(const char *data, uint32_t size, char *conv,struct tftp_session *session)
{
    int i, j;
    char c, pre;
    
    i = 0;
    j = 0;

    pre = session->conv_c;
    if (pre == '\r')
        conv[j++] = pre;
    while (size--) {
        c = data[i++];
        if (pre == '\r') {
            if (c == '\n' && j != 0)
                j--;
            else if (c == '\0') {
                pre = c;
                continue;
            }
        }
        conv[j++] = c;
        pre = c;
    }
       session->conv_c = pre;
    if (pre == '\r')
        j--;

    return j;
}


static void identify_tftp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
        uint16_t s_port = 0, d_port = 0;
        uint16_t optcode;

        if (g_config.protocol_switch[PROTOCOL_TFTP] == 0)
                return;

        if (payload_len < 4) {
                DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TFTP);
                return;
        }

        if (flow->tuple.inner.proto == IPPROTO_UDP) {
                s_port = ntohs(flow->tuple.inner.port_src);
                d_port = ntohs(flow->tuple.inner.port_dst);
        } else {
                DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_TFTP);
                return;
        }

        optcode = get_uint16_ntohs(payload, 0);

        if ((s_port == 69 || d_port == 69) && optcode > 0 && optcode <= 6)
                flow->real_protocol_id = PROTOCOL_TFTP;
        else if ((s_port == 6969 || d_port == 6969) && optcode > 0 && optcode <= 6)
            flow->real_protocol_id = PROTOCOL_TFTP;

        return;
}

static int tftp_field_element(struct tbl_log *log_ptr,struct flow_info *flow _U_, int direction _U_, struct tftp_info *info, int *idx, int i)
{
    switch(i){

    case EM_TFTP_OPERATIONTYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->opt_str,strlen(info->opt_str));
        break;
    case EM_TFTP_OPCODE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, NULL, info->opt_code);
        break;
    case EM_TFTP_TRANSMODE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->transmode, strlen(info->transmode));
        break;
    case EM_TFTP_BLOCKSIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->blocksize);
        break;
    case EM_TFTP_BLOCKSEQ:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->blockseq);
        break;
    case EM_TFTP_TIMEOUT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->timeout);
        break;
    case EM_TFTP_FILESIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->filesize);
        break;
    case EM_TFTP_FILENAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->filename, strlen(info->filename));
        break;
    case EM_TFTP_FILETYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->filetype, strlen(info->filetype));
        break;
    case EM_TFTP_FILEPATH:
        if(strlen(info->filepath)>0){
            char filename[128]={0};
            if(get_filename(info->filepath, filename)){
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)filename, strlen(filename));
                break;
            }
        }
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->filepath, strlen(info->filepath));
        break;
    case EM_TFTP_SRCFILE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->srcfile, strlen(info->srcfile));
        break;
    case EM_TFTP_DESFILE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->desfile, strlen(info->desfile));
        break;
    case EM_TFTP_OPTION:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->option, strlen(info->option));
        break;
    case EM_TFTP_OPTION_KEY:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->opt_str, strlen(info->opt_str));
        break;
    case EM_TFTP_OPTION_VAL:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, tftp_field_array[i].type, (const uint8_t *)info->opt_value, strlen(info->opt_value));
        break;
    // case EM_TFTP_FILE_MD5:
    //     break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,1);
        break;
    }

    return 0;

}

static int write_tftp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
//    char __str[64] = {0};
    int idx = 0,i;
    struct tbl_log *log_ptr;

    struct tftp_info *info=(struct tftp_info *)field_info;
    if(!info){
        return 0;
    }
    
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "tftp");

    for(i=0;i<EM_TFTP_MAX;i++){
        tftp_field_element(log_ptr, flow, direction, info, &idx, i);
    }


    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_TFTP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}


static void tftp_dissect_options(const uint8_t *payload, uint32_t offset, uint32_t payload_len, struct tftp_session *session, struct tftp_info *info)
{
    int option_len, value_len;
    int copy_len;
    int value_offset;
    char optionname[128];
    char optionvalue[128];
    const char *delim  = "";
    uint16_t option_total_len = 0;

    while (offset < payload_len) {
        option_len = find_str_end_len(payload + offset, payload_len - offset);
        if (option_len <= 0)
            break;
        value_offset = offset + option_len;
        value_len = find_str_end_len(payload + value_offset, payload_len - value_offset);
        if (value_len <= 0)
            break;
        copy_len = option_len;
        if (copy_len > (int)sizeof(optionname) - 1)
            copy_len = sizeof(optionname) - 1;
        strncpy(optionname, (const char *)payload + offset, copy_len);
        optionname[copy_len] = 0;

        copy_len = value_len;
        if (copy_len > (int)sizeof(optionvalue) - 1)
            copy_len = sizeof(optionvalue) - 1;
        strncpy(optionvalue, (const char *)payload + value_offset, copy_len);
        optionvalue[copy_len] = 0;

        if (strcmp(optionname, "blksize") == 0)
            session->blocksize = (uint16_t)atoi(optionvalue);
        else if (strcmp(optionname, "timeout") == 0)
            session->timeout = atoi(optionvalue);
        else if (strcmp(optionname, "tsize") == 0)
            session->filesize = atoi(optionvalue);

        if (option_total_len < sizeof(info->option))
        {
            option_total_len += snprintf(info->option + option_total_len, sizeof(info->option) - option_total_len,
                                            "%s%s=%s", delim, optionname, optionvalue);
        }

        delim = ",";
        offset += option_len + value_len;
    }

    return;
}

static int dissect_tftp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(flag);
    UNUSED(seq);
    if(payload==NULL || payload_len<4){
        return PKT_DROP;
    }
    
    struct tftp_info info;
    struct tftp_session *session;
    uint16_t optcode;
    uint16_t offset = 0;
    uint32_t i;
    uint32_t filename_len = 0;
    uint32_t type_len = 0;
    uint16_t blocknumber;
    uint16_t err_code;
    // uint32_t header_4bytes=0;
    //uint8_t  last_flag=0;   // 判断udp数据是否为最后一个

    struct conversation_tuple tuple;

    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = payload_len;    

    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct tftp_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct tftp_session));
        session = (struct tftp_session *)flow->app_session;
        session->blocksize = 512;//default size
    }
    session = (struct tftp_session *)flow->app_session;


    memset(&info, 0, sizeof(info));
    
    if (-1 == dpi_get_be16(&pkt, offset, &optcode)) return 0;
    offset += 2;

    // info.opcode = val_to_string(optcode, tftp_opcode_vals);

    info.opt_code = optcode;
    info.opt_str = val_to_string(optcode, tftp_opcode_vals);

    if (info.opt_str == NULL)
        return 0;

    strncpy(session->opt_str, info.opt_str, sizeof(session->opt_str) - 1);

    switch (optcode) {
        case TFTP_RRQ:

            session->port_dst=flow->tuple.inner.port_dst;
            session->port_src=flow->tuple.inner.port_src;    

            for (i = 2; i < payload_len; i++) {
                if (payload[i] == 0) {
                    filename_len = i - 2;
                    break;
                }
            }
            if (filename_len <= 0)
                return PKT_OK;
            
            for (i = 2 + filename_len + 1; i < payload_len; i++) {
                if (payload[i] == 0) {
                    type_len = i - 3 - filename_len;            
                    break;
                }
            }
            if (type_len <= 0)
                return PKT_OK;
            
            strncpy(session->filename, (const char *)payload + 2, filename_len > sizeof(info.filename) - 1 ? sizeof(info.filename) - 1 : filename_len);
            strncpy(session->transmode, (const char *)payload + 3 + filename_len, type_len > sizeof(info.transmode) - 1 ? sizeof(info.transmode) - 1 : type_len);

            strncpy(info.filename, session->filename, strlen(session->filename));
            strncpy(info.srcfile, session->filename, strlen(session->filename));
            strncpy(info.transmode, session->transmode, strlen(session->transmode));
            strncpy(info.opt_value, session->filename, strlen(session->filename));

            if (strcasecmp(session->transmode, "netascii") == 0) {
                session->convert=1;
            }else{
                session->convert=0;
            }
            session->last_block=0;
            session->now_time_usec=(unsigned long long)g_config.g_now_time_usec;

            offset = filename_len + type_len + 4;
            tftp_dissect_options(payload, offset, payload_len, session, &info);


            write_tftp_log(flow, direction, &info, NULL);

            memset(&tuple, 0, sizeof(tuple));
            tuple.port_src = flow->tuple.inner.port_src;
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
            tuple.proto = flow->tuple.inner.proto;

            find_or_create_conversation(&tuple, NO_PORT_B, PROTOCOL_TFTP,session);
            break;
        case TFTP_WRQ:
            session->port_dst=flow->tuple.inner.port_dst;
            session->port_src=flow->tuple.inner.port_src;    

            for (i = 2; i < payload_len; i++) {
                if (payload[i] == 0) {
                    filename_len = i - 2;
                    break;
                }
            }
            if (filename_len <= 0)
                return PKT_OK;
            
            for (i = 2 + filename_len + 1; i < payload_len; i++) {
                if (payload[i] == 0) {
                    type_len = i - 3 - filename_len;            
                    break;
                }
            }

            if (type_len <= 0)
                return PKT_OK;

            strncpy(session->filename, (const char *)payload + 2, filename_len > sizeof(info.filename) - 1 ? sizeof(info.filename) - 1 : filename_len);
            strncpy(session->transmode, (const char *)payload + 3 + filename_len, type_len > sizeof(info.transmode) - 1 ? sizeof(info.transmode) - 1 : type_len);    
            strncpy(info.transmode, session->transmode, strlen(session->transmode));
            strncpy(info.filename, session->filename, strlen(session->filename));
            strncpy(info.desfile, session->filename, strlen(session->filename));
            strncpy(info.opt_value, session->filename, strlen(session->filename));

            if (strcasecmp(session->transmode, "netascii") == 0) {
                session->convert=1;
            }else{
                session->convert=0;
            }
            session->last_block=0;
            
            offset = filename_len + type_len + 4;
            tftp_dissect_options(payload, offset, payload_len, session, &info);
            write_tftp_log(flow, direction, &info, NULL);

            memset(&tuple, 0, sizeof(tuple));
            tuple.port_src = flow->tuple.inner.port_src;
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
            tuple.proto = flow->tuple.inner.proto;
            
            find_or_create_conversation(&tuple, NO_PORT_B, PROTOCOL_TFTP,session);
            break;

        case TFTP_OACK:
            tftp_dissect_options(payload, offset, payload_len, session, &info);
            info.blocksize = session->blocksize;
            info.timeout = session->timeout;
            info.filesize = session->filesize;
            write_tftp_log(flow, direction, &info, NULL);
            break;
        
        case TFTP_DATA:
            blocknumber = get_uint16_ntohs(payload, 2);
            info.blockseq = blocknumber;
            info.blocksize = session->blocksize;
            snprintf(info.opt_value, sizeof(info.opt_value), "%u", blocknumber);
            if (payload_len - 4 < session->blocksize && session->filesize == 0) {
                    info.filesize = (blocknumber -1) * session->blocksize + payload_len - 4;
                    info.timeout = session->timeout;
                    // write_tftp_log(flow, direction, &info, NULL);
            }
            write_tftp_log(flow, direction, &info, NULL);
            break;
            #if 0
            //strncpy(info.opt_str, "Data packet", sizeof(info.opt_str) - 1);
            strncpy(session->opt_str, "Data", sizeof(session->opt_str) - 1);
            strncpy(info.opt_str, session->opt_str, strlen(session->opt_str));
            if(payload_len>4){
                blocknumber = get_uint16_ntohs(payload, 2);
            }else{
                break;
            }
            
            info.blockseq = blocknumber;
            if (payload_len - 4 < session->blocksize ) {   // 判断是到最后一个包，如果是最后一个包则将相关信息写入tbl中
                //last_flag=1;
                info.blocksize = payload_len - 4;
                session->filesize = blocknumber * session->blocksize + payload_len - 4;
                info.filesize = session->filesize;
                //snprintf(session->filepath, COMMON_FILE_PATH,"%s/%s/%s", 
                //                             g_config.tbl_out_dir, tbl_log_array[TBL_LOG_TFTP].protoname, session->filename);
                if(strlen(session->filepath)==0){
                    snprintf(session->filepath, COMMON_FILE_PATH, "%s/%s/tftp_%llu_%s.writing", 
                        g_config.tbl_out_dir, tbl_log_array[TBL_LOG_TFTP].protoname,(unsigned long long)g_config.g_now_time_usec, session->filename);

                    /* 这种情况是udp只有一个包 */
                    if(payload_len<8){break;}
                    header_4bytes=get_uint32_ntohl(payload+4,0);
                    if(!detect_file_type(header_4bytes, session->filetype)){
                        if(dpi_is_utf8((const char*)payload+4, payload_len-4) > 0 || 
                           dpi_is_gbk((const char*)payload+4, payload_len-4) > 0){
                           strncpy((char*)session->filetype,"text",COMMON_SOME_TYPE);
                        }else{
                           strncpy((char*)session->filetype,"unknown",COMMON_SOME_TYPE);
                        }
                    }
                }
                strncpy(info.filepath, session->filepath, strlen(session->filepath)-8);
                strncpy(info.filetype, session->filetype, COMMON_SOME_TYPE);
                write_tftp_log(flow, direction, &info);
            }

             /*start to store tftp data add by liugh*/
            
            FILE *fp=NULL;
            const char *data;
            char *conv,*dummy;
            uint32_t  size=0;
            //char tftp_name[COMMON_FILE_PATH]={0};
            
            if(strlen(session->filepath)==0){
                snprintf(session->filepath, COMMON_FILE_PATH, "%s/%s/tftp_%llu_%s.writing", 
                    g_config.tbl_out_dir, tbl_log_array[TBL_LOG_TFTP].protoname,(unsigned long long)g_config.g_now_time_usec, session->filename);
            }
            fp = fopen(session->filepath, "a");
            if(NULL==fp){
                printf("tftp_open file error :%s !\n",session->filepath);
                break;
            }
            //printf("[debgug] session->convert=%d, session->block:%d, session->size:%d\n",session->convert, session->last_block,session->blocksize);
            data=(const char *)payload+TFTP_DATA_HEADER;
            if(session->convert==1){
                conv=dpi_malloc(session->blocksize+1);
                if(NULL==conv){
                    if(fp){fclose(fp);}
                    break;
                }
            }
            size=session->blocksize;

            if(blocknumber==1){  // 判断文件类型
                uint32_t header_4bytes=get_uint32_ntohl(data,0);
                //printf("header is:%04x",header_4bytes);
                if(!detect_file_type(header_4bytes, session->filetype)){
                    if(dpi_is_utf8((const char*)payload+4, payload_len-4) > 0 || 
                       dpi_is_gbk((const char*)payload+4, payload_len-4) > 0){
                       strncpy(session->filetype,"text",COMMON_SOME_TYPE);
                    }else{
                       strncpy(session->filetype,"unknown",COMMON_SOME_TYPE);
                    }
                }
            }

            if(blocknumber == session->last_block+1){
                if(size != payload_len-TFTP_DATA_HEADER){
                    size = payload_len-4;
                }
                if(1==session->convert){
                    size = tftp_data_conv(data, size, conv,session);
                    data = conv;
                }
                fwrite(data,1, size, fp);
                session->last_block=blocknumber;
            }else{
                if(blocknumber >session->blocksize ){
                    uint16_t temp_block=blocknumber;
                    dummy=dpi_malloc(session->blocksize+1);
                    if(NULL==dummy){
                        if(fp){fclose(fp);}
                        break;
                    }
                    memset(dummy, 0 ,session->blocksize);
                    for(;temp_block != session->last_block-1;temp_block++){
                        fwrite(dummy, 1, session->blocksize , fp);
                    }
                    dpi_free(dummy);

                    if(size != payload_len - TFTP_DATA_HEADER ){
                        size = payload_len - TFTP_DATA_HEADER;
                    }
                    session->conv_c=0;  
                    if(1==session->convert){
                        size = tftp_data_conv(data, size, conv,session);
                        data = conv;
                    }
                    fwrite(data, 1, size, fp);
                    session->conv_c=0; 
                    session->last_block=blocknumber;
                }else{
                    fseek(fp,(blocknumber-1)*session->blocksize,SEEK_SET);
                    session->conv_c=0;  
                    if(1==session->convert){
                        size = tftp_data_conv(data, size, conv,session);
                        data = conv;
                    }
                    fwrite(data, 1, size, fp);
                    session->conv_c=0;   
                    //fseek(fp,0,SEEK_SET);
                }
            }
            
            if(1==session->convert){
                dpi_free(conv);
            }
            
            if(fp){
                fclose(fp);
            }

            #endif
            break;
        case TFTP_ACK:
            info.blockseq = get_uint16_ntohs(payload, offset);
            snprintf(info.opt_value, sizeof(info.opt_value), "%u", info.blockseq);
            write_tftp_log(flow, direction, &info, NULL);
            break;
        case TFTP_ERROR:
            err_code = get_uint16_ntohs(payload, offset);
            snprintf(info.opt_value, sizeof(info.opt_value), "%u", err_code);
            write_tftp_log(flow, direction, &info, NULL);
            break;
        case TFTP_INFO:
        default:
            write_tftp_log(flow, direction, &info, NULL);
            break;
    }

    return 0;
}

static void init_tftp_dissector(void)
{
    dpi_register_proto_schema(tftp_field_array,EM_TFTP_MAX,"tftp");
    /* add by liugh */
    session_protocol_st_size[PROTOCOL_TFTP]=sizeof(struct tftp_session);

    
//    tcp_port_proto[23].port = 23;
//    tcp_port_proto[23].proto = PROTOCOL_TELNET;
    port_add_proto_head(IPPROTO_UDP, 69, PROTOCOL_TFTP);
    port_add_proto_head(IPPROTO_UDP, 6969, PROTOCOL_TFTP);

    udp_detection_array[PROTOCOL_TFTP].proto = PROTOCOL_TFTP;    
    udp_detection_array[PROTOCOL_TFTP].identify_func = identify_tftp;
    udp_detection_array[PROTOCOL_TFTP].dissect_func = dissect_tftp;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_TFTP].excluded_protocol_bitmask, PROTOCOL_TFTP);


    map_fields_info_register(tftp_field_array_sdt,PROTOCOL_TFTP, EM_TFTP_MAX,"tftp");
    return;
}

static __attribute((constructor)) void     before_init_tftp(void){
    register_tbl_array(TBL_LOG_TFTP, 0, "tftp", init_tftp_dissector);
}

