/****************************************************************************************
 * 文 件 名 : dpi_detect.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy           2018/07/06
编码: wangy           2018/07/06
修改: zhangsx         2019/10/25
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <glib.h>
//#include <linux/if_ether.h>
#include <assert.h>

#include "dpi_typedefs.h"
#include "dpi_detect.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_cdp.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_dissector.h"
#include "dpi_err_pcap_dump.h"
#include "dpi_gtp_u.h"

#include "sdt_action_out.h"
#include "sdt_ip_protocols.h"
#include "dpi_sdt_match.h"
#include "dpi_mac_pheader.h"
#include "dpi_sdt_link.h"
#include "dpi_utils.h"
#include "dpi_sctp.h"
#include "dpi_flow_timer.h"
#include "dpi_metrics.h"
#include "dpi_flow.h"
#include "dpi_pschema.h"
#include "dpi_tll.h"
#include "dpi_numa.h"

/* mask for Bad FCF presence */
#define BAD_FCS                           0x50    /* 0101 0000 */

#define  flow_malloc  malloc
#define  flow_free    free

GHashTable *ipv4_filter_table;
extern pthread_mutex_t rdp_mutex;
extern rte_atomic64_t flow_fail_get;
extern GAsyncQueue *sslh_encrypted_pcap;

extern int session_protocol_st_size[PROTOCOL_MAX];

extern struct global_config g_config;
extern struct protocol_record p_record;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];

extern struct rte_mempool *pktmbuf_pool[2];
extern struct rte_ring *packet_flow_ring[RTE_MAX_LCORE];
extern struct rte_ring *flow_aging_ring[RTE_MAX_LCORE];

extern struct ext_proto_st        *prot_tbl;
extern uint16_t                   prot_tbl_num ;
extern uint64_t                   packet_nohit ;
extern uint64_t                   packet_hit ;

extern struct rte_mempool         *tbl_log_mempool;
extern DpiMetrics g_metrics;


/* 同一个解析线程内的包， 解析的完整的协议栈路径，针对一个完整的包，而不是flow */
__thread char    g_protoinfo[MAX_CONTENT_SIZE];
__thread uint16_t g_proto_layer[32];
__thread uint8_t  g_proto_layer_cnt;

/* gtp-u的teid字段 */
__thread uint32_t g_tunnel_id;

extern rte_atomic64_t aging_fail_pkts;
extern rte_atomic64_t flow_hash_fail_pkts;
void write_H323_timeout_log(struct flow_info *flow);
void write_ldap_timeout_log(struct flow_info *flow);
void write_isakmp_timeout_log(struct flow_info *flow);

int  write_flow_log(struct flow_info *flow);
int  write_ah_n_tbl_log(struct flow_info *flow, int direction);
int  write_esp_n_tbl_log(struct flow_info *flow, int direction);
int  dissect_megaco_udp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);

#define LLC_SNAP 0xaa
#define LLC_SAP  0xfe
#define TERDO_IPV6 58
#define LLC      1600

struct int_to_string data_link_layer_proto[] = {
{DPI_HDLC                       ,"CiscoHDLC"},
{DPI_ETH                        ,"ETH"},
{SLARP                         ,"SLARP"},
{CISCO_D_PROTO                 ,"CISCO_D_PROTO"},
{VLAN                          ,"VLAN"},
{MPLS_UNI                      ,"MPLS"},
{MPLS_MULTI                    ,"MPLS"},
{MPLS_PWETHCW                  ,"PWETHCW"},
{PPPoE                         ,"PPPoE"},
{PPPoE_DISCOVERY               ,"PPPoE_DISCOVERY"},
{LLC_SNAP                      ,"CDP"},
{SNAP                          ,"SNAP"},
{BSTP                          ,"BSTP"},
{VMLAB                        ,"VMLAB"},
{LACP                         ,"LACP"},
{LLDP                         ,"LLDP"},
{ETHERTYPE_PPP                ,"PPP"},
{PPP_IP                       ,"IPv4"},
{PPP_IPCP                     ,"IPCP"},
{PPP_LCP                      ,"LCP"},
{PPP_PAP                      ,"PAP"},
{PPP_CHAP                     ,"CHAP"},
{0x22f4                       ,"ISIS"},
{0xfefe                       ,"ISIS"},
{ETH_P_IPV6                   ,"IPv6"},
{ETH_P_IP                     ,"IPv4"},
{IPPROTO_TCP                  ,"TCP"},
{IPPROTO_UDP                  ,"UDP"},
{LLC_SAP                      ,"ISIS"},
{0x42                         ,"STP"},
{ARP                          ,"ARP"},
{TERDO_IPV6                    ,"IPv6"},
{LLC                            ,"LLC"},
{0                            ,NULL},
};

#define FLOW_ARR_MAX 256
__thread struct flow_info *flow_arr[FLOW_ARR_MAX];
__thread int cnt = 0;

static void flow_timer_cb(struct rte_timer *tim, void *arg)
{
  struct flow_info         *flow    = (struct flow_info *)arg;
  struct work_process_data *process = NULL;
  struct rte_mbuf          *mbuf;
  unsigned                  free_space;
  int                       ret    = 0;
  int                       retval = 0;
  int again_ring_id = 0;

  uint8_t thread_id = flow->thread_id;

  process = &flow_thread_info[thread_id];
  process->stats.flow_stats[flow->real_protocol_id]++;
  ret = rte_timer_stop(tim);
  if (ret != 0) {
    printf("timer stop failed, ret = %d!!!\n", ret);
  }

  retval = rte_hash_del_key(process->hash, &flow->tuple_reverse);
  if (retval < 0) DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple_reverse to hash");
  retval = rte_hash_del_key(process->hash, &flow->tuple);
  if (retval < 0) DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple to hash");
  g_metrics.flow[thread_id].del++;
  again_ring_id = flow->thread_id % g_config.flow_aging_thread_num;
  ret = rte_ring_mp_enqueue_burst(flow_aging_ring[again_ring_id], (void *const *)&flow, 1, &free_space);
  if(ret!= 1){
    dpi_flow_timeout(flow);
    DPI_LOG(DPI_LOG_WARNING, "flow 老化队列满 超时当前flow");
  }
  return;
}
/* 拷贝协议栈  */
void append_proto_info_no_real_protocol_id(struct flow_info *flow) {
    memcpy(flow->proto_layer, g_proto_layer, sizeof(g_proto_layer));
    flow->proto_layer_cnt = g_proto_layer_cnt;
}

/* 追加协议栈  */
void append_proto_info(struct flow_info *flow) {

    if (!flow)
        return;
    if(flow->real_protocol_id == PROTOCOL_IPIP)
      return;
    // strcat(g_protoinfo, ".");
    // if(flow->real_protocol_id == PROTOCOL_FTP_CONTROL || flow->real_protocol_id == PROTOCOL_FTP_DATA)
    //     strcat(g_protoinfo, "FTP");
    // else
    //     strcat(g_protoinfo, protocol_name_array[flow->real_protocol_id]);

    g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
    // memset(flow->proto_info, 0, sizeof(flow->proto_info));
    // strcpy(flow->proto_info, g_protoinfo);

    memcpy(flow->proto_layer, g_proto_layer, sizeof(g_proto_layer));
    flow->proto_layer_cnt = g_proto_layer_cnt;
}
/*追加IP层之前的协议栈*/
void append_hardlink_proto_info(int type) {
    g_proto_layer[g_proto_layer_cnt++] = type + PROTOCOL_MAX;
}

static int DPI_BITMASK_COMPARE(DPI_PROTOCOL_BITMASK a, DPI_PROTOCOL_BITMASK b) {
    unsigned int i;
    for(i = 0; i < DPI_NUM_FDS_BITS; i++) {
        if(a.fds_bits[i] & b.fds_bits[i])
            return 1;
    }
    return 0;
}
/* 会话销毁前，如果有待重组的数据包，则做最后一次解析 */
static void tcp_reassemble_do_final(struct flow_info *flow)
{
    if (!list_empty(&flow->reassemble_src2dst_head) && flow->real_protocol_id != PROTOCOL_UNKNOWN) {
        uint8_t reassemble_result[REASSEMBLE_LEN_MAX];
        uint32_t reassemble_result_len = sizeof(reassemble_result) / sizeof(reassemble_result[0]);
        tcp_reassemble_do(&flow->reassemble_src2dst_head, reassemble_result, &reassemble_result_len);

        if (flow->tuple.inner.proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func) {
            tcp_detection_array[flow->real_protocol_id].dissect_func(flow, FLOW_DIR_SRC2DST, 0, reassemble_result, reassemble_result_len, DISSECT_PKT_FIANL);
            g_proto_layer_cnt = 0;
        }

        tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
    }

    if (!list_empty(&flow->reassemble_dst2src_head) && flow->real_protocol_id != PROTOCOL_UNKNOWN) {
        uint8_t reassemble_result[REASSEMBLE_LEN_MAX];
        uint32_t reassemble_result_len = sizeof(reassemble_result) / sizeof(reassemble_result[0]);
        tcp_reassemble_do(&flow->reassemble_dst2src_head, reassemble_result, &reassemble_result_len);

        if (flow->tuple.inner.proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func) {
            tcp_detection_array[flow->real_protocol_id].dissect_func(flow, FLOW_DIR_DST2SRC, 0, reassemble_result, reassemble_result_len, DISSECT_PKT_FIANL);
            g_proto_layer_cnt = 0;
        }

        tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);
    }

    return;
}

struct decode_t *decode_port_on_tcp_array [65535][PORT_PROTO_NUM_MAX];
struct decode_t *decode_port_on_udp_array [65535][PORT_PROTO_NUM_MAX];
struct decode_t *decode_port_on_sctp_array[65535][PORT_PROTO_NUM_MAX];

/*向某个端口注册协议，先注册的协议先匹配*/
int decode_on_port_tcp(uint16_t port, struct decode_t *decode)
{
    for(int i = 0; i < PORT_PROTO_NUM_MAX; i++)
    {
        if(NULL == decode_port_on_tcp_array[port][i])
        {
            decode_port_on_tcp_array[port][i] = decode;
            return 0;
        }
    }
    return -1;
}
int decode_on_port_udp(uint16_t port, struct decode_t *decode)
{
    for(int i = 0; i < PORT_PROTO_NUM_MAX; i++)
    {
        if(NULL == decode_port_on_udp_array[port][i])
        {
            decode_port_on_udp_array[port][i] = decode;
            return 0;
        }
    }
    return -1;
}
int decode_on_port_sctp(uint16_t port, struct decode_t *decode)
{
    for(int i = 0; i < PORT_PROTO_NUM_MAX; i++)
    {
        if(NULL == decode_port_on_sctp_array[port][i])
        {
            decode_port_on_sctp_array[port][i] = decode;
            return 0;
        }
    }
    return -1;
}

/*向某个端口注册协议，先注册的协议先匹配*/
void port_add_proto_head(uint8_t tcp_or_udp, uint16_t port, uint16_t protocol)
{
    int i;
    struct guess_proto_data *array;

    if (tcp_or_udp == IPPROTO_TCP)
        array = tcp_port_proto;
    else if (tcp_or_udp == IPPROTO_UDP)
        array = udp_port_proto;
    else if (tcp_or_udp == IPPROTO_SCTP)
        array = sctp_port_proto;
    else {
        DPI_LOG(DPI_LOG_DEBUG, "unknown ip protocol");
        return;
    }

    if (array[port].proto[PORT_PROTO_NUM_MAX - 1] != 0) {
        DPI_LOG(DPI_LOG_WARNING, "port %u has already registered %d protocol", port, PORT_PROTO_NUM_MAX);
        printf("port %u has already registered %d protocol", port, PORT_PROTO_NUM_MAX);
        return;
   }

    for(i = 0; i < PORT_PROTO_NUM_MAX; i++){
        if(array[port].proto[i] == 0){
            array[port].proto[i] = protocol;
            break;
        }
    }

    return;
}

static int identify_func_on_port(struct flow_info *flow, const unsigned char *payload, unsigned short paylod_len)
{
    int i;
    struct guess_proto_data *port_array;
    uint16_t sport = ntohs(flow->tuple.inner.port_src);
    uint16_t dport = ntohs(flow->tuple.inner.port_dst);
    uint16_t little_port = sport > dport ? dport : sport;
    struct check_proto_data *detection_array;

    if (flow->tuple.inner.proto == IPPROTO_TCP)
    {
        port_array = tcp_port_proto;
        detection_array = tcp_detection_array;
    }
    else
    if (flow->tuple.inner.proto == IPPROTO_UDP)
    {
        port_array = udp_port_proto;
        detection_array = udp_detection_array;
    }
    else
    if (flow->tuple.inner.proto == IPPROTO_SCTP)
    {
        port_array = sctp_port_proto;
        detection_array = sctp_detection_array;
    }
    else
    {
        return -1;
    }

    /*先识别小端口上最可能的协议，每个端口上可注册多个协议*/
    for (i = 0; i < PORT_PROTO_NUM_MAX; i++)
    {
        int app_proto = port_array[little_port].proto[i];
        if (app_proto == 0)
        {
            break;
        }

        if (g_config.protocol_switch[app_proto] == 0)
        {
            continue;
        }

        detection_array[app_proto].identify_func(flow, payload, paylod_len);
        if (flow->real_protocol_id != PROTOCOL_UNKNOWN)
        {
            return 0;
        }
    }
    return -1;
}

/**
* 内容识别
*/
static int identify_func_on_list(struct flow_info *flow, const unsigned char *payload, unsigned short paylod_len)
{
    int i;
    struct check_proto_data *detection_array;
    uint16_t sport = ntohs(flow->tuple.inner.port_src);
    uint16_t dport = ntohs(flow->tuple.inner.port_dst);
    // uint16_t little_port = sport > dport ? dport : sport;

    if (flow->tuple.inner.proto == IPPROTO_TCP)
    {
        detection_array = tcp_detection_array;
    }
    else
    if (flow->tuple.inner.proto == IPPROTO_UDP)
    {
        detection_array = udp_detection_array;
    }
    else
    if (flow->tuple.inner.proto == IPPROTO_SCTP)
    {
        detection_array = sctp_detection_array;
    }
    else
    {
        return -1;
    }

    for (i = 0; i < PROTOCOL_MAX; i++)
    {
        if (g_config.protocol_switch[i] == 0)
        {
            continue;
        }

        // 跳过仅端口识别的协议
        if (detection_array[i].identify_type == DPI_IDENTIFY_PORT) {
            continue;
        }

        if(NULL == detection_array[i].identify_func)
        {
            continue;
        }

        detection_array[i].identify_func(flow, payload, paylod_len);
        if (flow->real_protocol_id != PROTOCOL_UNKNOWN)
        {
            return 0;
        }
    }
    return -1;
}

static int identify_func_on_decode_port_tcp(struct flow_info *flow, unsigned int port, uint8_t C2S, const unsigned char *payload, unsigned short paylod_len)
{
    int protocol_id = 0;
    for(int i = 0; i < PORT_PROTO_NUM_MAX; i++)
    {
        struct decode_t *decode = decode_port_on_tcp_array[port][i];
        if(decode)
        {
            protocol_id  = decode->pkt_identify(flow, C2S, payload, paylod_len);
            if (protocol_id != PROTOCOL_UNKNOWN)
            {
                flow->real_protocol_id = protocol_id;
                flow->decode = decode;
                return 0;
            }
        }
    }
    return -1;
}

static int identify_func_on_decode_port_udp(struct flow_info *flow, unsigned int port, uint8_t C2S, const unsigned char *payload, unsigned short paylod_len)
{
    int protocol_id = 0;
    for(int i = 0; i < PORT_PROTO_NUM_MAX; i++)
    {
        struct decode_t *decode = decode_port_on_udp_array[port][i];
        if(decode)
        {
            protocol_id  = decode->pkt_identify(flow, C2S, payload, paylod_len);
            if (protocol_id != PROTOCOL_UNKNOWN)
            {
                flow->real_protocol_id = protocol_id;
                flow->decode = decode;
                return 0;
            }
        }
    }
    return -1;
}

static int identify_func_on_decode_port_sctp(struct flow_info *flow, unsigned int port, uint8_t C2S, const unsigned char *payload, unsigned short paylod_len)
{
    int protocol_id = 0;
    for(int i = 0; i < PORT_PROTO_NUM_MAX; i++)
    {
        struct decode_t *decode = decode_port_on_sctp_array[port][i];
        if(decode)
        {
            protocol_id  = decode->pkt_identify(flow, C2S, payload, paylod_len);
            if (protocol_id != PROTOCOL_UNKNOWN)
            {
                flow->real_protocol_id = protocol_id;
                flow->decode = decode;
                return 0;
            }
        }
    }
    return -1;
}

static int identify_func_on_decode_port(struct flow_info *flow, const unsigned char *payload, unsigned short paylod_len)
{
    uint16_t sport = ntohs(flow->tuple.inner.port_src);
    uint16_t dport = ntohs(flow->tuple.inner.port_dst);
    uint16_t little_port = sport > dport ? dport : sport;
    uint8_t C2S =   sport > dport;

    if (flow->tuple.inner.proto == IPPROTO_TCP)
    {
        return identify_func_on_decode_port_tcp(flow, little_port, C2S, payload, paylod_len);
    }
    else
    if (flow->tuple.inner.proto == IPPROTO_UDP)
    {
        return identify_func_on_decode_port_udp(flow, little_port, C2S, payload, paylod_len);
    }
    else
    if (flow->tuple.inner.proto == IPPROTO_SCTP)
    {
        return identify_func_on_decode_port_sctp(flow, little_port, C2S, payload, paylod_len);
    }
    return -1;
}

static int identify_func_on_decode(struct flow_info *flow, const unsigned char *payload, unsigned short paylod_len)
{
    uint16_t sport = ntohs(flow->tuple.inner.port_src);
    uint16_t dport = ntohs(flow->tuple.inner.port_dst);

    if (flow->tuple.inner.proto == IPPROTO_TCP)
    {
        //新版协议识别函数
        for (int i = 0; decode[i]; i++)
        {
            if (decode[i]->identify_type == DPI_IDENTIFY_PORT) {
                continue;
            }
            int protocol_id  = decode[i]->pkt_identify(flow, sport > dport, payload, paylod_len);
            if (protocol_id != PROTOCOL_UNKNOWN)
            {
                flow->real_protocol_id = protocol_id;
                flow->decode = decode[i];
                return 0;
            }
        }
    }
    //TODO 未知协议

    return -1;
}

static int identify_func_on_extern(struct flow_info *flow, const unsigned char *payload, unsigned short paylod_len)
{
    int ret=0;
    int i = 0;
    if(flow->real_protocol_id == PROTOCOL_UNKNOWN)
    {
        for(i=0; i<prot_tbl_num; i++)
        {
            ret=prot_tbl[i].proto_identify(flow->port_src, flow->port_dst, payload, paylod_len);
            if(ret==1)
            {
                flow->real_protocol_id=prot_tbl[i].protocol_id;
                return 0;
            }
        }
    }
    return -1;
}


/* 协议识别的主函数 */
void check_dpi_flow_func(struct flow_info *flow, const unsigned char *payload, const unsigned short paylod_len)
{
    int err = 0;

    err = identify_func_on_port(flow, payload, paylod_len);
    if(0 == err)
    {
        return;
    }

    err = identify_func_on_list(flow, payload, paylod_len);
    if(0 == err)
    {
        return;
    }

    err = identify_func_on_decode_port(flow, payload, paylod_len);
    if(0 == err)
    {
        return;
    }

    err = identify_func_on_decode(flow, payload, paylod_len);
    if(0 == err)
    {
        return;
    }

    err = identify_func_on_extern(flow, payload, paylod_len);
    if(0 == err)
    {
        return;
    }
}

#define SET_IP_FLOW_TUPLE(tuple, protocol, version, srcip, dstip, srcport, dstport, _sctp_id)   \
            {                                                                                   \
                tuple.proto = protocol;                                                         \
                tuple.ip_version = version;                                                     \
                memcpy(tuple.ip_src, srcip, (version - 4) * 7 + 4);                             \
                memcpy(tuple.ip_dst, dstip, (version - 4) * 7 + 4);                             \
                tuple.port_src = srcport;                                                       \
                tuple.port_dst = dstport;                                                       \
                tuple.sctp_id  = _sctp_id;                                                      \
            }

#define SET_IPV4_FLOW_TUPLE(tuple, protocol, srcip, dstip, srcport, dstport, _sctp_id)   \
            {                                                                            \
                tuple.proto = protocol;                                                  \
                tuple.ip_version = 4;                                                    \
                tuple.ip_src.ip4 = srcip;                                                \
                tuple.ip_dst.ip4 = dstip;                                                \
                tuple.port_src = srcport;                                                \
                tuple.port_dst = dstport;                                                \
                tuple.sctp_id = _sctp_id;                                                \
            }

#define SET_IPV6_FLOW_TUPLE(tuple, protocol, srcip, dstip, srcport, dstport, _sctp_id)   \
            {                                                                            \
                tuple.proto = protocol;                                                  \
                tuple.ip_version = 6;                                                    \
                memcpy(tuple.ip_src.ip6, srcip, sizeof(tuple.ip_src.ip6));               \
                memcpy(tuple.ip_dst.ip6, dstip, sizeof(tuple.ip_dst.ip6));               \
                tuple.port_src = srcport;                                                \
                tuple.port_dst = dstport;                                                \
                tuple.sctp_id = _sctp_id;                                                \
            }

const unsigned char weight[256] =
{ 0, 1, 1, 2, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 3, 4,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
  4, 5, 5, 6, 5, 6, 6, 7, 5, 6, 6, 7, 6, 7, 7, 8 };

void flow_bit_info(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (flow == NULL) {
        return;
    }
    if (payload == NULL || payload_len == 0) {
        return;
    }

    for (int i = 0; i < payload_len; ++i) {
        uint8_t tmp = payload[i];
        flow->flow_bit1_num += weight[tmp];
    }
    flow->flow_bit_total += (payload_len * 8);

    return;
}

const char *filetype_flow(const char*p, int l)
{
    if (p == NULL || l == 0) {
        return NULL;
    }

    struct
    {
        const char *prefix;
        int            len;
        int            offset;
        const char *detail;
    } file_hdr[] = {
        {"\x37\x7A\xBC\xAF\x27\x1C", 6, 0, "7Z"},
        {"\xFD\x37\x7A\x58\x5A\x00\x00\x04", 8, 0, "tar.xz"},
        {"\x1F\x9D", 2, 0, "tar.z"},
        {"\x1F\xA0", 2, 0, "tar.z"},
        {"\x78\x01\xED\x9D\x0B\x94\x1C\x57", 8, 0, "zlib"},
        {"\x1F\x8B\x08", 3, 0, "gz"},
        {"\x1F\x8B\x08", 3, 0, "tgz"},
        {"\x50\x4B\x03\x04", 4, 0, "apk"},
        {"\x50\x4B\x03\x04", 4, 0, "jar"},
        {"\x50\x4B\x03\x04", 4, 0, "zip"},
        {"\x50\x4B\x05\x06", 4, 0, "zip"},
        {"\x50\x4B\x07\x08", 4, 0, "zip"},
        {"\x52\x61\x72\x21\x1A\x07", 6, 0, "rar"},
        {"\x42\x5A\x68", 3, 0, "bz2"},
        {"\x42\x5A\x68", 3, 0, "tar.bz2"},
        {"\x42\x5A\x68", 3, 0, "tbz2"},
        {"\x42\x5A\x68", 3, 0, "tb2"},
        {"\x42\x5A\x68", 3, 0, "dng"},
        {"\x4D\x53\x43\x01", 4, 0, "cab"},
        {"\x4D\x53\x43\x01", 4, 0, "ppz"},
        {"\x4D\x53\x43\x01", 4, 0, "snp"},
        {"\x21\x12", 2, 0, "ain"},
        {"\x41\x72\x43\x01", 4, 0, "arc"},
        {"\x4B\x47\x42\x5F\x61\x72\x63\x68", 8, 0, "dgb"},
        {"\x2D\x6C\x68", 3, 2, "lha"},
        {"\x2D\x6C\x68", 3, 2, "lzh"},
        {"\x50\x41\x43\x4B", 4, 0, "pak"},
        {"\xED\xAB\xEE\xDB", 4, 0, "rpm"},
        {"\xFD\x37\x7A\x58\x5A\x00", 6, 0, "xz"},
        {"\x5A\x4F\x4F\x20", 4, 0, "zoo"},
        {"\x75\x73\x74\x61\x72\x00\x30\x30", 8, 0, "tar"},
        {"\x75\x73\x74\x61\x72\x20\x20\x00", 8, 0, "tar"},


        {NULL, 0, 0, NULL},
    };

    for(int i = 0; 0 != file_hdr[i].len; i++)
    {
        if(l >= file_hdr[i].len+file_hdr[i].offset && 0 == memcmp(p+file_hdr[i].offset, file_hdr[i].prefix, file_hdr[i].len))
        {
            return file_hdr[i].detail;
        }
    }
    return NULL;
}

int data_link_layer_copy(struct flow_info *dst_newflow, struct work_process_data *src_workflow)
{
    struct flow_info *newflow = dst_newflow;
    struct work_process_data *workflow = src_workflow;

    newflow->is_mpls   = workflow->is_mpls;
    newflow->vlan_flag = workflow->vlan_flag;
    newflow->data_link_layer.vlan_id[0]=workflow->data_link_layer.vlan_id[0];
    newflow->data_link_layer.vlan_id[1]=workflow->data_link_layer.vlan_id[1];
    newflow->data_link_layer.mpls_label[0]=workflow->data_link_layer.mpls_label[0];
    newflow->data_link_layer.mpls_label[1]=workflow->data_link_layer.mpls_label[1];
    newflow->data_link_layer.mpls_label[2]=workflow->data_link_layer.mpls_label[2];
    newflow->data_link_layer.mpls_label[3]=workflow->data_link_layer.mpls_label[3];
    newflow->ip_link_type = workflow->ip_link_type;
    newflow->routers_flag = workflow->routers_flag;
    newflow->handshake = workflow->handshake;
    newflow->flow_cycle = workflow->flow_cycle;

    return 0;
}


/*依据五元组Hash查找流结构体,找不到则创建,创建会话函数会把正反两个方向都会加入到hash表中*/
static struct flow_info* find_or_create_flow(struct work_process_data * workflow,
                const struct five_tuple *outer, const void *ip4_or_ip6, uint8_t proto,
                uint16_t sport, uint16_t dport, uint16_t sctp_id, uint8_t *src_to_dst_direction)
{
    const uint8_t *src_ip = NULL;
    const uint8_t *dst_ip = NULL;
    const struct dpi_iphdr   *iph  = NULL;
    const struct dpi_ipv6hdr *iph6 = NULL;
    uint8_t version = (*((const uint8_t*)ip4_or_ip6)) >> 4;
    if(version == 4){
        iph = ip4_or_ip6;
        src_ip = iph->saddr;
        dst_ip = iph->daddr;
    }
    else if(version == 6){
        iph6 = ip4_or_ip6;
        src_ip = iph6->ip6_src;
        dst_ip = iph6->ip6_dst;
    }
    else{
        return NULL;
    }

    struct flow_key key;
    memset(&key, 0, sizeof(key));

    /*赋值五元组信息*/
    if(outer)
    {
        SET_IP_FLOW_TUPLE(key.outer,  outer->proto, outer->ip_version, outer->ip_src, outer->ip_dst, outer->port_src, outer->port_dst, sctp_id)
    }

    SET_IP_FLOW_TUPLE(key.inner, proto, version, src_ip, dst_ip, sport, dport, sctp_id)

    struct flow_info *flow_find = NULL;
    int pos = dpi_flow_hash_lookup(workflow->hash, &key, (void **)&flow_find);

    if (unlikely(workflow->is_tll)) {
        if (pos < 0) {
            // 通联日志只有 udp 报文，如果 udp 流表中没有找到，继续找 tcp 流表
            SET_IP_FLOW_TUPLE(key.inner, IPPROTO_TCP, version, src_ip, dst_ip, sport, dport, sctp_id)
            pos = dpi_flow_hash_lookup(workflow->hash, &key, (void **)&flow_find);
        }

        return pos >= 0 ? flow_find : NULL;
    }

    if(NULL == flow_find) {
        struct flow_info *newflow = dpi_flow_create();
        if (newflow == NULL) { return NULL; }

        rte_atomic16_init(&newflow->ref);
        rte_atomic16_inc(&newflow->ref);

        g_metrics.flow[workflow->thread_id].total += 1;

        /*flow中的链表需要初始化*/
        // INIT_LIST_HEAD(&newflow->node_timeout);
        INIT_LIST_HEAD(&newflow->reassemble_src2dst_head);
        INIT_LIST_HEAD(&newflow->reassemble_dst2src_head);

        INIT_LIST_HEAD(&newflow->sdt_flow.pkt_stream_head[FLOW_DIR_SRC2DST]); //测试用
        INIT_LIST_HEAD(&newflow->sdt_flow.pkt_stream_head[FLOW_DIR_DST2SRC]); //测试用
        INIT_LIST_HEAD(&newflow->sdt_flow.single_stream_head[FLOW_DIR_SRC2DST]); //测试用
        INIT_LIST_HEAD(&newflow->sdt_flow.single_stream_head[FLOW_DIR_DST2SRC]); //测试用

        INIT_LIST_HEAD(&newflow->sdt_flow.tcp_rsm_head[FLOW_DIR_SRC2DST]); //测试用
        INIT_LIST_HEAD(&newflow->sdt_flow.tcp_rsm_head[FLOW_DIR_DST2SRC]); //测试用

        dpi_flow_timer_init(workflow, newflow);

        //将newflow的数据链路层 复制 workflow
        data_link_layer_copy(newflow, workflow);

        newflow->timeout_flag = 0;
        newflow->create_time = g_config.g_now_time_usec;
        newflow->end_time = g_config.g_now_time_usec;
        workflow->flow_cycle += 1;
        newflow->flow_id = g_config.g_now_time_usec;
        newflow->intflag   = 1;

        if (outer) {
            SET_IP_FLOW_TUPLE(newflow->tuple.outer,         outer->proto, outer->ip_version, outer->ip_src, outer->ip_dst, outer->port_src, outer->port_dst, sctp_id)
            SET_IP_FLOW_TUPLE(newflow->tuple_reverse.outer, outer->proto, outer->ip_version, outer->ip_dst, outer->ip_src, outer->port_dst, outer->port_src, sctp_id)
        }

        if (ntohs(sport) > ntohs(dport)){
            SET_IP_FLOW_TUPLE(newflow->tuple.inner,         proto, version, src_ip, dst_ip, sport, dport, sctp_id)
            SET_IP_FLOW_TUPLE(newflow->tuple_reverse.inner, proto, version, dst_ip, src_ip, dport, sport, sctp_id)
        }
        else{
            SET_IP_FLOW_TUPLE(newflow->tuple.inner,         proto, version, dst_ip, src_ip, dport, sport, sctp_id)
            SET_IP_FLOW_TUPLE(newflow->tuple_reverse.inner, proto, version, src_ip, dst_ip, sport, dport, sctp_id)
        }

        newflow->ip_version = version;
        newflow->l3         = ip4_or_ip6;
        newflow->thread_id  = workflow->thread_id;
        newflow->linktype   = version == 4 ? DLT_IPV4 : DLT_IPV6;
        newflow->proto_type = version == 4 ? iph->protocol : iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
        newflow->ttl = version == 4 ? iph->ttl : iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim;
        newflow->ip_len     = version == 4 ? ntohs(iph->tot_len) : sizeof(struct dpi_ipv6hdr) + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);

        newflow->port_src=ntohs(sport);
        newflow->port_dst=ntohs(dport);
        newflow->sdt_flow.pEngine = workflow->pEngine;
        newflow->memAc  = alloc_init();
        if (ntohs(sport) > ntohs(dport)){
            newflow->direction = FLOW_DIR_SRC2DST;
            *src_to_dst_direction = FLOW_DIR_SRC2DST;
            //newflow->src2dst_payload_len += workflow->payload_len;

        } else {
            newflow->direction = FLOW_DIR_DST2SRC;
            *src_to_dst_direction = FLOW_DIR_DST2SRC;
            //newflow->dst2src_payload_len += workflow->payload_len;
        }
        newflow->dst2src_max_packet_len      = 0;
        newflow->dst2src_min_packet_len      = g_config.max_pkt_len+1;
        newflow->dst2src_max_packet_interval = 0;
        newflow->dst2src_min_packet_interval = g_config.tcp_flow_timeout*1000000+1; // 微妙

        newflow->src2dst_max_packet_len      = 0;
        newflow->src2dst_min_packet_len      = g_config.max_pkt_len+1;
        newflow->src2dst_max_packet_interval = 0;
        newflow->src2dst_min_packet_interval = g_config.tcp_flow_timeout*1000000+1; // 微妙

         //避免327 标签值为空
        if(g_config._327_common_switch){
            memcpy(&newflow->p327_header[*src_to_dst_direction], &workflow->p327_header, sizeof(workflow->p327_header));
        }

        int rc = 0;
        /*正反两个方向的五元组都加入到hash表中，这样无论哪个方向的数据包都能关联到会话*/
        // g_hash_table_insert(workflow->hash, &newflow->tuple, newflow);
        // g_hash_table_insert(workflow->hash, &newflow->tuple_reverse, newflow);
        rc |= dpi_flow_hash_insert(workflow->hash, &newflow->tuple, newflow);
        rc |= dpi_flow_hash_insert(workflow->hash, &newflow->tuple_reverse, newflow);
        workflow->stats.flow_count++;
        //printf("insert thread_id %u hash_size %u flow_count %u\n", newflow->thread_id, g_hash_table_size(workflow->hash), workflow->stats.flow_count);
        workflow->stats.inc_flow_num++;
        workflow->num_allocated_flows++;
        workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]++;

        if(rc)
        {
            log_warn("dpi_flow_hash_insert: 内存空间耗尽 %u", g_config.max_hash_node_per_thread);
            rte_atomic64_inc(&flow_hash_fail_pkts);
            dpi_flow_hash_remove(workflow->hash, &newflow->tuple);
            dpi_flow_hash_remove(workflow->hash, &newflow->tuple_reverse);
            dpi_flow_free(newflow, NULL);
            return NULL;
        }
        return newflow;
    } else {
        if ( (memcmp(flow_find->tuple.inner.ip_src, src_ip, ((version - 4) * 7 + 4)) == 0) && flow_find->tuple.inner.port_src == sport )
        {
            *src_to_dst_direction = FLOW_DIR_SRC2DST;
        } else {
            *src_to_dst_direction = FLOW_DIR_DST2SRC;
        }
        flow_find->port_src = ntohs(sport);
        flow_find->port_dst = ntohs(dport);
        flow_find->l3           = ip4_or_ip6;
        flow_find->handshake    = workflow->handshake;
        flow_find->ip_link_type = workflow->ip_link_type;
        flow_find->routers_flag = workflow->routers_flag;
        flow_find->ip_len       = version == 4 ? ntohs(iph->tot_len) : sizeof(struct dpi_ipv6hdr) + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
        flow_find->ttl          = version == 4 ? iph->ttl : iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim;
        return flow_find;
    }
}

extern struct int_to_string chunk_type_values[];
extern int dissect_S1AP_PDU_PDU(struct flow_info *flow, int direction, struct dpi_pkt_st *pkt);

#if 0
static void set_tcp_stat_info(struct flow_info *flow, int direction, const struct dpi_tcphdr *tcph)
{
    if (tcph->syn && tcph->ack) { /*second handshaking*/
        flow->ack_tcp_session[direction] = 1;  /*检测是谁响应连接*/
        if (flow->tcp_status == FLOW_SYN)
            flow->tcpsynacktime = g_config.g_now_time_usec;
        flow->tcp_status = FLOW_SYN_ACK;

        flow->up_window = tcph->window;
        flow->up_seq = tcph->seq;
        flow->down_ttl = flow->ttl;

        if( g_config.flow_log_switch && (flow->synackData == NULL) && (flow->synackData = malloc(flow->ip_len)) ){
            memcpy(flow->synackData, flow->l3, flow->ip_len);
            flow->synackDataLen = flow->ip_len;
        }

    }
    else if (tcph->syn) {        /*first handshaking*/
        flow->tcpsyncounter++;
        flow->init_tcp_session[direction] = 1;  /*检测是谁发起连接*/
        if (flow->tcp_status < 1)
            flow->tcpsyntime = g_config.g_now_time_usec;
        flow->tcp_status = FLOW_SYN;

        flow->down_window = tcph->window;
        flow->down_seq = tcph->seq;
        flow->down_ttl = flow->ttl;

        if( g_config.flow_log_switch && (flow->synData == NULL) && (flow->synData = malloc(flow->ip_len)) ){
            memcpy(flow->synData, flow->l3, flow->ip_len);
            flow->synDataLen = flow->ip_len;
        }

    }

    if (tcph->rst)
        flow->tcprstcounter++;

    if (tcph->fin)
        flow->tcpfincounter++;

    if (tcph->urg)
        flow->tcpurgcounter++;

    if (tcph->psh)
        flow->tcppshcounter++;

    if (tcph->ack)
        flow->tcpackcounter++;
}
#else
static void set_tcp_stat_info(struct flow_info *flow, int direction, const struct dpi_tcphdr *tcph)
{
    if (tcph->syn && tcph->ack) { /*second handshaking  S2C */
        flow->ack_tcp_session[direction] = 1;  /*检测是谁响应连接*/
        if (flow->tcp_status == FLOW_SYN)
            flow->tcpsynacktime = g_config.g_now_time_usec;
        flow->tcp_status = FLOW_SYN_ACK;

        flow->down_window = ntohs(tcph->window);
        flow->down_seq    = ntohl(tcph->seq);
        flow->down_ttl    = flow->ttl;

        if( (flow->synackData == NULL) && (flow->synackData = malloc(flow->ip_len)) ){
            memcpy(flow->synackData, flow->l3, flow->ip_len);
            flow->synackDataLen = flow->ip_len;
        }
        if(flow->tuple.inner.port_src==tcph->source){
            flow->flow_direction_flag=2;  // tuple_reverse 五元组表示  c2s
        }else{
            flow->flow_direction_flag=1;  // tuple 五元组表示  c2s
        }
    }
    else if (tcph->syn) {        /*first handshaking   C2S*/
        flow->tcpsyncounter++;
        flow->init_tcp_session[direction] = 1;  /*检测是谁发起连接*/
        if (flow->tcp_status < 1)
            flow->tcpsyntime = g_config.g_now_time_usec;
        flow->tcp_status = FLOW_SYN;

        flow->up_window = ntohs(tcph->window);
        flow->up_seq    = ntohl(tcph->seq);
        flow->up_ttl    = flow->ttl;

        if( (flow->synData == NULL) && (flow->synData = malloc(flow->ip_len)) ){
            memcpy(flow->synData, flow->l3, flow->ip_len);
            flow->synDataLen = flow->ip_len;
        }

        if(flow->tuple.inner.port_src==tcph->source){
            flow->flow_direction_flag=1;  // tuple 五元组表示  c2s
        }else{
            flow->flow_direction_flag=2;  // tuple_reverse 五元组表示  c2s
        }
    }

    if (tcph->rst)
        flow->tcprstcounter++;

    if (tcph->fin)
        flow->tcpfincounter++;

    if (tcph->urg)
        flow->tcpurgcounter++;

    if (tcph->psh)
        flow->tcppshcounter++;

    if (tcph->ack)
        flow->tcpackcounter++;

    if(tcph->ece)
        flow->tcpececounter++;

    if(tcph->cwr)
        flow->tcpcwrcounter++;

    if(tcph->res1&0x1)
        flow->tcpnscounter++;

    if(tcph->syn && tcph->ack)
        flow->tcpsynackcounter++;
}



#endif

static int identify_non_app_protocol(struct flow_info *flow, int proto)
{
    switch(proto){
    case IPPROTO_ICMP:
    case IPPROTO_ICMPV6:
        flow->real_protocol_id = PROTOCOL_ICMP;
        break;
    case IPPROTO_SCTP:
        flow->real_protocol_id = PROTOCOL_SCTP;
        break;
    case IPPROTO_OSPF:
        flow->real_protocol_id = PROTOCOL_OSPF;
        break;
    case IPPROTO_ESP:
        flow->real_protocol_id = PROTOCOL_ESP;
        break;
    case IPPROTO_GRE:
        flow->real_protocol_id = PROTOCOL_GRE;
        break;
    case IPPROTO_AH:
        flow->real_protocol_id = PROTOCOL_AH;
        break;
    case IPPROTO_EIGRP:
        flow->real_protocol_id = PROTOCOL_EIGRP;
        break;
    case IPPROTO_RSVP:
        flow->real_protocol_id = PROTOCOL_RSVP;
        break;
    case IPPROTO_VRRP:
        flow->real_protocol_id = PROTOCOL_VRRP;
        break;
    case IPPROTO_IPIP:
    case IPPROTO_IPV6:
        flow->real_protocol_id = PROTOCOL_IPIP;
        break;
    case IPPROTO_IGMP:
        flow->real_protocol_id = PROTOCOL_IGMP;
        break;
    case IPPROTO_L2TP:
        flow->real_protocol_id = PROTOCOL_L2TP;
        break;
    default:
        return 0;
        break;
    }
    return 1;
}


static int identify_tunnel_protocol(uint16_t realproto)
{
    switch (realproto) {
    case PROTOCOL_GTP_U:
    case PROTOCOL_GRE:
    case PROTOCOL_IPIP:
    case PROTOCOL_L2TP:
    case PROTOCOL_TEREDO:
        return 1;
    default:
        return 0;
    };
}

static void set_flow_pkt_distribution(struct flow_info *flow, uint8_t direction, uint16_t rawsize)
{
    flow->disbt_index += 1;
    if (flow->disbt_index >= PKT_DISTRIBUTION_NUM)
        return;
    if (direction == flow->direction)
        flow->distribution[flow->disbt_index].direction = 0;
    else
        flow->distribution[flow->disbt_index].direction = 1;
    flow->distribution[flow->disbt_index].len = rawsize;
    flow->distribution[flow->disbt_index].time = flow->timestamp / 1e6;

    return;
}

/*直接位于IP层之上的应用协议根据协议号解析*/
static int dissect_l4_protocol(struct flow_info* flow, int direction, const uint8_t *payload, uint16_t payload_len, uint8_t origin_flag, uint8_t proto)
{
    memset(flow->proto_info, 0, array_length(flow->proto_info));

    switch(proto){
    case IPPROTO_ICMP:
    case IPPROTO_ICMPV6:
        flow->real_protocol_id = PROTOCOL_ICMP;
        g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
        append_proto_info(flow); //延续 旧的 协议层次 append
        dpi_dissect_icmp(flow, direction, payload, payload_len, origin_flag);
        break;
    case IPPROTO_SCTP:
        flow->real_protocol_id = PROTOCOL_SCTP;
        g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
        append_proto_info(flow); //延续 旧的 协议层次 append
        dpi_dissect_sctp(flow, direction, payload, payload_len, origin_flag);
        break;
    case IPPROTO_ESP:
        flow->real_protocol_id = PROTOCOL_ESP;
        g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
        append_proto_info(flow); //延续 旧的 协议层次 append
        dpi_dissect_esp(flow, direction, payload, payload_len, origin_flag);
        break;
    case IPPROTO_GRE:
        flow->real_protocol_id = PROTOCOL_GRE;
        g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
        append_proto_info(flow); //延续 旧的 协议层次 append
        dpi_dissect_gre(flow, direction, payload, payload_len, origin_flag);
        break;
    case IPPROTO_AH:
        flow->real_protocol_id = PROTOCOL_AH;
        g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
        append_proto_info(flow); //延续 旧的 协议层次 append
        dpi_dissect_ah(flow, direction, payload, payload_len, origin_flag);
        break;
    case IPPROTO_IPIP:
    case IPPROTO_IPV6:
        flow->real_protocol_id = PROTOCOL_IPIP;
        g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
        append_proto_info(flow); //延续 旧的 协议层次 append
        dpi_dissect_ipip(flow, direction, payload, payload_len, origin_flag);
		break;
    case IPPROTO_L2TP:
        flow->real_protocol_id = PROTOCOL_L2TP;
        g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
        append_proto_info(flow); //延续 旧的 协议层次 append
        dissect_l2tp_ip(flow, direction, payload, payload_len, origin_flag);
        break;
    default:
        break;
    }

    // if (flow->real_protocol_id != PROTOCOL_UNKNOWN) {
    //     g_proto_layer[g_proto_layer_cnt++] = flow->real_protocol_id;
    // }
    return 0;
}


static void process_non_app_protocol(struct work_process_data *workflow,
                                    struct flow_info* flow, uint8_t proto,
                                    const uint8_t *payload, uint16_t payload_len)
{
    int yes = identify_non_app_protocol(flow, proto);

    if (flow->real_protocol_id == PROTOCOL_UNKNOWN ||
        g_config.protocol_switch[flow->real_protocol_id] == 0)
    {
        return;
    }

    //如果
    if (yes)
    {
        dissect_l4_protocol(flow, flow->direction, payload, payload_len, DISSECT_PKT_ORIGINAL, proto);
    }
    else
    if (identify_tunnel_protocol(flow->real_protocol_id))  //隧道协议放行解析，在write tbl log处拦截
    {
        dissect_l4_protocol(flow, flow->direction, payload, payload_len, DISSECT_PKT_ORIGINAL, proto);
    }
}


/*查找或创建流的准备工作:获取端口*/
static struct flow_info *dpi_find_create_flow_ip(struct work_process_data * workflow,
                const struct five_tuple *outer,
                const void *ip4_or_ip6,
                uint16_t ipsize,
                const struct dpi_tcphdr **tcph,
                const struct dpi_udphdr **udph,
                uint16_t *sport, uint16_t *dport,
                uint8_t *proto,
                const uint8_t **payload,
                uint16_t *payload_len,
                uint8_t *src_to_dst_direction)
{

    const uint8_t *l4;
    uint16_t l4_offset = 0;
    uint16_t l4_packet_len;

    const struct dpi_iphdr   *iph  = NULL;
    const struct dpi_ipv6hdr *iph6 = NULL;

    const uint8_t *data_start = ip4_or_ip6;
    const uint8_t version = *((const uint8_t*)ip4_or_ip6) >> 4;
    if(version == 4)
        iph = ip4_or_ip6;
    else if(version == 6)
        iph6 = ip4_or_ip6;
    else
        return NULL;

    if(version == 4){
        *proto = iph->protocol;
        l4_offset += iph->ihl * 4;
        l4_packet_len = ntohs(iph->tot_len) - (iph->ihl * 4);
        if(iph->ihl > 5)
        {
           /* 0x83松散源路由选项,0x89严格源路由,0x44时间戳,0x20Router-Alert,0x07Record-Route */
           /* 目前只判定第一个选项的类型,后续如果需要添加更多选项,则需要在iph的成员中依次补充剩余选项字段 */
            workflow->ip_link_type = iph->option_type;
            if((0x07 & iph->option_type) == 0x07)
                workflow->routers_flag = 0x07;
        }
    }
    else{
        *proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
        l4_offset += 40;
        l4_packet_len = ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
        if (*proto == IPPROTO_DSTOPTS || *proto == IPPROTO_HOPOPTS) {/* IPv6 destination option */
            uint8_t off = 8 * (data_start[1] + 1);
            if(off > l4_packet_len)
                return NULL;
            l4_offset     += off;
            l4_packet_len -= off;
            *proto = data_start[40];
        }

        /* VoLTE */
        if(*proto == IPPROTO_ESP && l4_packet_len > 22
            && data_start[l4_offset+l4_packet_len-13] == IPPROTO_UDP
            && data_start[l4_offset+l4_packet_len-14] + 22 + get_uint16_ntohs(data_start, l4_offset+12) == l4_packet_len)
        {
            *proto = IPPROTO_UDP;
            l4_offset += 8; /* spi & seq */
            l4_packet_len = get_uint16_ntohs(data_start, l4_offset+4);
        }
    }

    l4 = data_start + l4_offset;

    if (outer == NULL) {
        if(l4_packet_len < 64)
            workflow->stats.packet_len[0]++;
        else if(l4_packet_len >= 64 && l4_packet_len < 128)
            workflow->stats.packet_len[1]++;
        else if(l4_packet_len >= 128 && l4_packet_len < 256)
            workflow->stats.packet_len[2]++;
        else if(l4_packet_len >= 256 && l4_packet_len < 1024)
            workflow->stats.packet_len[3]++;
        else if(l4_packet_len >= 1024 && l4_packet_len < 1500)
            workflow->stats.packet_len[4]++;
        else if(l4_packet_len >= 1500)
            workflow->stats.packet_len[5]++;

        if(l4_packet_len > workflow->stats.max_packet_len)
            workflow->stats.max_packet_len = l4_packet_len;
        if (l4_packet_len < workflow->stats.min_packet_len)
            workflow->stats.min_packet_len = l4_packet_len;
    }

    if(*proto == IPPROTO_TCP && l4_packet_len >= 20) {
        workflow->stats.tcp_count++;
        *tcph = (const struct dpi_tcphdr *)l4;
        *sport = ntohs((*tcph)->source);
        *dport = ntohs((*tcph)->dest);
        int tcp_len = DPI_MIN(4 * (*tcph)->doff, l4_packet_len);
        *payload = &l4[tcp_len];
        *payload_len = l4_packet_len > (4 * (*tcph)->doff) ? l4_packet_len - (4 * (*tcph)->doff) : 0;
        workflow->check = ntohs((*tcph)->check);
        workflow->handshake = ( (*tcph)->cwr << 7 | (*tcph)->ece << 6 | (*tcph)->urg << 5 | (*tcph)->ack << 4
                               |(*tcph)->psh << 3 | (*tcph)->rst << 2 | (*tcph)->syn << 1 | (*tcph)->fin);

    } else if(*proto == IPPROTO_UDP && l4_packet_len >= 8) {
        workflow->stats.udp_count++;
        *udph = (const struct dpi_udphdr *)l4;
        *sport = ntohs((*udph)->source);
        *dport = ntohs((*udph)->dest);
        *payload = &l4[sizeof(struct dpi_udphdr)];
        *payload_len = l4_packet_len > sizeof(struct dpi_udphdr) ? l4_packet_len - sizeof(struct dpi_udphdr) : 0;
        workflow->check = ntohs((*udph)->check);

    } else if(*proto != IPPROTO_TCP && *proto != IPPROTO_UDP){
        if(*proto == IPPROTO_SCTP){
            const struct dpi_sctphdr *sctph = (const struct dpi_sctphdr*)l4;
            *sport = ntohs(sctph->source);
            *dport = ntohs(sctph->dest);
            workflow->stats.sctp_count++;
        }
        else{
            *sport = *dport = 0;
        }
        *payload = l4;
        *payload_len = l4_packet_len;
    }
    else{
        return NULL;
    }

    workflow->payload_len   = *payload_len;
    workflow->l4_protocol   = *proto;
    workflow->l4_packet     = l4;
    workflow->l4_packet_len = l4_packet_len;

    return find_or_create_flow(workflow, outer, version == 4 ? (const void*)iph : (const void*)iph6, *proto, htons(*sport), htons(*dport), 0, src_to_dst_direction);
}

/** 基于 checksum 的流完整性校验 **/
bool flowChecksum(const void *ipHdr, uint8_t iphver, const void *l4hdr, uint8_t l4proto, uint16_t l4len)
{
    uint16_t csum = 1;
    const struct dpi_iphdr   *iph4 = NULL;
    const struct dpi_ipv6hdr *iph6 = NULL;
    const struct dpi_tcphdr  *tcph = NULL;
    const struct dpi_udphdr  *udph = NULL;

    if (iphver == 4)
    {
        iph4 = (const struct dpi_iphdr*)ipHdr;
        csum = IPv4Checksum((uint16_t*)iph4, iph4->ihl * 4, iph4->check);

        switch (l4proto) {
        case IP_PROTO_TCP:
            tcph = (const struct dpi_tcphdr*)l4hdr;
            csum |= TCPv4Checksum(iph4, tcph, l4len, tcph->check);
            break;
        case IP_PROTO_UDP:
            udph = (const struct dpi_udphdr*)l4hdr;
            csum |= UDPv4Checksum(iph4, udph, l4len, udph->check);
            break;
        }
    }
    else if (iphver == 6)
    {
        iph6 = (const struct dpi_ipv6hdr*)ipHdr;

        switch (l4proto) {
        case IP_PROTO_TCP:
            tcph = (const struct dpi_tcphdr*)l4hdr;
            csum = TCPv6Checksum(iph6, tcph, l4len,  tcph->check);
            break;
        case IP_PROTO_UDP:
            udph = (const struct dpi_udphdr*)l4hdr;
            csum = UDPv6Checksum(iph6, udph, l4len,  udph->check);
            break;
        }
    }

    return csum == 0;
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//代码折叠

inline static int
flow_code_2(struct work_process_data *workflow, struct flow_info *flow, int src_to_dst_direction, const void *ip4_or_ip6, uint8_t version)
{
    if(src_to_dst_direction == FLOW_DIR_SRC2DST) {
        if (flow->src2dst_packets == 1) {
            flow->check[FLOW_DIR_SRC2DST] = flowChecksum(ip4_or_ip6, version, workflow->l4_packet,
                                                        workflow->l4_protocol, workflow->l4_packet_len);
        }
    } else {
        if (flow->dst2src_packets == 1) {
            flow->check[FLOW_DIR_DST2SRC] = flowChecksum(ip4_or_ip6, version, workflow->l4_packet,
                                                        workflow->l4_protocol, workflow->l4_packet_len);
        }
    }
    return 0;
}

inline static int
flow_code_1(struct work_process_data *workflow, struct flow_info *flow, uint16_t rawsize, uint16_t payload_len, int src_to_dst_direction, uint64_t time)
{
    flow->packet_len=rawsize;

    /* 更新workflow的统计信息 */
    workflow->stats.ip_packet_count++;
    workflow->stats.total_ip_bytes += rawsize;
    workflow->stats.flow_stats_total_pkts[flow->real_protocol_id]++;
    workflow->stats.flow_stats_total_bytes[flow->real_protocol_id] += rawsize;

    /* 更新flow的统计信息 */
    flow->last_seen = time;
    flow->pkt_first_line.linelen    = 0;
    flow->pkt_first_line.has_search = 0;

    flow->timestamp = READ_FROM_PCAP == g_config.data_source ? workflow->timestamp : g_config.g_now_time_usec;
    if(src_to_dst_direction == FLOW_DIR_SRC2DST) {
        if(flow->src2dst_max_packet_len<rawsize){
            flow->src2dst_max_packet_len=rawsize;
        }
        if(flow->src2dst_min_packet_len>rawsize){
            flow->src2dst_min_packet_len=rawsize;
        }
        if(flow->src2dst_max_packet_interval<workflow->pkt_interval){
            flow->src2dst_max_packet_interval=workflow->pkt_interval;
        }
        if(flow->src2dst_min_packet_interval>workflow->pkt_interval){
            flow->src2dst_min_packet_interval=workflow->pkt_interval;
        }
        if(flow->up_payload_len_cnt<SDT_PAYLOAD_LEN_SET_NUM && payload_len>0){
            flow->up_payload_len_set[flow->up_payload_len_cnt]=payload_len;
            flow->up_payload_len_cnt++;
        }
        flow->src2dst_packets++;
        flow->src2dst_bytes += rawsize;
        flow->src2dst_payload_len += payload_len;
    } else {
        if(flow->dst2src_max_packet_len<rawsize){
            flow->dst2src_max_packet_len=rawsize;
        }
        if(flow->dst2src_min_packet_len>rawsize){
            flow->dst2src_min_packet_len=rawsize;
        }
        if(flow->dst2src_max_packet_interval<workflow->pkt_interval){
            flow->dst2src_max_packet_interval=workflow->pkt_interval;
        }
        if(flow->dst2src_min_packet_interval>workflow->pkt_interval){
            flow->dst2src_min_packet_interval=workflow->pkt_interval;
        }
        if(flow->down_payload_len_cnt<SDT_PAYLOAD_LEN_SET_NUM && payload_len>0){
            flow->down_payload_len_set[flow->down_payload_len_cnt]=payload_len;
            flow->down_payload_len_cnt++;
        }
        flow->dst2src_packets++;
        flow->dst2src_bytes       += rawsize;
        flow->dst2src_payload_len += payload_len;
    }
    return 0;
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/*link层的主处理函数*/
int dpi_packet_processing_datalink_layer(struct work_process_data *workflow, uint64_t time,
                                     const struct pkt_info  *pkt, const void *payload,
                                     uint32_t linksize, uint16_t rawsize, uint16_t proto_type);
int dpi_packet_processing_datalink_layer(struct work_process_data *workflow, uint64_t time,
                                     const struct pkt_info  *pkt, const void *payload,
                                     uint32_t linksize, uint16_t rawsize, uint16_t proto_type)
{
    int ret = PKT_OK;
    // arp 解析使用了通用tbl接口, 其中使用了dpi_flow_clone, dpi_flow_free操作flow
    struct flow_info *flow = dpi_flow_create();
    if (flow == NULL)
        return PKT_DROP;

    memset(flow, 0, sizeof(*flow));
    rte_atomic16_init(&flow->ref);
    rte_atomic16_inc(&flow->ref);

    /*flow中的链表需要初始化*/
    INIT_LIST_HEAD(&flow->reassemble_src2dst_head);
    INIT_LIST_HEAD(&flow->reassemble_dst2src_head);

    INIT_LIST_HEAD(&flow->sdt_flow.pkt_stream_head[FLOW_DIR_SRC2DST]); //测试用
    INIT_LIST_HEAD(&flow->sdt_flow.pkt_stream_head[FLOW_DIR_DST2SRC]); //测试用
    INIT_LIST_HEAD(&flow->sdt_flow.single_stream_head[FLOW_DIR_SRC2DST]); //测试用
    INIT_LIST_HEAD(&flow->sdt_flow.single_stream_head[FLOW_DIR_DST2SRC]); //测试用

    INIT_LIST_HEAD(&flow->sdt_flow.tcp_rsm_head[FLOW_DIR_SRC2DST]); //测试用
    INIT_LIST_HEAD(&flow->sdt_flow.tcp_rsm_head[FLOW_DIR_DST2SRC]); //测试用

    flow->thread_id = workflow->thread_id;
    flow->last_seen = time;
    flow->timestamp = READ_FROM_PCAP == g_config.data_source ? workflow->timestamp : g_config.g_now_time_usec;
    flow->pkt = pkt;
    if(g_config._327_common_switch){
        memcpy(&flow->p327_header[0], &workflow->p327_header, sizeof(workflow->p327_header));
    }

    flow->sdt_flow.pEngine=workflow->pEngine;
    sdt_acl_match_result_copy( &workflow->acl_result, &flow->acl_result);

    //将newflow的数据链路层 复制 workflow
    data_link_layer_copy(flow, workflow);

    int direction = 0;
    switch(proto_type){
        case ARP:
        case RARP:
            dpi_dissect_arp(flow, direction, (const uint8_t*)payload, linksize, DISSECT_PKT_ORIGINAL);
           break;
    }

    //非ip数据报 不需等待超时, 解析完即释放
    dpi_flow_free(flow, dpi_flow_timeout_free);

    return ret;

}

/********** TCP_RSM 是一个纯粹的库 *************************/
/********** TCP_RSM 中间层 *********************************/

uint64_t  tcp_alloc_cnt = 0;
uint64_t  tcp_free_cnt  = 0;
#define  tcp_malloc malloc
#define  tcp_free   free
static void* node_new(void *user)
{
    void  *mem = NULL;
    struct flow_info *flow = (struct flow_info *)user;
    mem = tcp_malloc(tcp_rsm_node_size());
    ATOMIC_ADD_FETCH(&tcp_alloc_cnt);
    return mem;
}

static void node_put(void *user, void *mem)
{
    struct flow_info *flow = (struct flow_info *)user;
    tcp_free(mem);
    ATOMIC_ADD_FETCH(&tcp_free_cnt);
}

static int  pkt_dissect(void *user, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    struct flow_info *flow = (struct flow_info *)user;
    return flow->decode->pkt_dissect(flow, C2S, payload, payload_len);
}

static int  pkt_miss(void *user, uint8_t C2S, uint32_t miss_len)
{
    struct flow_info *flow = (struct flow_info *)user;
    return flow->decode->pkt_miss(flow, C2S, miss_len);
}

static void flow_finish(void *user)
{
    struct flow_info *flow = (struct flow_info *)user;
    return flow->decode->flow_finish(flow);
}

static int flow_tcp_rsm( struct flow_info *flow, const struct dpi_tcphdr *tcph, const unsigned char *payload, int payload_len)
{
    uint32_t fail = 0;
    uint32_t tcp_seq = ntohl(tcph->seq);
    uint32_t tcp_ack = ntohl(tcph->ack_seq);
    uint16_t portsrc = ntohs(tcph->source);
    uint16_t portdst = ntohs(tcph->dest);
    uint8_t  C2S     = portsrc > portdst;
    uint8_t  flg     = 0;

    flg |= tcph->fin << 0;
    flg |= tcph->syn << 1;
    flg |= tcph->rst << 2;
    flg |= tcph->psh << 3;

    //初始化
    if(NULL == flow->rsm)
    {
        flow->rsm = tcp_rsm_init(pkt_dissect, pkt_miss, flow_finish, node_new, node_put, flow, g_config.tcp_rsm_out_of_order);
    }

    //加入重组
    int rc = tcp_rsm_push(flow->rsm, C2S, tcp_seq, tcp_ack, flg, payload, payload_len);
    if(rc < 0)
    {
        tcp_rsm_free(flow->rsm);
        flow->rsm = TCP_RSM_DISABLE;
        fail = -1;
    }

    return fail;
}

//记录 payload 到 stream
static int stream_recoder(struct stream_flow_t *stream_flow, const uint8_t *payload, uint16_t payload_len)
{
    if((stream_flow->index < (int)sizeof(stream_flow->byte)) && payload_len > 0)
    {
        int ramin = sizeof(stream_flow->byte) - stream_flow->index;
        int minlen= ramin < (int)payload_len ? ramin : payload_len;

        memcpy(stream_flow->byte + stream_flow->index, payload, minlen);

        stream_flow->index += minlen;
    }
    return 0;
}

static int cache_mbuf_tcp_rsm( struct flow_info *flow)
{
    while(flow->cache_index)
    {
        flow->cache_index--;

        struct dpi_tcphdr *tcph       = &flow->cache[flow->cache_index].tcphdr;
        unsigned char     *payload    = flow->cache[flow->cache_index].payload;
        int                payload_len= flow->cache[flow->cache_index].payload_len;;


        flow_tcp_rsm(flow, tcph, payload, payload_len);
        g_free(payload);
    }
    return 0;
}

static int cache_mbuf_release(struct flow_info *flow)
{
    while(flow->cache_index)
    {
        flow->cache_index--;
        gpointer payload = flow->cache[flow->cache_index].payload;
        g_free(payload);
    }
    return 0;
}

//是否缓存了一部分报文
static int cache_release(struct flow_info *flow)
{
    //放入到 TCP_RSM
    if(flow->decode)
    {
        return cache_mbuf_tcp_rsm(flow);
    }
    else
    //旧的协议解析模块, 直接释放
    {
        return cache_mbuf_release(flow);
    }
}

/********** TCP_RSM 中间层 *********************************/



/*ip层的主处理函数*/
int dpi_packet_processing_ip_layer(struct work_process_data *workflow,
                                            uint64_t time,
                                            const struct five_tuple *outer,
                                            struct pkt_info  *pkt,
                                            const void *ip4_or_ip6,
                                            uint16_t ipsize,
                                            uint16_t rawsize)
{
    int ret = PKT_OK;
    uint16_t sport, dport, payload_len;
    const uint8_t *payload;

    uint8_t proto; /* 因为有VoLTE等隧道协议,proto需要保留 */
    uint8_t src_to_dst_direction = FLOW_DIR_SRC2DST;

    const struct dpi_iphdr   *iph  = NULL;
    const struct dpi_ipv6hdr *iph6 = NULL;
    const struct dpi_tcphdr  *tcph = NULL;
    const struct dpi_udphdr  *udph = NULL;

    struct flow_info *flow = NULL;
    uint8_t version = 0;
    struct mac_packet_header *psdx = NULL;
    uint16_t index = 0;
    const uint8_t *eth_trialer = NULL;
    uint16_t      eth_trialer_len = 0;

    if(ip4_or_ip6 == NULL){
        workflow->stats.total_discarded_pkts++;
        ret = PKT_DROP;
        goto ERROR;
    }

    version = *((const uint8_t*)ip4_or_ip6) >> 4;
    if(version == 4){
        iph = ip4_or_ip6;
        if((iph->ihl * 4) > ipsize || ipsize < ntohs(iph->tot_len))
        {
            ret = PKT_DROP;
            goto ERROR;
        }
    }
    else if(version == 6){
        iph6 = ip4_or_ip6;
        if(ipsize < sizeof(const struct dpi_ipv6hdr) || ipsize < 40 + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen))
        {
            ret = PKT_DROP;
            goto ERROR;
        }
    }
    else{
        ret = PKT_DROP;
        goto ERROR;
    }

    /*建会话*/
    flow = dpi_find_create_flow_ip(workflow, outer, version == 4 ? (const void*)iph : (const void*)iph6,
                                                       ipsize, &tcph, &udph, &sport, &dport, &proto,
                                                       &payload, &payload_len, &src_to_dst_direction);

    //有些错包是无法 创建会话的 []
    if (!flow){
        workflow->stats.total_discarded_pkts++;
        ret = PKT_OK;
        goto ERROR;
    }

    //存储 SDX 头到 flow 中
    psdx = flow->pSDTMacHeader[src_to_dst_direction];
    if(1==g_config.sdx_config.sdx_mac_packet_header_flag && pkt->raw_pkt && NULL== psdx){
        psdx = malloc(sizeof(struct mac_packet_header));
        memcpy(psdx, pkt->raw_pkt, sizeof(struct mac_packet_header));
        flow->pSDTMacHeader[src_to_dst_direction] = psdx;
    }

    //记住最后的TTL, 超时写TBL的时候, 要用到
    flow->last_ttl = 4==version ? iph->ttl : iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim;

    flow->pkt=pkt;
    flow->end_time = g_config.g_now_time_usec;
    // 正反向每间隔4包重置一下定时器
    if ((flow->src2dst_packets & 3) == 0 || (flow->dst2src_packets & 3) == 0) {
      dpi_flow_timer_reset(workflow, flow, flow_timer_cb, (void *)flow);
    }

    // 通联日志在此解析，解析完毕直接 return
    if (workflow->is_tll == 1) {
        dissect_tll(flow, src_to_dst_direction, 0, payload, payload_len, DISSECT_PKT_ORIGINAL);
        flow->pkt=NULL;
        return PKT_OK;
    }

    if(g_config._327_common_switch){
        memcpy(&flow->p327_header[src_to_dst_direction], &workflow->p327_header, sizeof(workflow->p327_header));
    }

    //代码折叠一下
    flow_code_1(workflow, flow, rawsize, payload_len, src_to_dst_direction, time);
    flow_code_2(workflow, flow, src_to_dst_direction, ip4_or_ip6, version);

    if(pkt->ethhdr)
        memcpy(&flow->session_ethhdr[src_to_dst_direction], pkt->ethhdr, sizeof(struct dpi_ethhdr));

    dpi_err_pcap_copy(flow, version == 4 ? (const void*)iph : (const void*)iph6 , (int)ipsize);  /* 如果开启了流模式抓包 */

    if (tcph) {
        set_tcp_stat_info(flow, src_to_dst_direction, tcph); /*统计tcp的部分信息，流日志中需要*/
    }

    uint16_t xa_trailer_len = 0;
    //scan模式获取文件名   放在dpi_sdt_packet_match_process之前避免第一帧输出还未获取到文件名
    if(g_config.work_mode_flag == WMF_scan){
        xa_trailer_len = get_uint16_t((const uint8_t *)ip4_or_ip6 + ipsize - 2, 0) ;
        if(*((const uint8_t *)ip4_or_ip6 + ipsize - xa_trailer_len) == 'X' &&
                *((const uint8_t *)ip4_or_ip6 + ipsize - xa_trailer_len+1) == 'A'){
            //从Trailer中获取离线包文件名
            if(flow->path==NULL){
                yv_trailer_info *xa_trailer = (yv_trailer_info *)((const uint8_t *)ip4_or_ip6 + ipsize - xa_trailer_len);
                flow->path=(char*)malloc(COMMON_FILE_PATH);
                if(flow->path){
                    memset(flow->path, 0, COMMON_FILE_PATH);
                    memcpy(flow->path, xa_trailer->ext_data, COMMON_FILE_PATH-1);
                    flow->path[xa_trailer->ext_data_len] = 0;
               }
            }

        }else
            xa_trailer_len  = 0;
    }

    /* sdt规则匹配功能模块 */
    sdt_acl_match_result_copy(&workflow->acl_result, &flow->acl_result);

    //MARK PCPA 输出功能在这里实现
    if(g_config.enable_ipff_match && PKT_DROP==dpi_sdt_packet_match_process(flow,pkt,payload, payload_len, (int)src_to_dst_direction)){
        ret = PKT_DROP;
        goto EXIT;
    }

    flow->data_link_layer.data = workflow->data_link_layer.data; /* 传递遗产:MAC层数据 */

    /*trailer在ipv4尾部*/
    if (version == 4 && payload_len && g_config.net_type == MOBILE_NET && ipsize > ntohs(iph->tot_len)) {
        eth_trialer = (const uint8_t *)iph + ntohs(iph->tot_len);
        eth_trialer_len = ipsize - ntohs(iph->tot_len) - xa_trailer_len;
        flow->tunnel_ip_len=eth_trialer_len;
        parse_trailer(&(flow->pTrailer), eth_trialer, eth_trialer_len, g_config.trailer_type, g_config.trailer_length);
    }

    /*trailer在ipv6尾部*/
    if (version == 6 && payload_len && g_config.net_type == MOBILE_NET && ipsize > sizeof(struct dpi_ipv6hdr) + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen)) {
        eth_trialer = (const uint8_t *)iph6 + sizeof(struct dpi_ipv6hdr) + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
        eth_trialer_len = ipsize - sizeof(struct dpi_ipv6hdr) - ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - xa_trailer_len;
        flow->tunnel_ip_len = eth_trialer_len;
        parse_trailer(&(flow->pTrailer), eth_trialer, eth_trialer_len, g_config.trailer_type, g_config.trailer_length );
    }

    /* 非tcp/udp的数据是单独解析 */
    if(unlikely(proto != IPPROTO_TCP && proto != IPPROTO_UDP))
    {
        process_non_app_protocol(workflow, flow, proto, payload, payload_len);
        goto final;
    }

    /*会话的第一个报文,需要先识别关联协议,关联协议是像ftp-control与ftp-data,及sip与rtp等数据与控制不经过同一条流,不与控制流关联就识别不出数据流类型的*/
    /*元数据一般不还原关联协议,目前的条件设置应该进不了这块代码*/
    //if (flow->real_protocol_id == PROTOCOL_UNKNOWN && payload_len && flow->src2dst_packets + flow->dst2src_packets < 6) {
    if (flow->real_protocol_id == PROTOCOL_UNKNOWN && payload_len &&
        flow->src2dst_packets + flow->dst2src_packets <= g_config.conversation_identify_pkt_num)
    {
        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.port_src = flow->tuple.inner.port_src;
        tuple.port_dst = flow->tuple.inner.port_dst;
        memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
        memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        tuple.proto = flow->tuple.inner.proto;

        struct conversation_value *conv = find_conversation(&tuple, 0);
        if (conv) {
            flow->real_protocol_id = conv->protocol;
            workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]--;
            workflow->stats.flow_stats_total[flow->real_protocol_id]++;
            //workflow->stats.flow_stats[PROTOCOL_UNKNOWN]--;       转移到超时的时候再标记
            //workflow->stats.flow_stats[flow->real_protocol_id]++; 转移到超时的时候再标记
        }
    }


    /*普通协议识别，通过端口，模式匹配进行识别*/
    if (flow->real_protocol_id == PROTOCOL_UNKNOWN && payload_len
        &&  ((proto == IPPROTO_UDP && flow->src2dst_packets + flow->dst2src_packets <= g_config.udp_identify_pkt_num) ||
             (proto == IPPROTO_TCP && flow->src2dst_packets + flow->dst2src_packets <= g_config.tcp_identify_pkt_num)) )
    {
        check_dpi_flow_func(flow, payload, payload_len);

        if ((flow->real_protocol_id > PROTOCOL_UNKNOWN) && (flow->real_protocol_id < PROTOCOL_MAX)) {
            //workflow->stats.flow_stats[PROTOCOL_UNKNOWN]--;       转移到超时的时候再标记
            //workflow->stats.flow_stats[flow->real_protocol_id]++; 转移到超时的时候再标记
            workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]--;
            workflow->stats.flow_stats_total[flow->real_protocol_id]++;

            workflow->stats.flow_stats_total_pkts[flow->real_protocol_id] += flow->src2dst_packets + flow->dst2src_packets;
            workflow->stats.flow_stats_total_bytes[flow->real_protocol_id] += flow->src2dst_bytes + flow->dst2src_bytes;
            workflow->stats.flow_stats_total_pkts[PROTOCOL_UNKNOWN] -= flow->src2dst_packets + flow->dst2src_packets;
            workflow->stats.flow_stats_total_bytes[PROTOCOL_UNKNOWN] -= flow->src2dst_bytes + flow->dst2src_bytes;

            //TODO  有锁，后续重做
            // pthread_mutex_lock(&p_record.record_mutex);
            // if (unlikely(p_record.record_permin[flow->real_protocol_id] == UINT64_MAX))
            // {
            //     p_record.record_permin_circle[flow->real_protocol_id]++;
            //     p_record.record_permin[flow->real_protocol_id] = 0;
            // }
            // else
            //     p_record.record_permin[flow->real_protocol_id]++;

            // if (unlikely(p_record.record_perday[flow->real_protocol_id] == UINT64_MAX))
            // {
            //     p_record.record_perday_circle[flow->real_protocol_id]++;
            //     p_record.record_perday[flow->real_protocol_id] = 0;
            // }
            // else
            //     p_record.record_perday[flow->real_protocol_id]++;

            // pthread_mutex_unlock(&p_record.record_mutex);
        }
    }

    //对于长期 TCP 重组 依然无法识别的流量, 拒绝解析.
    if(IPPROTO_TCP == proto && PROTOCOL_UNKNOWN == flow->real_protocol_id && flow->cache_index >= CACHE_MAX){
        cache_mbuf_release(flow);
        ret = PKT_OK;
        goto EXIT;
    }

    /*l2tp下又是l2tp,线上出现一直l2tp嵌套,导致栈满的崩溃*/
    if (outer && flow->real_protocol_id == PROTOCOL_L2TP)
        goto final;


    //存储流的stream 信息 both
    stream_recoder(&flow->both_stream, payload, payload_len);

    //存储流的stream 信息 up/dpwn
    if(FLOW_DIR_SRC2DST == src_to_dst_direction)
    {
        stream_recoder(&flow->up_stream, payload, payload_len);
    }
    else
    {
        stream_recoder(&flow->down_stream, payload, payload_len);
    }

    //如果 流协议未识别 -- 缓存 mbuf
    if(tcph && flow->real_protocol_id == PROTOCOL_UNKNOWN && flow->cache_index < g_config.unknown_cache_max)
    {
        flow->cache[flow->cache_index].tcphdr       = *tcph;
        flow->cache[flow->cache_index].payload      = g_memdup(payload,payload_len);
        flow->cache[flow->cache_index].payload_len  = payload_len;
        flow->cache_index++;
    }

#ifdef DPI_SDT_ZDY
    //无法识别先释放 mbuf,重新缓存
    if(tcph && flow->real_protocol_id == PROTOCOL_UNKNOWN && flow->cache_index >= g_config.unknown_cache_max)
    {
        cache_release(flow);
    }
#endif

    //协议识别成功
    //预匹配
    //再解析
    if ((flow->real_protocol_id > PROTOCOL_UNKNOWN) && (flow->real_protocol_id < PROTOCOL_MAX))
    {
        //如果有缓存报文 - 泄洪
        if(flow->cache_index)
        {
            cache_release(flow);
        }

        // 新版解析接口
        if(proto == IPPROTO_TCP && flow->decode)
        {
            append_proto_info(flow); //延续 旧的 协议层次 append
            flow_tcp_rsm(flow, tcph, payload, payload_len);
        }
        // 旧版解析接口
        else
        if (proto == IPPROTO_TCP && 0==flow->timeout_flag && tcp_detection_array[flow->real_protocol_id].dissect_func){
            append_proto_info(flow);
            ret = tcp_detection_array[flow->real_protocol_id].dissect_func(flow,
                    src_to_dst_direction, ntohl(tcph->seq),
                    payload, payload_len, DISSECT_PKT_ORIGINAL);
        }else if (proto == IPPROTO_UDP && udp_detection_array[flow->real_protocol_id].dissect_func){
                append_proto_info(flow);
                ret = udp_detection_array[flow->real_protocol_id].dissect_func(flow,
                                                        src_to_dst_direction, 0,
                                                        payload, payload_len,
                                                        DISSECT_PKT_ORIGINAL);
        }
    }

EXIT:
    //清除 pkt 标识.
    flow->tunnel_ip_len=0;

final:
    /*会话数据包分布，流日志中需要*/
    set_flow_pkt_distribution(flow, src_to_dst_direction, rawsize);

ERROR:
    if(flow)
    {
        flow->pkt=NULL;
    }
    return ret;
}

static int
mpls_bol(const char*p, int len)
{
    return 1 == (p[2] & 0X01);
}

static uint16_t
protocol_detect(const char*p, int len)
{
    int what = p[0] >> 4;
    switch(what)
    {
        case 6:
            return  ETH_P_IPV6;
        case 4:
            return  ETH_P_IP;
        case 1:
        case 0:
        default:
            return 0;
    }
}

int
skip_mpls(const char*p, uint32_t len, uint16_t *next_type, uint32_t *mpls_lablesl, uint8_t *mpls_cnt)
{
    uint8_t    bos = 0;
    uint32_t   offset = 0;

    while(len > offset)
    {
        if(mpls_lablesl && *mpls_cnt<4)
        {
            mpls_lablesl[*mpls_cnt]=(get_uint32_ntohl(p,offset)>>12) & 0x000FFFFF;
            (*mpls_cnt)++;
        }
        bos =  mpls_bol(p + offset, len - offset);
        offset += 4;
        *next_type = protocol_detect(p + offset, len - offset);
        switch(bos)
        {
            case 0:
                if (mpls_lablesl)
                {
                    //这里是为了追加多层mpls的protoinfo
                    append_hardlink_proto_info(MPLS_UNI);
                }
                break;
            case 1:
                if(*next_type==MPLS_PWETHCW)
                {
                    offset += 4;
                }
                return offset;
            default:
                return -1;
        }
    }
    return -1;
}

#define LLC_SNAP 0xaa
#define LLC_SAP  0xfe
static void dissect_llc(const uint8_t *payload, uint32_t payload_len, struct work_process_data *workflow)
{
    if(payload_len < 17) // 2MAC + Type/Length + DSAP + SSAP + Control
        return;

    uint32_t length = get_uint16_ntohs(payload, 12);
    if(length + 14 > payload_len)
        return;
    append_hardlink_proto_info(LLC);

    uint32_t offset = 14;
    uint8_t  dsap = payload[offset] & 0xfe;
    uint8_t  ssap = payload[offset+1] & 0xfe;
    uint8_t  control = payload[offset+3];
    offset += 3;
    append_hardlink_proto_info(dsap);

    if(dsap == LLC_SNAP && ssap == LLC_SNAP){
        uint32_t organization = get_uint32_ntohl(payload, offset) >> 8;
        uint16_t pid = get_uint16_ntohs(payload, offset + 3);
        offset += 5;
        if(organization == 0x0c){ //CISCO
            if(pid == 0x2000){     //CDP
                dissect_cdp(payload + offset, payload_len - offset);
            }
        }
    }
}


/*根据传输层协议和当前会话的超时下标，得到会话应该放入的超时链表的下标*/
uint16_t dpi_get_flow_timeout_index(uint8_t proto)
{
    uint64_t index;

    switch (proto) {

        case IPPROTO_TCP :
            index = (g_config.timeout_index + g_config.tcp_flow_timeout - 1) % TIMEOUT_MAX;
            break;
        case IPPROTO_UDP :
            index = (g_config.timeout_index + g_config.udp_flow_timeout - 1) % TIMEOUT_MAX;
            break;
        case IPPROTO_SCTP :
            index = (g_config.timeout_index + g_config.sctp_flow_timeout - 1) % TIMEOUT_MAX;
            break;
        default :
            index = (g_config.timeout_index + g_config.tcp_flow_timeout - 1) % TIMEOUT_MAX;
            break;
    }

    return index;
}

/*根据传输层协议和当前会话的超时下标，得到会话应该放入的超时链表的下标*/
static uint16_t dpi_get_flow_timeout_and_delete_index(void)
{
    uint64_t index;

    index = (g_config.timeout_index + g_config.tcp_flow_timeout_del - 1) % TIMEOUT_MAX;
    return index;
}

extern rte_atomic64_t rsm_fail_get;

extern struct rte_mempool *pktmbuf_pool[];

static void flow_timeout_new(struct flow_info *pos)
{
    TCP_RSM *rsm = pos->rsm;
    if(rsm)
    {
        int64_t rsm_fail = rsm_fail_get.cnt;
        const struct tcp_status * status = NULL;

        status = tcp_rsm_status(rsm, 0);
        rte_atomic64_add(&rsm_fail_get, status->fail);

        status = tcp_rsm_status(rsm, 1);
        rte_atomic64_add(&rsm_fail_get, status->fail);

        tcp_rsm_free(rsm);
    }

    cache_mbuf_release(pos);
    pos->rsm = NULL;
}

/**
*  流表超时分为两个环节： 数据处理 和 内存回收，此前逻辑为同时处理， 第一次超时的时候会对部分内存进行回收
*  鉴于 sdt flow 多线程存活的机制，需要把逻辑拆分，流表老化第一次回调的时候只做数据处理，内存回收在流表超时的时候再处理。
*  dpi_flow_timeout_flush  只做数据处理
*  dpi_flow_timeout_free   内存回收
*/
static void dpi_flow_timeout_flush(struct flow_info *flow)
{
  struct work_process_data *process = NULL;
  process                           = &flow_thread_info[flow->thread_id];

  flow_timeout_new(flow);
  // 调用每个flow的fini接口
  if (tcp_detection_array[flow->real_protocol_id].flow_timeout) {
    tcp_detection_array[flow->real_protocol_id].flow_timeout(flow);
  }

  /*tcp的数据包，需要做最后一次重组解析*/
  if (flow->tuple.inner.proto == IPPROTO_TCP) tcp_reassemble_do_final(flow);

  if (READ_FROM_PCAP != g_config.data_source) flow->end_time = g_config.g_now_time_usec;

  if (flow->app_session) {
    if (tcp_detection_array[flow->real_protocol_id].exit_func) {
      tcp_detection_array[flow->real_protocol_id].exit_func(flow, flow->app_session);
    }
  }
}

void dpi_flow_timeout_free(struct flow_info *flow)
{
  // 新版会话超时
  // 旧版的超时 即将全部删除, 现在处于过度期
  flow->pkt = NULL;

  if (flow->app_session) {
    free(flow->app_session);
    flow->app_session = NULL;
  }

  if (flow->synData) {
    free(flow->synData);
    flow->synData = NULL;
  }

  if (flow->synackData) {
    free(flow->synackData);
    flow->synackData = NULL;
  }

  destroy_trailer(&(flow->pTrailer));

  if (flow->pSDTMacHeader[0]) {
    free(flow->pSDTMacHeader[0]);
    flow->pSDTMacHeader[0] = NULL;
  }

  if (flow->pSDTMacHeader[1]) {
    free(flow->pSDTMacHeader[1]);
    flow->pSDTMacHeader[1] = NULL;
  }

  // 释放内存
  alloc_destory(flow->memAc);
  flow->memAc = NULL;

  // dpi_sdt_flow_link_match(pos);
  dpi_sdt_flow_timeout(flow);
}

/*会话超时的主要函数*/
void dpi_flow_timeout(struct flow_info *flow)
{
    // 数据处理
    dpi_flow_timeout_flush(flow);

    // 流表超时，内存回收
    dpi_flow_free(flow, dpi_flow_timeout_free);

    return;

}

/*销毁所有的会话，只有收到vtysh的dpi退出命令时才会执行*/
void do_all_flow_free(uint16_t thread_id)
{
    int i;
    struct work_process_data *process = &flow_thread_info[thread_id];

    if (process->stats.flow_count == 0)
        return;

    for (i = 0; i < TIMEOUT_MAX; i++) {
    }
    return;
}

/*包解析入口函数,解析以太层,并剥离GTP隧道*/
int workflow_process_packet2 (struct work_process_data * workflow,u_int64_t p_usec, const u_char *raw_packet,
                              uint32_t raw_pkt_len, uint64_t timestamp, void* userdata)
{
    struct pkt_tuple_t  tuple;
    struct pkt_info     pkt_data;
    memset(&pkt_data, 0, sizeof(struct pkt_info ));
    const u_char *packet =  raw_packet;
    uint32_t     pkt_len = raw_pkt_len;

    /* Declare pointers to packet headers */
    const struct dpi_ethhdr  *ethhdr;
    const struct dpi_iphdr   *iph  = NULL;
    const struct dpi_ipv6hdr *iph6 = NULL;

    uint16_t eth_offset = 0;
    uint16_t ip_offset = 0;
    uint16_t ip_len = 0;
    uint16_t type;
    uint8_t  proto;
    int      ret;

    workflow->path = workflow->pcap_file_name;
    workflow->timestamp = READ_FROM_PCAP == g_config.data_source ? timestamp : g_config.g_now_time_usec;
    workflow->stats.total_wire_bytes += pkt_len;
    workflow->stats.raw_packet_count++;
    workflow->vlan_flag   = 0;
    workflow->is_mpls     = 0;
    workflow->is_tll      = 0;
    workflow->layer_cnt   = 0;
    pkt_data.pkt_len      = raw_pkt_len;
    pkt_data.raw_pkt      = raw_packet;

    //数据链路层 -- 清零
    memset(&workflow->data_link_layer, 0, sizeof(workflow->data_link_layer));

    //workflow->last_time = p_usec > workflow->last_time ? p_usec : workflow->last_time; /* 计算时间差 */
    if(p_usec > workflow->last_time)
    {
        workflow->pkt_interval=p_usec - workflow->last_time;
        if(workflow->max_interval <  p_usec - workflow->last_time)
            workflow->max_interval = p_usec - workflow->last_time;
        if(0 == workflow->min_interval || workflow->min_interval > p_usec - workflow->last_time)
            workflow->min_interval = p_usec - workflow->last_time;
        workflow->last_time = p_usec;
    }

    /* =========================== sdx  特殊mac头信息 =========================== */
    if(1==g_config.sdx_config.sdx_mac_packet_header_flag){
        uint16_t mac_header_len=sizeof(struct mac_packet_header);
        if(raw_pkt_len < mac_header_len){
            return PKT_DROP;
        }

        const struct mac_packet_header *mh=(const struct mac_packet_header *)raw_packet;

        packet = &raw_packet[mac_header_len];
        pkt_len = raw_pkt_len - mac_header_len;

        switch(mh->DataType){
        case 0x47:   // 通联日志
            workflow->is_tll = 1;
            break;
        case 0x40:  // 匹配命中Ipv4数据
        case 0x41:  // 未匹配命中Ipv4数据
        case 0x45:  // 匹配命中IPv6数据
        case 0x46:  // 未匹配命中IPv6数据
        case 0x42:  // 非IP数据
            break;
        default:
            break;
        }

        /* 特殊MAC头之后的链路层解析 */
        switch (mh->bLinkType) {
            case 0x00:  /* 无链路层信息字段,Data即纯粹的通联日志数据 */
                type = ETH_P_IP;
                ethhdr = (const struct dpi_ethhdr *) &raw_packet[eth_offset];
                pkt_data.ethhdr = (const struct dpi_ethhdr *) &raw_packet[eth_offset];
                goto outer;
            case 0X01:  /* VPI VCI */
            case 0X04:  /* DLCI (FR) */
                packet += mh->bLinkLen;
                pkt_len -= mh->bLinkLen;
                break;
            case 0X06:  /* MAC 地址 */
                break;
            case 0X0A:  /* PPP */
            case 0X0B:  /* Cisco PPP */
//                dissect_llc(packet, pkt_len, workflow);
                return PKT_DROP;
            case 0X02:  /* 废弃 */
            case 0X03:  /* 废弃 */
            case 0X05:  /* 废弃 */
            case 0X07:  /* 废弃 */
            case 0X08:  /* 废弃 */
            case 0X09:  /* 废弃 */
            case 0XFF:  /* 未定义的链路层信息 */
            default:
                return PKT_DROP;
        }
    }

    get_eth_info(packet, pkt_len, &(workflow->data_link_layer), g_config.trailer_type); /* 获取以太层相关信息 */

    if(READ_FROM_PCAP == g_config.data_source){ /* 识别离线解析的一些奇葩格式 */
        if((get_uint32_ntohl(packet, 0) == 0x0f000800)){
            if(is_ip4(packet+4, pkt_len-4)){
                type = ETH_P_IP;
                ip_offset = 4;
                goto outer;
            }
            else if(is_ip6(packet+4, pkt_len-4)){
                type = ETH_P_IPV6;
                ip_offset = 4;
                goto outer;
            }
        }
        else if(is_ip4(packet, pkt_len)){
            type = ETH_P_IP;
            ip_offset = 0;
            goto outer;
        }
        else if(is_ip6(packet, pkt_len)){
            type = ETH_P_IPV6;
            ip_offset = 0;
            goto outer;
        }
    }

eth_again:
    ethhdr = (const struct dpi_ethhdr *) &packet[eth_offset];
    ip_offset = sizeof(struct dpi_ethhdr) + eth_offset;
    pkt_data.ethhdr=(const struct dpi_ethhdr *) &packet[eth_offset];
    g_proto_layer[g_proto_layer_cnt++] = DPI_ETH + PROTOCOL_MAX;

    if(g_config._327_common_switch){
        dissect_mac_to_p327_header(&workflow->p327_header, (const uint8_t *)ethhdr, sizeof(*ethhdr));
    }

    uint16_t check = ntohs(ethhdr->h_proto);
    if (check >= 0x0600){
        type = check;
    }
    else if(check <= 1500){
        dissect_llc(packet, pkt_len, workflow);
        return PKT_OK;
    }
    else{
        type = 0;
    }

//剥洋葱-手艺
strip_again:
    /*网络层类型*/
    if (type != ETH_P_IP && type != ETH_P_IPV6) {
        // 这里是为了防止输出两次ip层，在剥洋葱结束后再向protoinfo中增加IP层
        append_hardlink_proto_info(type);
    }
    switch(type) {
        case MPLS_PWETHCW:
        {
            packet+=ip_offset;
            pkt_len-=ip_offset;
            eth_offset = 0;
            goto eth_again;
        }
        case VLAN:
            workflow->data_link_layer.vlan_id[workflow->vlan_flag] = get_uint16_ntohs(packet, ip_offset);
            workflow->vlan_flag++;
            type = get_uint16_ntohs(packet, ip_offset+2);
            ip_offset += 4;
            goto strip_again; //解决VLAN 中的ARP

        case MPLS_UNI:
        case MPLS_MULTI:
            ret = skip_mpls((const char*)packet + ip_offset, pkt_len - ip_offset, &type, workflow->data_link_layer.mpls_label,&workflow->is_mpls);
            if (ret <= 0)
                return PKT_DROP;
            ip_offset += ret;
            goto strip_again; //解决 ETH/VLAN/MPLS/MPLS/PW/ETH/IP/...

        case PPPoE:
            type = ETH_P_IP;
            ip_offset += 8;
            break;
        case ARP:
        case RARP:
        {
            return dpi_packet_processing_datalink_layer(workflow, p_usec, &pkt_data,
                                                   (const void*)(packet + ip_offset),
                                                   pkt_len - ip_offset, pkt_len, type);

        }
           break;
        case VMLAB:
            type = get_uint16_ntohs(packet + ip_offset, VMLAB_ENCPTYPE_OFFSET);
            ip_offset += VMLAB_SIZE;
            goto strip_again;
        default:
            break;
    }

outer:
    if (type != ETH_P_IP && type != ETH_P_IPV6) {
        return PKT_DROP;
    } else {
        append_hardlink_proto_info(type);
        workflow->layers[++workflow->layer_cnt] = type + PROTOCOL_MAX;
    }

    memset(&tuple,0,sizeof(struct pkt_tuple_t));

    iph = (const struct dpi_iphdr *) &packet[ip_offset];
    pkt_data.ipversion=iph->version;
    if (iph->version == 4) {
        ip_len = ((uint16_t)iph->ihl * 4);
        proto = iph->protocol;
        iph6 = NULL;
        pkt_data.iph=iph;

        tuple.af=AF_INET;
        tuple.proto=proto;
        memcpy(tuple.src,iph->saddr,sizeof(iph->saddr));
        memcpy(tuple.dst,iph->daddr,sizeof(iph->saddr));
    } else if (iph->version == 6) {
        iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
        proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
        ip_len = sizeof(struct dpi_ipv6hdr);
        iph = NULL;
        if(proto == IPPROTO_DSTOPTS /* ipv6 destination option */) {
            const uint8_t *options = (const uint8_t*)&packet[ip_offset + ip_len];
            proto = options[0];
            ip_len += 8 * (options[1] + 1);
        }
        pkt_data.iph6=iph6;

        tuple.af=AF_INET6;
        tuple.proto=proto;
        memcpy(tuple.src,iph6->ip6_src,sizeof(iph6->ip6_src));
        memcpy(tuple.dst,iph6->ip6_dst,sizeof(iph6->ip6_dst));
    }
    else {
        return PKT_DROP;
    }

    /*udp的数据可能是gtp，如果是gtp数据，剥离出内层ip头*/
    if (proto == IPPROTO_UDP) {
        const struct dpi_udphdr *udp = (const struct dpi_udphdr *)&packet[ip_offset + ip_len];
        pkt_data.udph = udp;
        uint16_t sport = ntohs(udp->source);
        uint16_t dport = ntohs(udp->dest);

        if(tuple.af == AF_INET){
            tuple.proto=IPPROTO_UDP;
            tuple.sport=udp->source;
            tuple.dport=udp->dest;
        }else{
            tuple.proto=IPPROTO_UDP;
            tuple.sport=udp->source;
            tuple.dport=udp->dest;
        }
    }

    pkt_data.proto=proto;
    if(proto == IPPROTO_TCP){
        const struct dpi_tcphdr *tcp = (const struct dpi_tcphdr *)&packet[ip_offset + ip_len];
        pkt_data.tcph = tcp;
        if(tuple.af == AF_INET){
            tuple.proto=IPPROTO_TCP;
            tuple.sport=tcp->source;
            tuple.dport=tcp->dest;

        }else{
            tuple.proto=IPPROTO_TCP;
            tuple.sport=tcp->source;
            tuple.dport=tcp->dest;
        }
    }

    if (proto == IPPROTO_TCP || proto == IPPROTO_UDP) {
        append_hardlink_proto_info(proto);
    }

    // 通联帧跳过 acl 匹配
    if (workflow->is_tll)
    {
        return dpi_packet_processing_ip_layer(workflow, p_usec,
                                         NULL, &pkt_data,
                                         iph ? (const void*)iph : (const void*)iph6,
                                         pkt_len - ip_offset,
                                         pkt_len);
    }

    //MARK
    SdtAclMatchedRuleInfo  *acl_ret=NULL;
    acl_ret=sdtEngine_matchAclRules(workflow->pEngine, &tuple);
    if(acl_ret==NULL){
        ATOMIC_ADD_FETCH(&packet_nohit);
        return PKT_DROP;
    }else{
        sdt_acl_match_result_copy(acl_ret, &workflow->acl_result);
        ATOMIC_ADD_FETCH(&packet_hit);
    }

    //如果ACL HASH 没有被命中 -- 拜拜~
    if(0 == acl_ret->aclHashCnt)
    {
        return PKT_DROP;
    }

    return dpi_packet_processing_ip_layer(workflow, p_usec,
                                         NULL, &pkt_data,
                                         iph ? (const void*)iph : (const void*)iph6,
                                         pkt_len - ip_offset,
                                         pkt_len);
}


int get_flow_total_num(void)
{
    unsigned int i = 0;
    int total_num = 0;
    for (i = 0; i < g_config.dissector_thread_num; i++) {
        struct work_process_data *process = &flow_thread_info[i];
        total_num += dpi_flow_hash_size(process->hash);
    }

    return total_num;
}




/****************************************************************************************************************************
* SDT function
*****************************************************************************************************************************/
int sdt_acl_match_result_copy(SdtAclMatchedRuleInfo         *src, SdtAclMatchedRuleInfo  *dst)
{
    if(src==NULL || dst==NULL){
        return 0;
    }

    int hashCnt = src->aclHashCnt;

    //转储
    dst->aclHashCnt = hashCnt;
    memcpy(dst->aclHashCode, src->aclHashCode, sizeof(src->aclHashCode[0]) * hashCnt);

    dst->sumInfo_matchModeFlag   = src->sumInfo_matchModeFlag;
    dst->sumInfo_protoLayerFlag  = src->sumInfo_protoLayerFlag;
    dst->sumInfo_actionFlag      = src->sumInfo_actionFlag;
    dst->sumInfo_matchHintFlag   = src->sumInfo_matchHintFlag;

    if(sizeof(dst->bitmap_proto_id) != sizeof(src->bitmap_proto_id))
    {
        printf("ERROR: sdt_acl_match_result_copy bitmap_proto_id\n");
        abort();
    }
    memcpy(dst->bitmap_proto_id, src->bitmap_proto_id, sizeof(src->bitmap_proto_id));
    return 1;
}

int sdt_clean_flow_update_rule(void)
{
    uint32_t i;
    for (i = 0; i < g_config.dissector_thread_num; i++) {
        do_all_flow_free(i);
    }

    return 1;
}


int_to_stringlist protos_associated_map[] = {
	{
		// 即cwmp在http之上， 匹配cwmp时需要先打开http协议开关
		PROTOCOL_CWMP,
		{
			{ PROTOCOL_HTTP,			"http"},
			{ PROTOCOL_UNKNOWN,			NULL}
		},
	},

	/* 这里可以添加其他的关联协议 */


	{
		PROTOCOL_UNKNOWN,
		{
			{ PROTOCOL_UNKNOWN,			NULL}
		}
	}
};

/* 开启 master_proto 的关联协议*/
void sdt_enable_rule_protos_associated(int master_proto) {

	if (master_proto <= PROTOCOL_UNKNOWN || master_proto >= PROTOCOL_MAX)
		return;

	int i = 0;
	int j = 0;
	for (i = 0; protos_associated_map[i].value != PROTOCOL_UNKNOWN; i++) {
		if (protos_associated_map[i].value != master_proto)
			continue;

		for (j = 0; j < PROTOCOL_MAX; j++) {
			struct int_to_string associal = protos_associated_map[i].str_list[j];
			if (associal.value == PROTOCOL_UNKNOWN || !associal.strptr)
				break;

			g_config.protocol_switch[associal.value] = 1;
		}
	}
}

static int _get_proto_id(int protoid)
{
    int index = 0;
    const char * proto_name = NULL;
    char low_proto_name[64] = { 0 };
    for (pschema_t *schema = dpi_pschema_get_first(); schema; schema = dpi_pschema_get_next(schema)) {
        index = pschema_get_index(schema);
        proto_name = pschema_get_proto_name(schema);
        if (index == protoid) {
            break;
        }
    }

    if (proto_name == NULL) return -1;

    if (strcmp(proto_name, "common") == 0 ) return -1;

    if (strcmp(proto_name, "ssl_n") == 0) {
        strncpy(low_proto_name, "ssl", sizeof(low_proto_name));
    } else if (strcmp(proto_name, "X509Cer") == 0) {
        strncpy(low_proto_name, "x509", sizeof(low_proto_name));
    } else if (strcmp(proto_name, "ftp") == 0) {
        strncpy(low_proto_name, "ftp_control", sizeof(low_proto_name));

    } else {
        strncpy(low_proto_name, proto_name, sizeof(low_proto_name));
    }

    char inner_proto_name[64] = { 0 };
    for (int i = 0; i < PROTOCOL_MAX; ++i) {
        strncpy(inner_proto_name, protocol_name_array[i], sizeof(inner_proto_name) - 1);
        inner_proto_name[sizeof(inner_proto_name) - 1] = '\0'; // 确保字符串以 '\0' 结尾

        // 将 inner_proto_name 转换为小写
        for (char *p = inner_proto_name; *p; ++p) {
            *p = tolower(*p);
        }

        if (strcmp(inner_proto_name, low_proto_name) == 0) {
            return i;
        }
    }

    log_error("schema 协议 %s, id %d, 在 dpi 中未找到协议", proto_name, protoid);
    return -1;
}

int sdt_set_rule_proto_switch(void)
{
    int i;
    int ret=0;
    int *proto_array = NULL;
    int proto_num    = 0;
    SdtErrorMsg_t p_err_msg;
    int proto_id = -1;
    uint8_t origin_tll_switch;

    ret=sdtEngine_getProtocolsReferedByRules(&proto_array, &proto_num, &p_err_msg);
    if(ret<0){
        printf("get rule protocol list failed! errno:%d, err mesage:%s\n",
                            p_err_msg.lErrorCode, p_err_msg.pszErrorBuff);
        return -1;
    }

    // 先记下 通联日志的原始配置
    origin_tll_switch = g_config.protocol_switch[PROTOCOL_TLL];

//2024-10-24 牡丹江项目 要求ACL+预匹配+空Body packet_dump()时, 不要走协议解析
//2024-11-28 牡丹江现场 要求KS-YSJ 也能输出具体的协议层JSON元数据
    //如果没有启用规则字段: 就是空BODY 规则
    //开放所有协议 - 避免空BODY规则无法匹配
    if(0 == proto_num)
    {
        for(i=0;i<PROTOCOL_MAX;i++)
        {
            g_config.protocol_switch[i]=1;
        }
        // 通联日志使用原始配置, 不跟随规则变化
        g_config.protocol_switch[PROTOCOL_TLL] = origin_tll_switch;
        printf("已开放所有的协议解析模块\n");
        return 0;
    }

    /* 先关闭所有协议 */
    for(i=0;i<PROTOCOL_MAX;i++){
        g_config.protocol_switch[i]=0;
    }

    /* 在开启规则支持协议列表 */
    for(i=0;i<proto_num;i++)
    {
        proto_id = _get_proto_id(proto_array[i]);
        if (proto_id < 0) continue;

        // schema index 转换 proto id
        g_config.protocol_switch[proto_id]=1;
    }

    // 通联日志使用原始配置, 不跟随规则变化
    g_config.protocol_switch[PROTOCOL_TLL] = origin_tll_switch;

    /* 隧道协议要开启 */
    {
        g_config.protocol_switch[PROTOCOL_GRE] = 1;
        g_config.protocol_switch[PROTOCOL_GTP_U] = 1;
        g_config.protocol_switch[PROTOCOL_IPIP] = 1;
        g_config.protocol_switch[PROTOCOL_L2TP] = 1;
        g_config.protocol_switch[PROTOCOL_TEREDO] = 1;
    }

    //dbbasic特殊处理
    if(g_config.protocol_switch[PROTOCOL_DBBASIC] == 1){
        g_config.protocol_switch[PROTOCOL_MYSQL]=1;
        g_config.protocol_switch[PROTOCOL_TNS]=1;
        g_config.protocol_switch[PROTOCOL_PGSQL]=1;
        g_config.protocol_switch[PROTOCOL_TDS]=1;
    }

    //EMAIL 特殊处理
    if (g_config.protocol_switch[PROTOCOL_EMAIL] == 1) {
        g_config.protocol_switch[PROTOCOL_MAIL_ESMTP] = 1;
        g_config.protocol_switch[PROTOCOL_MAIL_SMTP] = 1;
        g_config.protocol_switch[PROTOCOL_MAIL_POP] = 1;
        g_config.protocol_switch[PROTOCOL_MAIL_IMAP] = 1;
    }

    /* x509 依赖 ssl*/
    if (g_config.protocol_switch[PROTOCOL_X509] == 1) {
        g_config.protocol_switch[PROTOCOL_SSL] = 1;
    }

    //MARK
    ///* 测试开启所有协议 */
    for(i=0;i<PROTOCOL_MAX;i++){
       g_config.protocol_switch[i]=1;
    }
    for(i=0;i<PROTOCOL_MAX;i++)
    {
        if(g_config.protocol_switch[i])
        {
            printf("协议开关状态 %s = %u\n", protocol_name_array[i], g_config.protocol_switch[i]);
        }
    }

    return 0;
}

void sdt_atomic_lock(int32_t *lock)
{
    //互斥锁 -- 只有第一个才能通过, 其他人都陷入loop
    while(ATOMIC_FETCH_ADD(lock))
    {
        usleep(1000 * ATOMIC_FETCH_SUB(lock)); //N ms
    }
}

void sdt_atomic_unlock(int32_t *lock)
{
    ATOMIC_FETCH_SUB(lock);
}


extern struct decode_t decode_http;
extern struct decode_t decode_mysql;
extern struct decode_t decode_cwmp;
extern struct decode_t decode_socks;
extern struct decode_t decode_ssl;
extern struct decode_t decode_ssh;

struct decode_t *decode[]={
    &decode_socks,  // http in socks, 需要更高的优先级
    &decode_cwmp,   // cwmp over http, 需要更高的优先级. 真是丑陋的实现方式!
    &decode_http,
    &decode_mysql,
    &decode_ssl,
    &decode_ssh,
    NULL,
};
