/****************************************************************************************
 * 文 件 名 : dpi_x509.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *设计: wangy   2018/07/06
 *编码: licl    2018/07/06
 *修改: wangch  2019/11/01
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <glib.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <jhash.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_sdp.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_ssl.h"

#include "openssl/md5.h"
#include "openssl/sha.h"
#include "openssl/x509v3.h"

static const char *X509_PROTO_NAME = "X509Cer";

GHashTable *x509_filter_table;
extern struct global_config g_config;
extern struct rte_mempool*  tbl_log_mempool;

#define GET3BYTES(x) ((get_uint32_ntohl((x), 0)) >> 8)


enum x509_index_em{
    EM_509_PROTO,
    EM_509_TYPE,
    EM_509_VERSION,
    EM_509_SEQ_LEN,
    EM_509_SEQUENCE,

    EM_509_ISSUER,
    EM_509_ISSUER_LENGTH,
    EM_509_ISSUER_NUM,
    EM_509_ISSUER_FIRSTNAME,
    EM_509_ISSUER_NATION,
    EM_509_ISSUER_LOCALITY,
    EM_509_ISSUER_PROVINCE,
    EM_509_ISSUER_PARTY,
    EM_509_ISSUER_UNIT,

    EM_509_ISSUER_POST_OFFICE,
    EM_509_ISSUER_LASTNAME,
    EM_509_ISSUER_ID,
    EM_509_ISSUER_ROAD,
    EM_509_ISSUER_TITLE,
    EM_509_ISSUER_DESCRIPTION,
    EM_509_ISSUER_JOB,
    EM_509_ISSUER_POST_ADDR,
    EM_509_ISSUER_POST_CODE,
    EM_509_ISSUER_OFFICE,
    EM_509_ISSUER_PHONE,
    EM_509_ISSUER_TELE,
    EM_509_ISSUER_FAX,
    EM_509_ISSUER_X121,
    EM_509_ISSUER_ISDN,
    EM_509_ISSUER_REGISTER_ADDR,
    EM_509_ISSUER_TRANS_MODE,
    EM_509_ISSUER_SHOW_ADDR,
    EM_509_ISSUER_APP_ENV,

    EM_509_SUBJECT,
    EM_509_SUBJECT_LENGTH,
    EM_509_SUBJECT_NUM,
    EM_509_SUBJECT_FIRSTNAME,
    EM_509_SUBJECT_NATION,
    EM_509_SUBJECT_LOCALITY,
    EM_509_SUBJECT_PROVINCE,
    EM_509_SUBJECT_PARTY,
    EM_509_SUBJECT_UNIT,

    EM_509_SUBJECT_POST_OFFICE,
    EM_509_SUBJECT_LASTNAME,
    EM_509_SUBJECT_ID,
    EM_509_SUBJECT_ROAD,
    EM_509_SUBJECT_TITLE,
    EM_509_SUBJECT_DESCRIPTION,
    EM_509_SUBJECT_JOB,
    EM_509_SUBJECT_POST_ADDR,
    EM_509_SUBJECT_POST_CODE,
    EM_509_SUBJECT_OFFICE,
    EM_509_SUBJECT_PHONE,
    EM_509_SUBJECT_TELE,
    EM_509_SUBJECT_FAX,
    EM_509_SUBJECT_X121,
    EM_509_SUBJECT_ISDN,
    EM_509_SUBJECT_REGISTER_ADDR,
    EM_509_SUBJECT_TRANS_MODE,
    EM_509_SUBJECT_SHOW_ADDR,
    EM_509_SUBJECT_APP_ENV,

    EM_509_BEGINTIME,
    EM_509_ENDTIME,
    EM_509_TOTALTIME,
    EM_509_LASTTIME,

    EM_509_PUBKEY_ALGORITHM,
    EM_509_PUBKEY_LENGTH,
    EM_509_PUBKEY_MOD,
    EM_509_PUBKEY_EXP,
    EM_509_RSA_MOD,
    EM_509_RSA_EXP,

    EM_509_DH_MOD,
    EM_509_DH_BASE,
    EM_509_DH_PUBLICKEY,
    EM_509_DSA_P,
    EM_509_DSA_Q,
    EM_509_DSA_G,

    EM_509_SIGNATURE_ALG,
    EM_509_SIGNATURE,

    EM_509_AUTH_KEYID,
    EM_509_SUBJ_KEYID,
    EM_509_KEY_USAGE,
    EM_509_SECKEY_BEGINTIME,
    EM_509_SECKEY_ENDTIME,
    EM_509_POLICY,
    EM_509_DNS_NUM,
    EM_509_SUBJ_DNS,
    EM_509_SUBJ_IP,
    EM_509_SUBJ_OTHER,
    EM_509_AUTH_DNS,
    EM_509_AUTH_IP,
    EM_509_AUTH_OTHER,
    EM_509_SUBJ_DIRECT,
    EM_509_BEFORE_NEW,
    EM_509_BEFORE_OLD,
    EM_509_KEY_EXT,
    EM_509_CRL_SOURCE,
    EM_509_CRL_REASON,
    EM_509_CRL_AUTHORITY,
    EM_509_CRL_DIST_POINTS,
    EM_509_METHOD,
    EM_509_LOCATION,

    EM_509_PRINT_ALG,
    EM_509_PRINT,

    EM_509_IS_WILDCARD,
    EM_509_IS_MULTI_DNS,
    EM_509_IS_SELF_SIGNED,

    EM_509_PATH,

    EM_509_SUBJ_INTERM_CN,
    EM_509_SUBJ_INTERM_ON,
    EM_509_ISSUER_INTERM,
    EM_509_SUBJ_ROOT_CN,
    EM_509_SUBJ_ROOT_ON,
    EM_509_ISSUER_ROOT,

    EM_509_LEN,
    EM_509_NUM,
    EM_509_SIGN_VALID,
    EM_509_SUSPEND,
    EM_509_EXT_NUMS,
    EM_509_EXTENSION,
    EM_509_EXT_AUTH_ACCESS,
    EM_509_EXT_BASIC_CONS_CA,
    EM_509_EXT_BASIC_CONS_PATHLEN,
    EM_509_INDB_TIME,
    EM_509_SUBJECT_PUBKEY,

    EM_509_MAX
};

static dpi_field_table x509_field_array[EM_509_MAX] = {
    DPI_FIELD_D(EM_509_PROTO,                      YV_FT_BYTES,        "proto"),
    DPI_FIELD_D(EM_509_TYPE,                       YV_FT_BYTES,        "type"),
    DPI_FIELD_D(EM_509_VERSION,                    YV_FT_INT64,        "version"),
    DPI_FIELD_D(EM_509_SEQ_LEN,                    YV_FT_UINT32,        "sequence_len"),
    DPI_FIELD_D(EM_509_SEQUENCE,                   YV_FT_BYTES,        "sequence"),

    DPI_FIELD_D(EM_509_ISSUER,                     YV_FT_BYTES,        "issuer"),
    DPI_FIELD_D(EM_509_ISSUER_LENGTH,              YV_FT_UINT32,        "issuer_length"),
    DPI_FIELD_D(EM_509_ISSUER_NUM,                 YV_FT_INT32,        "issuer_num"),
    DPI_FIELD_D(EM_509_ISSUER_FIRSTNAME,           YV_FT_BYTES,        "issuer_firstname"),
    DPI_FIELD_D(EM_509_ISSUER_NATION,              YV_FT_BYTES,        "issuer_nation"),
    DPI_FIELD_D(EM_509_ISSUER_LOCALITY,            YV_FT_BYTES,        "issuer_locality"),
    DPI_FIELD_D(EM_509_ISSUER_PROVINCE,            YV_FT_BYTES,        "issuer_province"),
    DPI_FIELD_D(EM_509_ISSUER_PARTY,               YV_FT_BYTES,        "issuer_party"),
    DPI_FIELD_D(EM_509_ISSUER_UNIT,                YV_FT_BYTES,        "issuer_unit"),

    DPI_FIELD_D(EM_509_ISSUER_POST_OFFICE,         YV_FT_NIL,          "issuer_email"),
    DPI_FIELD_D(EM_509_ISSUER_LASTNAME,            YV_FT_BYTES,        "issuer_lastname"),
    DPI_FIELD_D(EM_509_ISSUER_ID,                  YV_FT_NIL,          "issuer_id"),
    DPI_FIELD_D(EM_509_ISSUER_ROAD,                YV_FT_NIL,          "issuer_road"),
    DPI_FIELD_D(EM_509_ISSUER_TITLE,               YV_FT_NIL,          "issuer_title"),
    DPI_FIELD_D(EM_509_ISSUER_DESCRIPTION,         YV_FT_NIL,          "issuer_description"),
    DPI_FIELD_D(EM_509_ISSUER_JOB,                 YV_FT_NIL,          "issuer_job"),
    DPI_FIELD_D(EM_509_ISSUER_POST_ADDR,           YV_FT_NIL,          "issuer_post_addr"),
    DPI_FIELD_D(EM_509_ISSUER_POST_CODE,           YV_FT_NIL,          "issuer_post_coding"),
    DPI_FIELD_D(EM_509_ISSUER_OFFICE,              YV_FT_NIL,          "issuer_office"),
    DPI_FIELD_D(EM_509_ISSUER_PHONE,               YV_FT_NIL,          "issuer_phone_number"),
    DPI_FIELD_D(EM_509_ISSUER_TELE,                YV_FT_NIL,          "issuer_telegraphy_number"),
    DPI_FIELD_D(EM_509_ISSUER_FAX,                 YV_FT_NIL,          "issuer_fax_number"),
    DPI_FIELD_D(EM_509_ISSUER_X121,                YV_FT_NIL,          "issuer_X121_addr"),
    DPI_FIELD_D(EM_509_ISSUER_ISDN,                YV_FT_NIL,          "issuer_ISDN"),
    DPI_FIELD_D(EM_509_ISSUER_REGISTER_ADDR,       YV_FT_NIL,          "issuer_register_addr"),
    DPI_FIELD_D(EM_509_ISSUER_TRANS_MODE,          YV_FT_NIL,          "issuer_trans_mode"),
    DPI_FIELD_D(EM_509_ISSUER_SHOW_ADDR,           YV_FT_NIL,          "issuer_show_addr"),
    DPI_FIELD_D(EM_509_ISSUER_APP_ENV,             YV_FT_NIL,          "issuer_app_env"),

    DPI_FIELD_D(EM_509_SUBJECT,                    YV_FT_BYTES,        "subject"),
    DPI_FIELD_D(EM_509_SUBJECT_LENGTH,             YV_FT_UINT32,       "subject_length"),
    DPI_FIELD_D(EM_509_SUBJECT_NUM,                YV_FT_INT32,        "subject_num"),
    DPI_FIELD_D(EM_509_SUBJECT_FIRSTNAME,          YV_FT_BYTES,        "subject_firstname"),
    DPI_FIELD_D(EM_509_SUBJECT_NATION,             YV_FT_BYTES,        "subject_nation"),
    DPI_FIELD_D(EM_509_SUBJECT_LOCALITY,           YV_FT_BYTES,        "subject_locality"),
    DPI_FIELD_D(EM_509_SUBJECT_PROVINCE,           YV_FT_BYTES,        "subject_province"),
    DPI_FIELD_D(EM_509_SUBJECT_PARTY,              YV_FT_BYTES,        "subject_party"),
    DPI_FIELD_D(EM_509_SUBJECT_UNIT,               YV_FT_BYTES,        "subject_unit"),

    DPI_FIELD_D(EM_509_SUBJECT_POST_OFFICE,        YV_FT_NIL,          "subject_email"),
    DPI_FIELD_D(EM_509_SUBJECT_LASTNAME,           YV_FT_BYTES,        "subject_lastname"),
    DPI_FIELD_D(EM_509_SUBJECT_ID,                 YV_FT_NIL,          "subject_id"),
    DPI_FIELD_D(EM_509_SUBJECT_ROAD,               YV_FT_NIL,          "subject_road"),
    DPI_FIELD_D(EM_509_SUBJECT_TITLE,              YV_FT_NIL,          "subject_title"),
    DPI_FIELD_D(EM_509_SUBJECT_DESCRIPTION,        YV_FT_NIL,          "subject_description"),
    DPI_FIELD_D(EM_509_SUBJECT_JOB,                YV_FT_NIL,          "subject_job"),
    DPI_FIELD_D(EM_509_SUBJECT_POST_ADDR,          YV_FT_NIL,          "subject_post_addr"),
    DPI_FIELD_D(EM_509_SUBJECT_POST_CODE,          YV_FT_NIL,          "subject_post_coding"),
    DPI_FIELD_D(EM_509_SUBJECT_OFFICE,             YV_FT_NIL,          "subject_office"),
    DPI_FIELD_D(EM_509_SUBJECT_PHONE,              YV_FT_NIL,          "subject_phone_number"),
    DPI_FIELD_D(EM_509_SUBJECT_TELE,               YV_FT_NIL,          "subject_telegraphy_number"),
    DPI_FIELD_D(EM_509_SUBJECT_FAX,                YV_FT_NIL,          "subject_fax_number"),
    DPI_FIELD_D(EM_509_SUBJECT_X121,               YV_FT_NIL,          "subject_X121_addr"),
    DPI_FIELD_D(EM_509_SUBJECT_ISDN,               YV_FT_NIL,          "subject_ISDN"),
    DPI_FIELD_D(EM_509_SUBJECT_REGISTER_ADDR,      YV_FT_NIL,          "subject_register_addr"),
    DPI_FIELD_D(EM_509_SUBJECT_TRANS_MODE,         YV_FT_NIL,          "subject_trans_mode"),
    DPI_FIELD_D(EM_509_SUBJECT_SHOW_ADDR,          YV_FT_NIL,          "subject_show_addr"),
    DPI_FIELD_D(EM_509_SUBJECT_APP_ENV,            YV_FT_NIL,          "subject_app_env"),

    DPI_FIELD_D(EM_509_BEGINTIME,                  YV_FT_TIME,          "begintime"),
    DPI_FIELD_D(EM_509_ENDTIME,                    YV_FT_TIME,          "endtime"),
    DPI_FIELD_D(EM_509_TOTALTIME,                  YV_FT_UINT32,        "totaltime"),
    DPI_FIELD_D(EM_509_LASTTIME,                   YV_FT_UINT32,        "lasttime"),

    DPI_FIELD_D(EM_509_PUBKEY_ALGORITHM,           YV_FT_BYTES,        "algorithm"),
    DPI_FIELD_D(EM_509_PUBKEY_LENGTH,              YV_FT_UINT32,       "key_length"),
    DPI_FIELD_D(EM_509_PUBKEY_MOD,                 YV_FT_BYTES,        "key_module"),
    DPI_FIELD_D(EM_509_PUBKEY_EXP,                 YV_FT_BYTES,        "key_exponent"),
    DPI_FIELD_D(EM_509_RSA_MOD,                    YV_FT_NIL,          "rsa_module"),
    DPI_FIELD_D(EM_509_RSA_EXP,                    YV_FT_NIL,          "rsa_exponent"),
    DPI_FIELD_D(EM_509_DH_MOD,                     YV_FT_NIL,          "dh_module"),
    DPI_FIELD_D(EM_509_DH_BASE,                    YV_FT_NIL,          "dh_base"),
    DPI_FIELD_D(EM_509_DH_PUBLICKEY,               YV_FT_NIL,          "dh_publickey"),
    DPI_FIELD_D(EM_509_DSA_P,                      YV_FT_NIL,          "dsa_p"),
    DPI_FIELD_D(EM_509_DSA_Q,                      YV_FT_NIL,          "dsa_q"),
    DPI_FIELD_D(EM_509_DSA_G,                      YV_FT_NIL,          "dsa_g"),

    DPI_FIELD_D(EM_509_SIGNATURE_ALG,              YV_FT_BYTES,        "signature_alg"),
    DPI_FIELD_D(EM_509_SIGNATURE,                  YV_FT_NIL,          "signature"),

    DPI_FIELD_D(EM_509_AUTH_KEYID,                 YV_FT_BYTES,        "auth_key_id"),
    DPI_FIELD_D(EM_509_SUBJ_KEYID,                 YV_FT_BYTES,        "subj_key_id"),
    DPI_FIELD_D(EM_509_KEY_USAGE,                  YV_FT_BYTES,        "key_usage"),
    DPI_FIELD_D(EM_509_SECKEY_BEGINTIME,           YV_FT_NIL,          "secretkey_begintime"),
    DPI_FIELD_D(EM_509_SECKEY_ENDTIME,             YV_FT_NIL,          "secretkey_endtime"),
    DPI_FIELD_D(EM_509_POLICY,                     YV_FT_BYTES,        "policy"),
    DPI_FIELD_D(EM_509_DNS_NUM,                    YV_FT_BYTES,        "dns_num"),
    DPI_FIELD_D(EM_509_SUBJ_DNS,                   YV_FT_BYTES,        "subj_dns"),
    DPI_FIELD_D(EM_509_SUBJ_IP,                    YV_FT_BYTES,        "subj_ip"),
    DPI_FIELD_D(EM_509_SUBJ_OTHER,                 YV_FT_NIL,          "subj_other"),
    DPI_FIELD_D(EM_509_AUTH_DNS,                   YV_FT_BYTES,        "issuer_dns"),
    DPI_FIELD_D(EM_509_AUTH_IP,                    YV_FT_NIL,          "issuer_ip"),
    DPI_FIELD_D(EM_509_AUTH_OTHER,                 YV_FT_NIL,          "issuer_other"),
    DPI_FIELD_D(EM_509_SUBJ_DIRECT,                YV_FT_NIL,          "issuer_direct"),
    DPI_FIELD_D(EM_509_BEFORE_NEW,                 YV_FT_NIL,          "before_new"),
    DPI_FIELD_D(EM_509_BEFORE_OLD,                 YV_FT_NIL,          "before_old"),
    DPI_FIELD_D(EM_509_KEY_EXT,                    YV_FT_BYTES,        "key_ext"),
    DPI_FIELD_D(EM_509_CRL_SOURCE,                 YV_FT_NIL,          "crl_source"),
    DPI_FIELD_D(EM_509_CRL_REASON,                 YV_FT_NIL,          "crl_reason"),
    DPI_FIELD_D(EM_509_CRL_AUTHORITY,              YV_FT_NIL,          "crl_authority"),
    DPI_FIELD_D(EM_509_CRL_DIST_POINTS,            YV_FT_BYTES,        "crl_dist_points"), 
    DPI_FIELD_D(EM_509_METHOD,                     YV_FT_NIL,          "access_method"),
    DPI_FIELD_D(EM_509_LOCATION,                   YV_FT_NIL,          "access_location"),

    DPI_FIELD_D(EM_509_PRINT_ALG,                  YV_FT_BYTES,        "print_alg"),
    DPI_FIELD_D(EM_509_PRINT,                      YV_FT_BYTES,        "print"),

    DPI_FIELD_D(EM_509_IS_WILDCARD,                YV_FT_UINT8,        "is_wildcard"),
    DPI_FIELD_D(EM_509_IS_MULTI_DNS,               YV_FT_UINT8,        "is_multi_dns"),
    DPI_FIELD_D(EM_509_IS_SELF_SIGNED,             YV_FT_UINT8,        "is_self_signed"),

    DPI_FIELD_D(EM_509_PATH,                       YV_FT_BYTES,        "filename"),

    DPI_FIELD_D(EM_509_SUBJ_INTERM_CN,             YV_FT_BYTES,        "interm_subj_commonname"),
    DPI_FIELD_D(EM_509_SUBJ_INTERM_ON,             YV_FT_BYTES,        "interm_subj_orgname"),
    DPI_FIELD_D(EM_509_ISSUER_INTERM,              YV_FT_BYTES,        "interm_issuer_name"),
    DPI_FIELD_D(EM_509_SUBJ_ROOT_CN,               YV_FT_BYTES,        "root_subj_commonname"),
    DPI_FIELD_D(EM_509_SUBJ_ROOT_ON,               YV_FT_BYTES,        "root_subj_orgname"),
    DPI_FIELD_D(EM_509_ISSUER_ROOT,                YV_FT_BYTES,        "root_issuer_name"),

    DPI_FIELD_D(EM_509_LEN,                        YV_FT_UINT32,       "cert_len"),
    DPI_FIELD_D(EM_509_NUM,                        YV_FT_UINT8,        "cert_num"),
    DPI_FIELD_D(EM_509_SIGN_VALID,                 YV_FT_INT32,        "sign_valid"),
    DPI_FIELD_D(EM_509_SUSPEND,                    YV_FT_NIL,          "suspend"),
    DPI_FIELD_D(EM_509_EXT_NUMS,                   YV_FT_INT32,        "ext_sums"),
    DPI_FIELD_D(EM_509_EXTENSION,                  YV_FT_BYTES,        "extension"),
    DPI_FIELD_D(EM_509_EXT_AUTH_ACCESS,            YV_FT_BYTES,        "authAccess"),
    DPI_FIELD_D(EM_509_EXT_BASIC_CONS_CA,          YV_FT_INT32,        "basicConsCA"),
    DPI_FIELD_D(EM_509_EXT_BASIC_CONS_PATHLEN,     YV_FT_INT32,        "basicConsPathLen"),
    DPI_FIELD_D(EM_509_INDB_TIME,                  YV_FT_BYTES,        "indb_time"),
    DPI_FIELD_D(EM_509_SUBJECT_PUBKEY,             EM_F_TYPE_STRING,      "subj_pubkey"),

};



static void
x509_field_element(struct tbl_log *log_ptr,struct flow_info *flow _U_, int direction _U_, struct dpi_x509_st *info, int *idx, int i)
{
    char str[1024];
    time_t not_after;
    time_t now;
    const char* print_alg;

    //int local_idx=*idx;
    switch(i){
    case EM_509_VERSION:
        write_one_snum_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->version);
        break;
    case EM_509_SEQUENCE:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 
            (const uint8_t *)info->serialNum_val_ptr, info->serialNum_val_len);
        break;
    case EM_509_SIGNATURE_ALG:
        // 签名信息
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->signature_alg);
        break;
    case EM_509_ISSUER:
    {   //issuer info
        if (info->issuer_val_ptr == NULL || info->issuer_val_len == 0) {
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        }

        char *tmp_buff = str;
        int  tmp_len = info->issuer_val_len;
        if (tmp_len > (int)sizeof(str))
            tmp_buff = (char*)malloc(tmp_len);

        for(int i=0; i<tmp_len; i++) {
            if (info->issuer_val_ptr[i] == '\0')
                tmp_buff[i] = ',';
            else
                tmp_buff[i] = info->issuer_val_ptr[i];
        }
        //跳过首位','
        write_one_chars_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp_buff+1, tmp_len-1);
        if (tmp_buff != str)
            free(tmp_buff);
        break;
    }
    case EM_509_ISSUER_LENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->issuer_val_len);
        break;
    case EM_509_SUBJECT:
    {   // subject info
        if (info->subject_val_ptr == NULL || info->subject_val_len == 0) {
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            break;
        }

        char *tmp_buff = str;
        int tmp_len = info->subject_val_len;
        if (tmp_len > (int)sizeof(str))
            tmp_buff = (char*)malloc(tmp_len);

        for(int i=0; i<tmp_len; i++) {
            if (info->subject_val_ptr[i] == '\0')
                tmp_buff[i] = ',';
            else
                tmp_buff[i] = info->subject_val_ptr[i];
        }
        write_one_chars_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, tmp_buff+1, tmp_len-1);
        if (tmp_buff != str)
            free(tmp_buff);
        break;
    }
    case EM_509_SUBJECT_LENGTH:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->subject_val_len);
        break;
    case EM_509_ISSUER_FIRSTNAME:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->issuer_name[DPI_X509_NAME_COMMON]);
        break;
    case EM_509_ISSUER_NATION:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->issuer_name[DPI_X509_NAME_COUNTRY]);
        break;
    case EM_509_ISSUER_LOCALITY:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->issuer_name[DPI_X509_NAME_CITY]);
        break;
    case EM_509_ISSUER_PROVINCE:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->issuer_name[DPI_X509_NAME_PROVINCE]);
        break;
    case EM_509_ISSUER_PARTY:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->issuer_name[DPI_X509_NAME_ORG]);
        break;
    case EM_509_ISSUER_UNIT:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->issuer_name[DPI_X509_NAME_ORG_UNIT]);
        break;
    case EM_509_ISSUER_ROAD:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->issuer_name[DPI_X509_NAME_STREET]);
        break;
    case EM_509_SUBJECT_FIRSTNAME:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->subject_name[DPI_X509_NAME_COMMON]);
        break;
    case EM_509_SUBJECT_NATION:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->subject_name[DPI_X509_NAME_COUNTRY]);
        break;
    case EM_509_SUBJECT_LOCALITY:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->subject_name[DPI_X509_NAME_CITY]);
        break;
    case EM_509_SUBJECT_PROVINCE:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->subject_name[DPI_X509_NAME_PROVINCE]);
        break;
    case EM_509_SUBJECT_PARTY:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->subject_name[DPI_X509_NAME_ORG]);
        break;
    case EM_509_SUBJECT_UNIT:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->subject_name[DPI_X509_NAME_ORG_UNIT]);
        break;
    case EM_509_SUBJECT_ROAD:
        write_string_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, info->subject_name[DPI_X509_NAME_STREET]);
        break;
    case EM_509_BEGINTIME:
        write_uint64_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, mktime(&info->begintime));
        break;
    case EM_509_ENDTIME:
        write_uint64_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, mktime(&info->endtime));
        break;
    // case EM_509_SECKEY_BEGINTIME:
    //     write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, ret);
    //     break;
    // case EM_509_SECKEY_ENDTIME:
    //     write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, str, ret);
    //     break;
    case EM_509_TOTALTIME:
        not_after = mktime(&info->endtime);
         write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (not_after - mktime(&info->begintime)) / 86400);
        break;
    case EM_509_LASTTIME:
        now = time(0);
        not_after = mktime(&info->endtime);
        if(not_after > now)
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (not_after - now) / 86400);
        else
            write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 0);
        break;
    case EM_509_PUBKEY_MOD:
        write_multi_num_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, (uint8_t*)info->pubKey_val_ptr, info->pubKey_val_len);
        break;
    case EM_509_KEY_USAGE:
        write_fstring_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "%u", info->extension.key_usage);
        break;
    case EM_509_AUTH_KEYID:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.auth_key_id, strlen(info->extension.auth_key_id));
        break;
    case EM_509_SUBJ_KEYID:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.sub_key_id, strlen(info->extension.sub_key_id));
        break;
    case EM_509_SUBJ_DNS:
        if (info->subject_alt_DNSNum > 0)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.sub_alt_name, strlen(info->extension.sub_alt_name));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_509_DNS_NUM:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->subject_alt_DNSNum);
        break;
    case EM_509_POLICY:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.cert_policies, strlen(info->extension.cert_policies));
        break;
    case EM_509_CRL_DIST_POINTS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.crl_dist_points, strlen(info->extension.crl_dist_points));
        break;
    case EM_509_PRINT_ALG:
        print_alg = g_config.x509_fingerprint_alg == 0 ? "MD5" : "SHA1";
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, print_alg, strlen(print_alg));
        break;
    case EM_509_PRINT:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->fingerprint, strlen(info->fingerprint));
        break;
    case EM_509_EXTENSION:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.extIDSet, strlen(info->extension.extIDSet));
        break;
    case EM_509_EXT_AUTH_ACCESS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.auth_info_access, strlen(info->extension.auth_info_access));
        break;
    case EM_509_EXT_BASIC_CONS_CA:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.basic_cons.CA);
        break;
    case EM_509_EXT_BASIC_CONS_PATHLEN:
        write_uint64_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.basic_cons.pathLen);
        break;
    case EM_509_EXT_NUMS:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->extension.extCount);
        break;
    case EM_509_SUBJECT_PUBKEY:
        write_multi_num_reconds(log_ptr->record, idx,  TBL_LOG_MAX_LEN, (uint8_t*)info->subjectPubKey_val_ptr, info->subjectPubKey_val_len);
        break;
    default:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    
    }

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = x509_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif

#if 0
    strcpy(str, flow->real_protocol_id == PROTOCOL_FTPS ? "ftps" : "ssl");
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "X.509", 5);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->version);

    //serial number
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->serialNum_val_len);
    write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint8_t*)info->serialNum_val_ptr, info->serialNum_val_len);

    //issuer info
    write_one_chars_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->issuer_val_ptr, info->issuer_val_len);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->issuer_val_len);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->issuer_num);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->issuer_cn);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->issuer_c);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->issuer_l);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->issuer_st);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->issuer_o);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->issuer_ou);
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 19);

    //subject info
    write_one_chars_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->subject_val_ptr, info->subject_val_len);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->subject_val_len);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->subject_num);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->subject_cn);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->subject_c);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->subject_l);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->subject_st);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->subject_o);
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->subject_ou);
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 19);

    //生效时间,失效时间,总时间,剩余时间
    ret = snprintf(str, 64, "%04d-%02d-%02d %02d:%02d:%02d",
                        info->begintime.tm_year + 1900,
                        info->begintime.tm_mon + 1,
                        info->begintime.tm_mday,
                        info->begintime.tm_hour,
                        info->begintime.tm_min,
                        info->begintime.tm_sec);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, ret);
    ret = snprintf(str, 64, "%04d-%02d-%02d %02d:%02d:%02d",
                        info->endtime.tm_year + 1900,
                        info->endtime.tm_mon + 1,
                        info->endtime.tm_mday,
                        info->endtime.tm_hour,
                        info->endtime.tm_min,
                        info->endtime.tm_sec);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, ret);
    time_t not_after = mktime(&info->endtime);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (not_after - mktime(&info->begintime)) / 86400);
    time_t now = time(0);
    if(not_after > now)
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (not_after - now) / 86400);
    else
        write_one_snum_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (long)(now - not_after) / (-86400));

    //公钥信息
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->pubKey_alg);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->pubKey_val_len);
    write_multi_num_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, (uint8_t*)info->pubKey_val_ptr, info->pubKey_val_len);
    write_one_str_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->pubKey_exp_val_ptr, info->pubKey_exp_val_len);

    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 8);

    //签名信息
    write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info->signature_alg);
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);//signature
    
    //扩展信息,如有需求,继续增加
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[4].ptr, info->extension[4].len);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[3].ptr, info->extension[3].len);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[1].ptr, info->extension[1].len);
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 2);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[5].ptr, info->extension[5].len);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->subject_alt_DNSNum);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[6].ptr, info->extension[6].len);
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 2);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[7].ptr, info->extension[7].len);
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 5);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[2].ptr, info->extension[2].len);
    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 5);

    //指纹［链］信息
    const char* print_alg = g_config.x509_fingerprint_alg == 0 ? "MD5" : "SHA1";
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, print_alg, strlen(print_alg));
    for(ret=0; ret < sslinfo->CertificatesNums; ret++){
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info[ret].fingerprint, strlen(info[ret].fingerprint));
        log_ptr->record[idx-1]=',';//GOD BLESS ME
    }
    log_ptr->record[idx-1]='|';

    //EM_509_IS_WILDCARD
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->is_wildcard);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->subject_alt_DNSNum ? 1 : 0);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->is_self_signed);

    if(info->inDatabase){
        //EM_509_PATH
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->fingerprint, strlen(info->fingerprint));
        idx -= 1;//GOD BLESS ME
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ".cer", 4);
    }
    else{
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
    }

    if(sslinfo->CertificatesNums == 2){
        //EM_509_SUBJ_INTERM_CN
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 3);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[1].subject_cn);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[1].subject_o);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[1].issuer_cn);
    }
    else if(sslinfo->CertificatesNums == 3){
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[1].subject_cn);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[1].subject_o);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[1].issuer_cn);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[2].subject_cn);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[2].subject_o);
        write_string_reconds(log_ptr->record, &idx,  TBL_LOG_MAX_LEN, info[2].issuer_cn);
    }
    else{
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 6);
    }

    //证书长度与级数
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->length);
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, sslinfo->CertificatesNums);

    //签名是否有效,证书是否吊销
    if(g_config.x509_verify_switch)
        write_one_snum_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->sign_valid);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    //扩展信息个数及集合
    write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extCount);
    write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->extension[0].ptr, info->extension[0].len);

    if(info->inDatabase)
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, g_config.time_str, STD_TIME_LEN);
    else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

#endif
    return;
}



int 
write_x509_log(struct flow_info *flow, int direction, struct dpi_x509_st *info, SdtMatchResult *match_result _U_)
{
    int *x509_reflect_array=map_fields_get_array(PROTOCOL_X509);
    if(NULL==x509_reflect_array){
        return 0;
    }

    int idx = 0,i=0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow,PROTOCOL_X509);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, X509_PROTO_NAME);

    for(i=0; i<EM_509_MAX;i++){
        x509_field_element(log_ptr,flow, direction, info, &idx, i);
    }

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_X509;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;


    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

//证书写入tbl目录
static int write_x509(struct flow_info *flow _U_, struct dpi_x509_st *info, const uint8_t *payload, uint32_t payload_len)
{
    FILE *fp;
    char filename[128];
    char filename_done[128];
    int len=0;

    snprintf(filename, 128, "%s/%s", g_config.tbl_out_dir, X509_PROTO_NAME);
    struct stat st;
    if (0 != stat(filename, &st)) // 是否已经创建
        mkdirs(filename);

    len = snprintf(filename, 128, "%s/%s/%s.cer.writing", g_config.tbl_out_dir, X509_PROTO_NAME, info->fingerprint);
    if((fp = fopen(filename, "w"))){
        fwrite(payload, payload_len, 1, fp);
        fclose(fp);
        memcpy(filename_done, filename, len);
        filename_done[len - 8] = 0;
        rename(filename, filename_done);
    }
    else{
        DPI_SYS_LOG(DPI_LOG_WARNING, "can't open file");
        return -1;
    }

    return 0;
}

static void find_info_in_subject_dns(struct dpi_x509_st *info, const uint8_t *str, uint32_t len)
{
    const uint8_t*begin;
    uint8_t count = 0;
    while( (begin = memstr(str, "DNS:", len)) ){
        if(begin[4] == '*')
            info->is_wildcard = 1;
        count += 1;
        len -= (begin + 4 - str);
        str = begin + 4;
    }
    info->subject_alt_DNSNum = count;
}

static size_t extension_data_copy(BUF_MEM *bptr, char *dst, size_t max_len)
{
    size_t  len;

    if (bptr == NULL || bptr->length == 0)
        return 0;

    len = DPI_MIN(bptr->length, max_len);
    strncpy(dst, bptr->data, len);

    return len;
}

static void dissect_common_ext(X509_EXTENSION *x509_ext, char *out, size_t max_len)
{
    BIO         *tmp_bio;
    BUF_MEM     *bptr = NULL;

    tmp_bio = BIO_new(BIO_s_mem());
    if(!tmp_bio)
        return;

    X509V3_EXT_print(tmp_bio, x509_ext, 0, 0);
    BIO_get_mem_ptr(tmp_bio, &bptr);

    extension_data_copy(bptr, out, max_len);

    BIO_free(tmp_bio);
}

#define extension_arg(arg) arg, sizeof(arg)-1


static void dissect_ext_key_usage(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    dissect_common_ext(x509_ext, extension_arg(info->extension.ext_key_usage));
}

static void dissect_subject_key_id(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    dissect_common_ext(x509_ext, extension_arg(info->extension.sub_key_id));
}

static void dissect_auth_key_id(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    dissect_common_ext(x509_ext, extension_arg(info->extension.auth_key_id));
}

static void dissect_cert_policies(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    int     i, j;
    int     write_length = 0;

    int                  cert_polic_cnt;
    CERTIFICATEPOLICIES *cert_polic;

    cert_polic = X509V3_EXT_d2i(x509_ext);
    g_return_if_fail(cert_polic != NULL);

    cert_polic_cnt = sk_POLICYINFO_num(cert_polic);

    for (i=0; i<cert_polic_cnt; i++)
    {
        POLICYINFO *polic = sk_POLICYINFO_value(cert_polic, i);
        if (!polic || !polic->policyid)
            break;

        const uint8_t *p = OBJ_get0_data(polic->policyid);
        if (p == NULL)
            break;

        if ((write_length + 2*OBJ_length(polic->policyid) + 2) > sizeof(info->extension.cert_policies))
            break;

        if (i > 0)
            info->extension.cert_policies[write_length++] = ',';

        for (j=0; j<(int)OBJ_length(polic->policyid); j++)
            write_length += sprintf(info->extension.cert_policies + write_length, "%02x", p[j]);
    }

    CERTIFICATEPOLICIES_free(cert_polic);
}

static void dissect_subject_alt_name(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    dissect_common_ext(x509_ext, extension_arg(info->extension.sub_alt_name));

    find_info_in_subject_dns(info, (uint8_t*)info->extension.sub_alt_name,
                                                    strlen(info->extension.sub_alt_name));
}

static void dissect_issuer_alt_name(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    dissect_common_ext(x509_ext, extension_arg(info->extension.iss_alt_name));
}

static void dissect_crl_dist_points(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    dissect_common_ext(x509_ext, extension_arg(info->extension.crl_dist_points));
}

static void dissect_info_access(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    int         i, ret;
    int         desc_num;
    int         method_usedlen;
    int         method_totallen;
    size_t      len;
    char        *method;

    BIO                     *bio;
    BUF_MEM                 *bptr;
    AUTHORITY_INFO_ACCESS   *ac_info;
    ACCESS_DESCRIPTION      *ac_desc;

    ac_info = (AUTHORITY_INFO_ACCESS*)X509V3_EXT_d2i(x509_ext);
    if (ac_info == NULL)
        return;

    method          = info->extension.auth_info_access;
    method_usedlen  = 0;
    method_totallen = sizeof(info->extension.auth_info_access);
    desc_num        = sk_ACCESS_DESCRIPTION_num(ac_info);
    bio             = BIO_new(BIO_s_mem());

    for (i=0; i<desc_num; i++)
    {
        if (i > 0)
            method[method_usedlen++] = ',';

        bptr = NULL;
        ac_desc = sk_ACCESS_DESCRIPTION_value(ac_info, i);

        method_usedlen += OBJ_obj2txt(method + method_usedlen, method_totallen - method_usedlen, ac_desc->method, 1);
        method[method_usedlen++] = ':';

        GENERAL_NAME_print(bio, ac_desc->location);
        BIO_get_mem_ptr(bio, &bptr);

        len = extension_data_copy(bptr, method + method_usedlen, method_totallen - method_usedlen -1);
        ret = BIO_reset(bio);

        if (len < bptr->length || ret <= 0)
            break;

        method_usedlen += len;
    }

    BIO_free(bio);
    AUTHORITY_INFO_ACCESS_free(ac_info);
}


static void dissect_basic_constraints(struct dpi_x509_st *info, X509_EXTENSION *x509_ext, int nid _U_)
{
    long                pathLen = 0;
    BASIC_CONSTRAINTS   *bcons;

    bcons = (BASIC_CONSTRAINTS*)X509V3_EXT_d2i(x509_ext);

    if (bcons->ca && bcons->pathlen)
    {
        pathLen = ASN1_INTEGER_get(bcons->pathlen);
    }

    info->extension.basic_cons.CA      = bcons->ca;
    info->extension.basic_cons.pathLen = pathLen;

    BASIC_CONSTRAINTS_free(bcons);
}

 
static void dissect_extension_id(struct dpi_x509_st *info,  ASN1_OBJECT *ext_obj)
{
    int             i;
    size_t          ext_len;
    size_t          left_len;
    char            *extIDSet;
    const uint8_t   *p;

    ext_len  = strlen(info->extension.extIDSet);
    left_len = sizeof(info->extension.extIDSet) - ext_len;
    extIDSet = info->extension.extIDSet + ext_len;
    p        = OBJ_get0_data(ext_obj);

    if ((2 * OBJ_length(ext_obj) + 2) >= left_len)
        return;

    if (ext_len > 0)
    {
        *extIDSet++ = ' ';
        left_len--;
    }

    for(i=0; i<(int)OBJ_length(ext_obj); i++)
    {
        snprintf(extIDSet, left_len, "%02x", p[i]);
        extIDSet += 2;
        left_len -= 2;
    }
}


struct X509ExtensionDissector {
    int nid;
    void (*dissector)(struct dpi_x509_st *, X509_EXTENSION *, int);
};

static struct X509ExtensionDissector extension_dissector[] =  {
    {NID_ext_key_usage,             dissect_ext_key_usage       },
    {NID_subject_key_identifier,    dissect_subject_key_id      },
    {NID_authority_key_identifier,  dissect_auth_key_id         },
    {NID_certificate_policies,      dissect_cert_policies       },
    {NID_subject_alt_name,          dissect_subject_alt_name    },
    {NID_issuer_alt_name,           dissect_issuer_alt_name     },
    {NID_crl_distribution_points,   dissect_crl_dist_points     },
    {NID_info_access,               dissect_info_access         },
    {NID_basic_constraints,         dissect_basic_constraints   },
    {-1, NULL}
};


static void dissect_x509_extension(X509 *x509, struct dpi_x509_st *info)
{
    int             i, j;
    int             ext_nid;
    X509_EXTENSION  *x509_ext;
    ASN1_OBJECT     *ext_obj;

    info->extension.key_usage = X509_get_key_usage(x509);
    info->extension.extCount = X509_get_ext_count(x509);

    for (i=0; i < info->extension.extCount; i++)
    {
        x509_ext = X509_get_ext(x509, i);
        if (!x509_ext)
            return;

        ext_obj = X509_EXTENSION_get_object(x509_ext);
        if (!ext_obj)
            return;

        dissect_extension_id(info, ext_obj);

        ext_nid = OBJ_obj2nid(ext_obj);

        for (j=0; extension_dissector[j].dissector; j++)
        {
            if (extension_dissector[j].nid == ext_nid) {
                extension_dissector[j].dissector(info, x509_ext, ext_nid);
                break;
            }
        }
    }
}

static void dissect_x509_pubkey(X509 *x509, struct dpi_x509_st *info, uint8_t count)
{
    ASN1_OBJECT *xpoid = NULL;
    X509_PUBKEY *xpkey = NULL;
    int keyid;
    EVP_PKEY *pkey;
    RSA* rsa;
    struct ec_key_st *eckey;
    if( (xpkey=X509_get_X509_PUBKEY(x509)) && X509_PUBKEY_get0_param(&xpoid, NULL, NULL, NULL, xpkey) )
        info->pubKey_alg = OBJ_nid2ln(OBJ_obj2nid(xpoid));
    if( (pkey = X509_get0_pubkey(x509)) && (NID_undef != (keyid = EVP_PKEY_id(pkey))) ){

        if(count == 1 && info[-1].x509){
            if(g_config.x509_verify_switch)
                info[-1].sign_valid = X509_verify(info[-1].x509, pkey);
            X509_free(info[-1].x509);
            info[-1].x509 = NULL;
        }

        unsigned char *pub;
        unsigned char *obj_pub;
        int len = i2d_PublicKey(pkey, NULL);
        if (len > 0) {
            if( (obj_pub = malloc(len)) ){
                unsigned char *p = obj_pub;
                len = i2d_PublicKey(pkey, &p);
                info->subjectPubKey_val_len = len;
                info->subjectPubKey_val_ptr = (char*)obj_pub;
            }
        }

        switch(keyid)
        {
        case EVP_PKEY_EC:
            eckey = EVP_PKEY_get0_EC_KEY(pkey);
            if(eckey){
                info->pubKey_val_len  = EC_KEY_key2buf(eckey, EC_KEY_get_conv_form(eckey), &pub, NULL); //Openssl malloc
                info->pubKey_val_ptr  = (char*)pub;
            }
            break;
        case NID_rsaEncryption:
            rsa = EVP_PKEY_get0_RSA(pkey);
            if(rsa){
                int buflen = BN_num_bytes(RSA_get0_n(rsa));
                if( (pub = malloc(buflen)) ){
                    BN_bn2bin(RSA_get0_n(rsa), pub);
                    info->pubKey_val_ptr = (char*)pub;
                    info->pubKey_val_len = buflen; 
                }
                info->pubKey_exp_val_ptr = BN_bn2dec(RSA_get0_e(rsa));
                info->pubKey_exp_val_len = info->pubKey_exp_val_ptr ? strlen(info->pubKey_exp_val_ptr) : 0;
            }
            break;
        }
    }
}

static void find_concrete_issuer(struct dpi_x509_st* info, char* longname)
{
    const char* str;
    if( (str= strstr(longname, "/CN=")) )
        info->issuer_name[DPI_X509_NAME_COMMON] = str + 4;
    if( (str= strstr(longname, "/C=")) )
        info->issuer_name[DPI_X509_NAME_COUNTRY]  = str + 3;
    if( (str= strstr(longname, "/O=")) )
        info->issuer_name[DPI_X509_NAME_ORG]  = str + 3;
    if( (str= strstr(longname, "/OU=")) )
        info->issuer_name[DPI_X509_NAME_ORG_UNIT] = str + 4;
    if( (str= strstr(longname, "/L=")) )
        info->issuer_name[DPI_X509_NAME_CITY]  = str + 3;
    if( (str= strstr(longname, "/ST=")) )
        info->issuer_name[DPI_X509_NAME_PROVINCE] = str + 4;
    if( (str= strstr(longname, "/street=")) )
        info->issuer_name[DPI_X509_NAME_STREET] = str + 8;
}

static void find_concrete_subject(struct dpi_x509_st* info, char* longname)
{
    const char* str;
    if( (str= strstr(longname, "/CN=")) ){
        info->subject_name[DPI_X509_NAME_COMMON] = str + 4;
        if(str[4] == '*')
            info->is_wildcard = 1;
    }
    if( (str= strstr(longname, "/C=")) )
        info->subject_name[DPI_X509_NAME_COUNTRY]  = str + 3;
    if( (str= strstr(longname, "/O=")) )
        info->subject_name[DPI_X509_NAME_ORG]  = str + 3;
    if( (str= strstr(longname, "/OU=")) )
        info->subject_name[DPI_X509_NAME_ORG_UNIT] = str + 4;
    if( (str= strstr(longname, "/L=")) )
        info->subject_name[DPI_X509_NAME_CITY]  = str + 3;
    if( (str= strstr(longname, "/ST=")) )
        info->subject_name[DPI_X509_NAME_PROVINCE] = str + 4;
     if( (str= strstr(longname, "/street=")) )
        info->subject_name[DPI_X509_NAME_STREET] = str + 8;
}


/**
 *  版本代码 转换为 x509 版本号（0x2 -> x509V3）
 *  SDX-2022 需求
 */
static int64_t
trans_x509_version(int64_t ver)
{
    static int64_t x509cer_ver_map[] = {1, 2, 3};
    if (ver >= (int64_t)(sizeof(x509cer_ver_map) / sizeof(x509cer_ver_map[0])))
        return 0;

    return x509cer_ver_map[ver];
}

static int dissect_certificate(struct dpi_x509_st* info, const uint8_t *data, uint32_t cert_len, uint8_t count)
{

    int  tmp_len;
    char *tmp_str;
    unsigned i;
    const unsigned char* ptr;

    uint8_t print[32];

/*
    //add by chenzq, add field md5
    uint8_t md5bin[32] = {};
    MD5(data, cert_len, md5bin);
    bintohexlow((const char *)md5bin, MD5_DIGEST_LENGTH, info->md5sum, sizeof(info->md5sum));
*/    

    if(g_config.x509_fingerprint_alg == 0){
        MD5(data, cert_len, print);
        snprintf(info->fingerprint, sizeof(info->fingerprint) - 1, "%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x", 
               print[0], print[1], print[2], print[3], print[4],
               print[5], print[6], print[7], print[8], print[9],
               print[10], print[11], print[12], print[13], print[14],
               print[15]);
    }
    else if(g_config.x509_fingerprint_alg == 1){
        SHA1(data, cert_len, print);
        snprintf(info->fingerprint, sizeof(info->fingerprint) - 1, "%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x", 
               print[0], print[1], print[2], print[3], print[4],
               print[5], print[6], print[7], print[8], print[9],
               print[10], print[11], print[12], print[13], print[14],
               print[15], print[16], print[17], print[18], print[19]);

    }
     


    X509* x509 = d2i_X509(NULL,  &data, cert_len);
    if(x509 == NULL)
        return -1;//bad format

    info->polluted = 1; 
    info->length  = cert_len;
    info->version = trans_x509_version(X509_get_version(x509));

    ASN1_INTEGER* serial = X509_get_serialNumber(x509);
    if(serial){
        tmp_len = ASN1_STRING_length(serial);
        if( (info->serialNum_val_ptr = malloc(tmp_len)) && (ptr = ASN1_STRING_get0_data(serial)) ){//手动 malloc
            memcpy(info->serialNum_val_ptr, ptr, tmp_len);
            info->serialNum_val_len  = tmp_len;
        }
    }

    const X509_ALGOR *tsig_alg = NULL;
    int sig_nid;
    if( ((tsig_alg = X509_get0_tbs_sigalg(x509))) && (NID_undef != (sig_nid = OBJ_obj2nid(tsig_alg->algorithm))) )//read only
        info->signature_alg = OBJ_nid2sn(sig_nid);

    const X509_NAME *X509_issuer_name = X509_get_issuer_name(x509);
    if(X509_issuer_name){
        info->issuer_num = X509_NAME_entry_count(X509_issuer_name);
        if( (tmp_str = X509_NAME_oneline(X509_issuer_name, NULL, 0)) ){ //Openssl malloc
            info->issuer_val_ptr = tmp_str;
            info->issuer_val_len = strlen(tmp_str);
            find_concrete_issuer(info, tmp_str);
            for(i=0; i < info->issuer_val_len; i++){
                if(tmp_str[i] == '/')
                    tmp_str[i] = '\0';
            }
        }
    }

    const ASN1_TIME *ASN1_time;
    if( (ASN1_time = X509_get0_notBefore(x509)) )
        ASN1_TIME_to_tm(ASN1_time,  &info->begintime);
    if( (ASN1_time = X509_get0_notAfter(x509)) )
        ASN1_TIME_to_tm(ASN1_time,  &info->endtime);

    const X509_NAME *X509_subject_name = X509_get_subject_name(x509);
    if(X509_subject_name){
        info->subject_num = X509_NAME_entry_count(X509_subject_name);
        if( (tmp_str = X509_NAME_oneline(X509_subject_name, NULL, 0)) ){ //Openssl malloc
            info->subject_val_ptr  = tmp_str;
            info->subject_val_len  = strlen(tmp_str);
            find_concrete_subject(info, tmp_str);
            for(i=0; i < info->subject_val_len; i++){
                if(tmp_str[i] == '/')
                    tmp_str[i] = '\0';
            }
        }
    }

    //单位名相同则为自签名证书
    if(info->subject_name[DPI_X509_NAME_ORG] && info->issuer_name[DPI_X509_NAME_ORG]
        && strcmp(info->subject_name[DPI_X509_NAME_ORG], info->issuer_name[DPI_X509_NAME_ORG]) == 0)
        info->is_self_signed = 1;

    dissect_x509_pubkey(x509, info, count);
    dissect_x509_extension(x509, info);

    if(count)
        X509_free(x509);
    else  //第一个不free,等待认证
        info->x509 = x509;

    return 0;
}

#define MIN_CERT_LEN 500
#define MAX_CERT_LEN 8192

struct dpi_x509_chain_st *dpi_creat_new_x509_chain_node(const char *desc_name) {
    struct dpi_x509_chain_st *p = malloc(sizeof(struct dpi_x509_chain_st));
    memset(p, 0, sizeof(struct dpi_x509_chain_st));
    int copy_len = strlen(desc_name)>sizeof(p->desc)?sizeof(p->desc)-1:strlen(desc_name);
    strncpy(p->desc, desc_name,copy_len);
    return p;
}

struct dpi_x509_chain_st *dpi_creat_new_x509_chain(const char *subj_name,const  char *issu_name) {
    struct dpi_x509_chain_st *p = dpi_creat_new_x509_chain_node(issu_name);
    if(strcmp(subj_name, issu_name)!= 0){
      p->subject = dpi_creat_new_x509_chain_node(subj_name);
      p->subject->iss = p;
    }
    return p;
}

//chain操作的状态 可以不用但是不能没有
enum{
  EM_X509_CAHIN_DO_NOTHIN,
  EM_X509_CAHIN_INSERT_NODE,
  EM_X509_CAHIN_INSERT_CHAIN,
};
/**
 * @brief 
 * 
 * @param sslinfo 
 * @param chain 
 * @param subj_name 
 * @param issu_name 
 * @return int  0 = do nothing
 * @return int  1 = do insert node
 * @return int  2 = do insert chain
 */
int dpi_search_x509_chain(struct SSL_info_t *sslinfo,  const char *subj_name,const  char *issu_name) {
  if(subj_name== NULL|| issu_name == NULL)
  {
    return 0;
  }
  struct dpi_x509_chain_st *search_chain = sslinfo->cert_chain[0];
  for (int i = 0; i < sslinfo->cert_chain_num; i++) {
        search_chain = sslinfo->cert_chain[i];
        //查找每条chain
        struct dpi_x509_chain_st *search_node = search_chain;
        int                       search_forward = 0;
        while (NULL != search_node) {
            if (0 == strcmp(search_node->desc, issu_name)) {
                //找到iss了
                //如果subj为空
                if (search_node->subject == NULL) {
                    search_node->subject = dpi_creat_new_x509_chain_node(subj_name);
                    search_node->subject->iss = search_node;
                    return EM_X509_CAHIN_INSERT_NODE;
                } else if (0 == strcmp(search_node->subject->desc, subj_name)) {
                    return EM_X509_CAHIN_DO_NOTHIN;
                } else {
                    //如果他的颁发者与前面不一样呢？
                    // 选择建立新链
                    sslinfo->cert_chain[sslinfo->cert_chain_num] = dpi_creat_new_x509_chain(subj_name, issu_name);
                    sslinfo->cert_chain_num++;
                    return EM_X509_CAHIN_INSERT_CHAIN;
                }
            }
            if (search_forward == 0) {
                //正向搜索
                search_node = search_node->iss;
                if (NULL == search_node) {
                    search_forward = 1;
                    search_node = search_chain->subject;
                }
            } else {
                //反向搜索
                search_node = search_node->subject;
            }
        }
  }
  //现有的chain中都没找到
  sslinfo->cert_chain[sslinfo->cert_chain_num] = dpi_creat_new_x509_chain(subj_name, issu_name);
  sslinfo->cert_chain_num++;
  return EM_X509_CAHIN_INSERT_CHAIN;
}

int dissect_x509_chain(struct flow_info *flow,struct dpi_x509_st* info){
    SSL_Session     *psslSession = (SSL_Session*)flow->app_session;
    ST_SSLInfo      *psslInfo = &psslSession->ssl_info;

    dpi_search_x509_chain(psslInfo,info->subject_name[DPI_X509_NAME_ORG],info->issuer_name[DPI_X509_NAME_ORG]);

    return 0;
}

void clean_x509_chain(struct flow_info *flow){
    SSL_Session     *psslSession = (SSL_Session*)flow->app_session;
    ST_SSLInfo      *psslInfo = &psslSession->ssl_info;
    for(int i =0;i < psslInfo->cert_chain_num;i++){

    }
}
int dissect_x509(struct flow_info *flow, int direction, const uint8_t* payload, uint32_t payload_len, struct SSL_info_t* pst_sslinfo)
{
    if(g_config.protocol_switch[PROTOCOL_X509] == 0)
        return 0;

    uint32_t i, offset, count = 0;
    uint32_t cert_len, server_cert_len = 0; 
    const uint8_t *server_cert_head = NULL;
    const uint8_t *data = payload;

    struct SslCertInfo  *pcert_info;

    pcert_info = &pst_sslinfo->cert_infos[direction];

    if (pcert_info->CertificatesDissectedFlag)
        return 0;

    pcert_info->CertificatesLength = GET3BYTES(payload+1) - 3;

    if(pcert_info->CertificatesLength != GET3BYTES(payload+4))
        return -1;//bad format
    if(pcert_info->CertificatesLength < MIN_CERT_LEN || pcert_info->CertificatesLength > MAX_CERT_LEN)
        return -1;//too short or too long
    struct dpi_x509_st *info = pcert_info->cert;
    data += 7;// 1:type 3:length 3:cer length
    offset = 7;
    while(offset < payload_len && count < CERT_MAX_NUM){
        cert_len = GET3BYTES(data);
        offset  += cert_len + 3;
        if(offset > payload_len || cert_len < MIN_CERT_LEN)
            break;

        if(dissect_certificate(info + count, data+3, cert_len, count) < 0)
            break;
        if(count == 0){//服务器证书
            server_cert_len  = cert_len;
            server_cert_head = data + 3;
        }
        dissect_x509_chain(flow,info + count);
        data = payload + offset;
        count += 1;
    }

    if(count && g_config.x509_write_switch){
        int write_flag = 1;
        long ret;
        if(g_config.x509_whitelist_switch){
            for(i=0; i < count; i++){
                ret = (long)g_hash_table_lookup(x509_filter_table, info[i].fingerprint);
                if(ret == 1)
                    break;
            }
            if(i == count)
                write_flag = 0;
        }

        if(write_flag && write_x509(flow, info, server_cert_head, server_cert_len) == 0)
            info->inDatabase = 1;
    }

    if(pcert_info->cert[0].x509){
        X509_free(pcert_info->cert[0].x509);
        pcert_info->cert[0].x509 = NULL;
    }

    pcert_info->CertificatesDissectedFlag = count;

    pcert_info->CertificatesNums = count;
    return 0;
}

void free_x509(struct dpi_x509_st *cert, uint32_t count)
{
    uint32_t i, num = DPI_MIN(count, CERT_MAX_NUM);
    for(i=0; i<num; i++){
        free(cert[i].serialNum_val_ptr);
        free(cert[i].subject_val_ptr);
        free(cert[i].issuer_val_ptr);
        free(cert[i].pubKey_val_ptr);
        free(cert[i].pubKey_exp_val_ptr);
    }
    memset(cert, 0, num * sizeof(struct dpi_x509_st));
}

static void init_x509_dissector(void){
    dpi_register_proto_schema(x509_field_array,EM_509_MAX, X509_PROTO_NAME);
    map_fields_info_register(x509_field_array,PROTOCOL_X509, EM_509_MAX, X509_PROTO_NAME);
}

static __attribute__((constructor)) void before_init_x509(void){
    register_tbl_array(TBL_LOG_X509, 0, X509_PROTO_NAME, init_x509_dissector);
}
