#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <glib.h>

#include "tcp_rsm.h"
#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_tcp_reassemble.h"

#include "libsdtacl/yasdtacl.h"
#include "sdt_ip_protocols.h"
#include "dpi_sdt_match.h"
#include "sdt_action_out.h"
#include "dpi_sdt_link.h"
#include "dpi_sdt_ipp.h"
#include "jhash.h"
#include "dpi_memory.h"
#include "dpi_pschema.h"
#include "dpi_flow.h"

int cloneIndex = 0, freeIndex = 0;
extern struct rte_ring *sdt_out_ring[SDT_MAX_OUT_RING];
extern struct rte_hash *g_sdt_hash_db;
extern struct rte_mempool *tbl_log_mempool;

extern rte_atomic64_t sdt_pcap_out_pkts;
extern rte_atomic64_t sdt_pcap_success_pkts;
extern rte_atomic64_t sdt_event_success_pkts;
extern rte_atomic64_t sdt_pcap_fail_pkts;
extern rte_atomic64_t sdt_event_fail_pkts;

struct rte_ring *app_match_ring[APP_PROTOCOL_RING_MAX_NUM];
int sdt_match_thfunc_signal = 0;

/**
 *  解析线程中也会进行匹配, 为避免产生竞争,
 *  MATCH_ENGINE_MAX_NUM = 解析线程数量 + 匹配线程数量
 */
SdxMatchProcessStatus  sdx_match_process_status[MATCH_ENGINE_MAX_NUM];

#if 0
struct rte_hash *tcp_hash_tab;

static dpi_field_table  tcp_field_array[] = {
    DPI_FIELD_D(ENUM_TCP_PAYLOAD,                  YV_FT_BYTES,       "payload"),
    DPI_FIELD_D(ENUM_TCP_PAYLOAD_LEN,              YV_FT_INT16,       "payload_len"),
    DPI_FIELD_D(ENUM_TCP_FLAGS,                    YV_FT_INT16,       "flags"),
    DPI_FIELD_D(ENUM_TCP_WINDOWSIZE,               YV_FT_INT16,       "windowsize"),
    DPI_FIELD_D(ENUM_TCP_STREAM0,                  YV_FT_BYTES,       "stream0"),
    DPI_FIELD_D(ENUM_TCP_STREAM1,                  YV_FT_BYTES,       "stream1"),
};
#endif

/** 更新命中统计信息 */
void sdx_match_status_add(int engine_id, int proto_id, int value)
{
    g_assert(engine_id < MATCH_ENGINE_MAX_NUM);
    g_assert(proto_id < PROTOCOL_MAX);

    sdx_match_process_status[engine_id].packet_on_rule_hit += value;
    sdx_match_process_status[engine_id].proto_hit_cnt[proto_id] += value;
}

/** 清除命中统计信息 */
void sdx_match_status_clean()
{
    int i;

    for (i=0; i<MATCH_ENGINE_MAX_NUM; i++)
    {
        memset(&sdx_match_process_status[i], 0, sizeof(SdxMatchProcessStatus));
    }
}


static inline void _sdt_free_header(gpointer data)
{

}

static inline void _sdt_free_key_value(gpointer data)
{
    dpi_free(data);
}


static int
_sdt_check_rule_exist(struct flow_info *flow, uint32_t id)
{
    uint32_t i;
    for(i=0;i<flow->sdt_flow.sdt_rule_cnt;i++){
        if(flow->sdt_flow.sdt_rules_rd[i]==id){
            return i;
        }
    }
    return -1;
}

static inline uint32_t gen_uid_gid_hash_code(const char *taskID, const char *groupID)
{
    struct user_group_key
    {
        char  taskID[64];
        char  groupID[64];
    } u_g_key;

    strncpy(u_g_key.taskID, taskID, sizeof(u_g_key.taskID));
    strncpy(u_g_key.groupID,groupID,sizeof(u_g_key.groupID));
    return jhash(&u_g_key, sizeof(u_g_key), JHASH_INITVAL);
}


static inline uint32_t gen_rule_hash_code(SdtMatchResult *match_result)
{
    struct user_group_key
    {
        char  uintID[64];
        char  taskID[64];
        char  groupID[64];
        uint32_t ruleID;
    } u_g_key;

    strncpy(u_g_key.uintID, match_result->unitID, sizeof(u_g_key.uintID));
    strncpy(u_g_key.taskID, match_result->taskID, sizeof(u_g_key.taskID));
    strncpy(u_g_key.groupID,match_result->groupID,sizeof(u_g_key.groupID));
    u_g_key.ruleID = match_result->ruleId;

    return jhash(&u_g_key, sizeof(u_g_key), JHASH_INITVAL);
}


//int sdt_in_pcap(struct flow_info *flow, SdtMatchResult  *sdt_act, sdt_out_status  *flow_rule_status, const struct pkt_info  *pkt)

static int
_sdt_malloc_packet_data_out(struct flow_info *flow, struct packet_stream *pos, SdtMatchResult  *sdt_act, sdt_out_status  *flow_rule_status, bool first_packet)
{
    if(NULL==pos || NULL==sdt_act){
        return 0;
    }

    uint32_t free_space = 0;
    int      ring_id    = 0;
    int      ret        = 0;

    struct packet_stream *item=malloc(sizeof(struct packet_stream));
    memset(item, 0, sizeof(struct packet_stream));

    //传递相关参数
    strncpy(item->unitID, sdt_act->unitID, sizeof(item->unitID));
    strncpy(item->taskID, sdt_act->taskID, sizeof(item->taskID));
    strncpy(item->groupID,sdt_act->groupID,sizeof(item->groupID));
    strncpy(item->method, sdt_act->method, sizeof(item->method));
    strncpy(item->topicName,sdt_act->topicName,sizeof(item->topicName));

    //牡丹江现场 -- XML文件新增3类属性
    item->mode_param_num = sdt_act->mode_param_num;
    item->task_mode      = sdt_act->task_mode;
    item->task_sub_type  = sdt_act->task_sub_type;
    item->rule_mode      = sdt_act->rule_mode;
    memcpy(item->mode_param, sdt_act->mode_param, sizeof(item->mode_param));

    item->action_type    = SAE_packetDump;
    item->rule_id        = sdt_act->ruleId;
    item->direction      = flow->direction;
    item->data_type      = SDT_OUT_ACTUAL_DATA_DUMP_PCAP;
    item->thread_id      = flow->thread_id;
    item->flow_id        = flow->flow_id;
    item->timestamp      = flow->timestamp;
    item->flow_cycle     = flow->flow_cycle;
    item->first_packet   = first_packet;

    int len = pos->pkt_data_len;
    if(pos->pkt_data_len>TCP_PAYLOAD_MAX_LEN)
    {
        //printf("ERROR: pkt_data_len too long\n");
        len = TCP_PAYLOAD_MAX_LEN;
    }

    item->pkt_data_len   = pos->pkt_data_len;
    memcpy(item->pkt_data, pos->pkt_data, len);
    ring_id=sdt_act->ruleId%g_config.sdt_out_thead_num;
    ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&item, 1, &free_space);
    if(ret>0){
        rte_atomic64_inc(&sdt_pcap_success_pkts);
    }else{
        free(item);
        rte_atomic64_inc(&sdt_pcap_fail_pkts);
    }
    return 1;
}


static int
_sdt_flush_cache_packets(struct flow_info *flow, struct list_head * stream_list,
                         uint8_t node_flag,
                         SdtMatchResult  *sdt_act,
                         sdt_out_status  *flow_rule_status,
                         uint32_t start_index)
{
    uint32_t free_space;
    int      ring_id;
    int      ret=0;
    struct packet_stream *pos=NULL;
    struct packet_stream *n;

    uint32_t count=0;
    bool first_packet = true;

    if (EM_LIST_NODE==node_flag)
    {
        list_for_each_entry_safe(pos,n, stream_list, node) {
            if(pos==NULL){
                continue;
            }
            count++;
            if(count<start_index){
                continue;
            }

            _sdt_malloc_packet_data_out(flow, pos, sdt_act, flow_rule_status, first_packet);
            first_packet = false;
        }
    } else if(EM_LIST_SNODE==node_flag){
        list_for_each_entry_safe(pos,n, stream_list, snode) {
            if(pos==NULL){
                continue;
            }

            count++;
            if(count<=start_index){
                continue;
            }

            _sdt_malloc_packet_data_out(flow, pos, sdt_act, flow_rule_status, first_packet);
            first_packet = false;
        }
    }

    if(flow->sdt_flow.sdt_rule_cnt==g_config.sdt_rule_max_num){
        pkt_stream_free_node(&flow->sdt_flow.pkt_stream_head[0]);
        pkt_stream_free_node(&flow->sdt_flow.pkt_stream_head[1]);
    }

    return 1;
}

static int
_sdt_handle_rule_statistics(struct flow_info            *flow,
                                        SdtMatchResult  *sdt_act,
                                        sdt_out_status  *flow_rule_status,
                                        uint16_t        proto_id,
                                        uint8_t         flag)
{
    if(SAE_none==sdt_act->action){
        return 0;
    }

    sdt_rule_perthread_pkt_statistics(flow,
                                      sdt_act,
                                      flow_rule_status,
                                      flow->thread_id,
                                      flag);

    return 1;
}


static int
_sdt_handle_event_match(struct flow_info *flow,
                                  SdtMatchResult  *sdt_act,
                                  sdt_out_status  *flow_rule_status,
                                  int direction,
                                  uint16_t proto_id,
                                  uint8_t *sdt_flag)
{
    if(SMH_hint_rule_has_no_body ==sdt_act->matchHintFlag && PROTOCOL_IP!=proto_id){
        return 0;
    }

    *sdt_flag = 1;
    sdt_event_handle(flow,sdt_act, flow_rule_status, direction);

    return 1;
}


/*处理缓存报文  action 为SAE_packetDump，SAE_event_and_packetDum 调用处理*/
static int
_sdt_handle_cache_packets(struct flow_info *flow,
                                 const struct pkt_info  *pkt,
                                 SdtMatchResult  *sdt_act,
                                 sdt_out_status  *flow_rule_status,
                                 int direction,
                                 uint16_t proto_id,
                                 uint8_t *sdt_flag)
{
    *sdt_flag=1;

    flow->direction                = direction;
    flow->sdt_flow.match_direction = direction;
    uint32_t           start_index = 0;


    //ipff 规则类型 是不可以基于会话级处理的.
    if(sdt_act->is_ipff)
    {
        return 0;
    }

    switch(sdt_act->dumpOpt)
    {
    case SPO_out_pkt:
        if(SMH_hint_rule_has_body==sdt_act->matchHintFlag){
            sdt_in_pcap(flow, sdt_act, flow_rule_status, pkt, 1);
        }else if(SMH_hint_rule_has_no_body==sdt_act->matchHintFlag && PROTOCOL_IP==proto_id){
            sdt_in_pcap(flow, sdt_act, flow_rule_status, pkt, 1);
        }
        *sdt_flag=0;  /* out_pkt单包模式，下次继续从这里刷出报文*/
        sdt_act->action= SAE_none;
        break;

    /*SPO_out_pkt_N 和 SPO_out_this 共用代码*/
    case SPO_out_pkt_N:
        if(flow->sdt_flow.single_stream_num[direction] >
           (uint32_t)sdt_act->packetDump_args.count)
        {
            start_index=flow->sdt_flow.single_stream_num[direction]-
                        sdt_act->packetDump_args.count;
        }
    case SPO_out_this:
        if(SPO_out_this==sdt_act->dumpOpt){
            start_index=0;
        }
        _sdt_flush_cache_packets(flow, &flow->sdt_flow.single_stream_head[direction],
                                 EM_LIST_SNODE, /* snode*/
                                 sdt_act,
                                 flow_rule_status,
                                 start_index);
        if(flow->sdt_flow.pkt_stream_num[0]>g_config.sdt_cache_max_pkts){
            sdt_in_pcap(flow, sdt_act, flow_rule_status, pkt, 0);
        }
        break;

     /*SPO_out_front 和 SPO_default 共用代码*/
    case SPO_out_front:
        if(flow->sdt_flow.pkt_stream_num[0] >
           (uint32_t)sdt_act->packetDump_args.count)
        {
            start_index=flow->sdt_flow.pkt_stream_num[0] -
                        sdt_act->packetDump_args.count;
        }
    case SPO_default:
        if(SPO_default==sdt_act->dumpOpt){
            start_index=0;
        }
        _sdt_flush_cache_packets(flow, &flow->sdt_flow.pkt_stream_head[0],
                                 EM_LIST_NODE, /* node*/
                                 sdt_act,
                                 flow_rule_status,
                                 start_index);
        if(flow->sdt_flow.pkt_stream_num[0]>g_config.sdt_cache_max_pkts){
            sdt_in_pcap(flow, sdt_act, flow_rule_status, pkt, 0);
        }
        break;
    default:
        break;
    }

    return 1;
}

int sdt_stream_rules_action_handle_hit
(int action, const struct pkt_info *pkt, struct flow_info *flow, int direction, SdtMatchResult *result, sdt_out_status *status)
{
    int ret = PKT_DROP;

    switch(action){
        case SAE_drop:
            return PKT_DROP;
        case SAE_packetDump:
            if (result->dumpOpt == SPO_default ) {
                sdt_in_pcap(flow, result, status, pkt, 0);
            } else
            if (result->dumpOpt == SPO_out_this) {
                if (flow->sdt_flow.match_direction == direction){
                    sdt_in_pcap(flow, result, status, pkt, 0);
                }
            }else if (result->dumpOpt == SPO_out_pkt) {
            }
            ret = PKT_OK;
            break;
        case SAE_event:
            /*sdt_rule_perthread_flow_statistics(result,
              status,
              flow->thread_id,
              pkt->pkt_len);
              */
            ret = PKT_OK;
            break;
        case SAE_report:
            /*sdt_rule_perthread_flow_statistics(result,
              status,
              flow->thread_id,
              pkt->pkt_len);
              */
            ret=PKT_OK;
            break;
        default:
            break;
    }
    return ret;
}

int _sdt_match_rules_match_handle_hit
(int action, const struct pkt_info *pkt, struct flow_info *flow, int direction, SdtMatchResult *result, sdt_out_status *status)
{
    uint8_t sdt_result_flag = 0;
    switch(action ){
    case SAE_none:
        break;
    case SAE_drop:
        /*删掉缓存节点包*/
        return PKT_DROP;
    case SAE_event:
        /*event 输出*/
        _sdt_handle_event_match(flow,
                                result,
                                status,
                                flow->direction,
                                flow->real_protocol_id,
                                &sdt_result_flag);
        break;
    case SAE_report:
    {
        struct packet_stream *pos=NULL;
        struct packet_stream *n;
        list_for_each_entry_safe(pos, n, &flow->sdt_flow.pkt_stream_head[0], node)
        {
            if (list_empty(&flow->sdt_flow.pkt_stream_head[0]))
                break;
            if(flow->sdt_flow.sdt_rule_cnt==g_config.sdt_match_max_rules-1)
            {
                if(!list_empty(&pos->snode)){
                    list_del(&pos->snode);
                    INIT_LIST_HEAD(&pos->snode);
                }
                if(!list_empty(&pos->tnode)){
                    list_del(&pos->tnode);
                    INIT_LIST_HEAD(&pos->tnode);
                }
                if(!list_empty(&pos->node)){
                    list_del(&pos->node);
                    INIT_LIST_HEAD(&pos->node);
                }
                free(pos);
            }
        }
    }
        break;
    case SAE_packetDump:
        _sdt_handle_cache_packets(flow, pkt,
                                  result,
                                  status,
                                  flow->direction,
                                  flow->real_protocol_id,
                                  &sdt_result_flag);
        break;
    default:
        break;
    }

    if(result->action & SAE_alert)
    {
        //printf("##################### find alert -----------------------------------------0x%x-----\n", action);
        sdt_in_syslog(flow, result);
    }
    return 0;
}

static int
sdt_stream_rules_action_handle(struct flow_info        *flow,
                                      const struct pkt_info  *pkt,
                                      sdt_out_status   *rule_hash_status)
{
    if(!flow || !pkt || !rule_hash_status){
        return PKT_OK;
    }

    int ret=PKT_DROP;
    int  direction = flow->direction;
    SdtMatchResult   *match_result=rule_hash_status->match_result;

    //////////////////////////////////////////////////////////////////////////
    //////////////// 规则命中之后, FOW后续报文没有经过引擎 ///////////////////
    //////////////// 这里没有经过规则引擎 -- 需要单独照顾 ////////////////////
    //////////////////////////////////////////////////////////////////////////
    //与TCP/IP匹配冲突,匹配次数重复计入
    //与TCP/IP匹配冲突,匹配次数重复计入
    //与TCP/IP匹配冲突,匹配次数重复计入
    //handle emax/elimit/efreq stream 模式
    //if(ACTION_DENIED == sdt_action_options_approve(match_result))
    //{
    //    return PKT_OK;
    //}
    //ATOMIC_ADD_FETCH(&(match_result->hitOnCnt));
    //ATOMIC_ADD_NUM(&(match_result->hitOnByte), pkt->pkt_len);
    //与TCP/IP匹配冲突,匹配次数重复计入
    //与TCP/IP匹配冲突,匹配次数重复计入


    //单包模式的规则, 会话的后续报文,不应该继续参与.
    if(match_result->is_ipff)
    {
        return PKT_OK;
    }

    for(int chunli = 0; chunli < 8 *(int)sizeof(match_result->action); chunli++)
    {
        uint32_t action = 1 << chunli;
        if(match_result->action & action)
        {
            ret = sdt_stream_rules_action_handle_hit(action, pkt, flow, direction, match_result, rule_hash_status);
            if(PKT_DROP == ret)
            {
                return PKT_DROP;
            }
        }
    }

    return ret;
}


/**
 * 对于已经命中规则后续的报文处理
 *
 * @flow
 *   流表
 * @pkt
 *   单包报文结构
 * @direction
 *   方向
*/
static int
_sdt_stream_action_process(struct flow_info *flow,const struct pkt_info  *pkt, int direction)
{
    if(pkt->ethhdr==NULL || pkt->pkt_len<=0){
        DPI_LOG(DPI_LOG_ERROR, "sdt_action_process pkt_len:%d",pkt->pkt_len);
        return 0;
    }
    int ret = PKT_DROP;
    uint32_t i   = 0;

    if(flow->sdt_flow.sdt_rule_cnt==0 ||
       flow->sdt_flow.sdt_rule_cnt<g_config.sdt_rule_max_num){
        ret=PKT_OK;
    }

    int local_ret=PKT_DROP;
    for(i=0;i<flow->sdt_flow.sdt_rule_cnt;i++){

        if(flow->sdt_flow.sdt_rules_rd[i]==0){
            break;
        }

        if(!flow->sdt_flow.sdt_rules_status[i]){
            break;
            //flow->sdt_flow.sdt_rules_status[i]=
            //          sdt_rule_hash_db_lookup(&flow->sdt_flow.sdt_rules_status[i]->match_result);
        }

        local_ret=sdt_stream_rules_action_handle(flow, pkt, flow->sdt_flow.sdt_rules_status[i]);
        if(PKT_OK==local_ret){/* 已经命中规则中有out_pkt和report则仍需继续往下走*/
            ret=PKT_OK;
        }
    }

    return ret;
}

static void
sdt_destroy_prec(ProtoRecord *pRec)
{
    if (pRec->record) {
        precord_destroy(pRec->record);
        pRec->record = NULL;
    }
}

static int
sdt_init_prec(struct flow_info *flow,const struct pkt_info  *pkt, ProtoRecord *pRec, int dir)
{
    char __str[64] = {0};

    pRec->raw_data      =   pkt->raw_pkt;
    pRec->raw_data_len  =   pkt->pkt_len;

    pRec->proto_id=PROTOCOL_IP;
    if (pkt->proto == IPPROTO_TCP){
        pRec->ip_proto=IPPROTO_TCP;
        pRec->proto_id=PROTOCOL_TCP;
    } else if (pkt->proto == IPPROTO_UDP){
        pRec->ip_proto=IPPROTO_UDP;
        pRec->proto_id=PROTOCOL_UDP;
    }
    u8  *l2=(u8 *)pkt->ethhdr;
    u8  *l3=NULL;
    pRec->tuple.ipversion = pkt->ipversion;
    if(pkt->proto == IPPROTO_TCP){
        pRec->tuple.PortDst = pkt->tcph->dest;
        pRec->tuple.PortSrc = pkt->tcph->source;

    } else if(pkt->proto == IPPROTO_UDP){
        pRec->tuple.PortDst = pkt->udph->dest;
        pRec->tuple.PortSrc = pkt->udph->source;
    }else{
        pRec->tuple.PortDst = 0;
        pRec->tuple.PortSrc = 0;
    }
    if(pkt->ipversion == 4){
        l3=(u8 *)pkt->iph;
        pRec->ip_len=ntohs(pkt->iph->tot_len);

        pRec->tuple.IpDst.ipv4   = *(int*)pkt->iph->daddr;
        pRec->tuple.IpSrc.ipv4   = *(int*)pkt->iph->saddr;
    }else if(pkt->ipversion==6){
        l3=(u8 *)pkt->iph6;
        pRec->ip_len=ntohs((pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen));
        memcpy(pRec->tuple.IpDst.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pRec->tuple.IpDst.ipv6));
        memcpy(pRec->tuple.IpSrc.ipv6, (const char *)pkt->iph6->ip6_src, sizeof(pRec->tuple.IpSrc.ipv6));
    }else{
        return PKT_DROP;
    }
    pRec->ip_version=pkt->ipversion;
    pRec->l3h_start=l3-l2;

    u8  *l4=NULL;
    if(pRec->ip_proto==IPPROTO_TCP){
        l4=(u8 *)pkt->tcph;
        pRec->tcp_flags=pkt->tcph->doff<<12|
                        pkt->tcph->res1<<8 |
                        pkt->tcph->cwr<<7  |
                        pkt->tcph->ece<<6  |
                        pkt->tcph->urg<<5  |
                        pkt->tcph->ack<<4  |
                        pkt->tcph->psh<<3  |
                        pkt->tcph->rst<<2  |
                        pkt->tcph->syn<<1  |
                        pkt->tcph->fin;
        pRec->tcp_flags&=0x0FFF;
        pRec->tcp_windowsize=ntohs(pkt->tcph->window);
        pRec->l4payload_start=(uint16_t)(l4-(uint8_t *)pkt->ethhdr+pkt->tcph->doff*4);
        pRec->seq=ntohl(pkt->tcph->seq);
        pRec->l4h_start=l4-l2;
    }else if(pRec->ip_proto==IPPROTO_UDP){
        l4=(u8 *)pkt->udph;
        pRec->l4payload_start=(uint16_t)(l4-(uint8_t *)pkt->ethhdr+sizeof(struct dpi_udphdr));
        pRec->l4h_start=l4-l2;
    }else{

    }

    pRec->pPayload.pBuff=(uint8_t *)pkt->raw_pkt;
    pRec->pPayload.len=pkt->pkt_len;
    pRec->direction = dir;
    pRec->record = NULL;
    return PKT_OK;
}

static int
_sdt_stream_data_move_block(struct stream_cache_st   *stream_cache, const uint8_t *payload, uint16_t  payload_len)
{
    if(NULL==payload || payload_len<=0){
        return -1;
    }

    if(NULL==stream_cache->buff){
        stream_cache->buff=(uint8_t *)dpi_malloc(g_config.sdt_block_buff_size*sizeof(uint8_t));
        if(NULL==stream_cache->buff){
            return -1;
        }
        stream_cache->buff_left = g_config.sdt_block_buff_size;
        stream_cache->buff_use  = 0;
    }

    if(stream_cache->buff_left<payload_len){
        if(stream_cache->buff_use>g_config.sdt_block_buff_keep){
            memmove(&stream_cache->buff[0],
                &stream_cache->buff[stream_cache->buff_use-g_config.sdt_block_buff_keep],
                g_config.sdt_block_buff_keep);
            stream_cache->buff_use=g_config.sdt_block_buff_keep;
            stream_cache->buff_left=g_config.sdt_block_buff_size-stream_cache->buff_use;
        }

        if(stream_cache->buff_left<payload_len){
            memmove(&stream_cache->buff[stream_cache->buff_use],
                    payload,
                    stream_cache->buff_left);
            stream_cache->buff_use  += stream_cache->buff_left;
            stream_cache->buff_left  = 0;
            return 0;
        }
    }

    memmove(&stream_cache->buff[stream_cache->buff_use],
            payload,
            payload_len);
    stream_cache->buff_use  += payload_len;
    stream_cache->buff_left -= payload_len;

    return 0;
}

// 已缓存的数据直接输出
static int
_sdt_tcp_miss(void *user, int C2S, uint32_t miss)
{
    struct flow_info    *flow     = (struct flow_info*)user;
    struct stream_cache_st   *tcp_cache=&flow->sdt_flow.sdt_l4stream_cache[C2S];
    uint8_t  buff[1520];
    memset(buff, 0x0c, 1520);
    if(miss>=1520){
        miss=1520;
    }

    _sdt_stream_data_move_block(tcp_cache,buff,miss);

    return 0;
}

static int
_sdt_tcp_strip(void *user, int C2S, const uint8_t *payload, uint16_t  payload_len)
{
    /* 到此的数据包时有序的 */
    struct flow_info    *flow     = (struct flow_info*)user;

    struct stream_cache_st   *tcp_cache=&flow->sdt_flow.sdt_l4stream_cache[C2S];

    _sdt_stream_data_move_block(tcp_cache,payload,payload_len);

    /* 最后超时在在做最后匹配 */
    //_sdt_tcp_stream_match(flow, C2S);


    return 0;
}

static int
_sdt_tcp_flow_finish(struct flow_info *flow)
{


    struct tcp_rsm *rsm = flow->sdt_flow.sdt_rsm;
    if(rsm){
        const struct tcp_status *status;
        status = tcp_rsm_status(rsm, 0);
        uint32_t num = status->fail;
        status = tcp_rsm_status(rsm, 1);
        num += status->fail;
        if(num){
          //  rte_atomic64_add(&rsm_fail_get, num);
        }

        tcp_rsm_free(rsm);
        flow->sdt_flow.sdt_rsm = NULL;
    }

    return 0;
}


static int
_sdt_tcp_rsm(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    if(DISSECT_PKT_ORIGINAL != flag)
    {
        return 0;
    }

    if(flow->pkt == NULL || flow->pkt->tcph == NULL){
        DPI_LOG(DPI_LOG_ERROR, "flow pkt tcph = NULL");
        return 0;
    }


    //光海没有完整实现 流式报文匹配
    //删除了 不起作用的 流式重组代码

    //对 TCP 重组, 流式报文匹配
    return 0;
}



static void
_sdt_match_rules_match_handle(struct flow_info       *flow,
                                      const struct pkt_info  *pkt,
                                      ProtoRecord      *pRec,
                                      sdt_out_status   *rule_hash_status,
                                      SdtMatchResult   *match_result)
{
    if(!flow || !pRec){
        return;
    }

    int ret = PKT_DROP;
    for(int chunli = 0; chunli < 8 *(int)sizeof(match_result->action); chunli++)
    {
        uint32_t action = 1 << chunli;
        if(match_result->action & action)
        {
            ret = _sdt_match_rules_match_handle_hit(action, pkt, flow, flow->direction, match_result, rule_hash_status);
            if(PKT_DROP == ret)
            {
                return;
            }
        }
    }

    return;
}

//add by huangzw 20221130
static sdt_out_status *get_group_name_status_by_rule_key(struct flow_info *flow, SdtMatchResult *match_result, struct tbl_log  *log, struct tbl_log  **log_new, bool copy_log_flag)
{
    if(!flow || !match_result || !log)
        return NULL;

    sdt_out_status  *lookup_result=NULL;
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d", match_result->unitID, match_result->taskID, match_result->groupID, match_result->ruleId);
    int pos = rte_hash_lookup_data(g_sdt_hash_db, (const void *)rule_key, (void **)&lookup_result);
    if(pos>=0)
    {
        //JSON 规则添加时, g_sdt_hash_db 内的groupName还没有得到初始化
        lookup_result->flag=1;
    }
    else
    //if not in hash table
    {
        sdt_out_status  *rule_elem=NULL;
        rule_elem=(sdt_out_status  *)malloc(sizeof(sdt_out_status));
        if(!rule_elem)
        {
            DPI_LOG(DPI_LOG_ERROR, "rule_hash_code:%u insert hash db failed, malloc failed!",
                    rule_elem->match_result->ruleHashCode)
            return NULL;
        }

        memset(rule_elem, 0, sizeof(sdt_out_status));
        sdt_init_rules_to_hash_db(match_result, rule_elem);
        rule_elem->flag      = 1;

        int retval = rte_hash_add_key_data(g_sdt_hash_db, (const void *)rule_key, (void *)rule_elem);
        if(retval<0)
        {
            DPI_LOG(DPI_LOG_ERROR, "rule_hash_code:%u insert hash db failed! hash table numbers:%d",
                    rule_elem->match_result->ruleHashCode,
                    rte_hash_count(g_sdt_hash_db));
            free(rule_elem);
            rule_elem = NULL;
            return NULL;
        }

        lookup_result = rule_elem;
    }

    return lookup_result;

    // struct tbl_log  *log_ptr = (struct tbl_log  *)log;
    // if(copy_log_flag)
    // {
    //   *log_new = dpi_tbl_clone_ref(log_ptr);
    // }
    // log_ptr->out_elem = lookup_result;
    // lookup_result->log_type = log_ptr->log_type;
    // return 0;
}

int sdt_action_options_approve(SdtMatchResult *actionMatched)
{
    switch(actionMatched->eventOpt)
    {
        case SEO_default:
            return ACTION_APPROVE;

        case SEO_emax_M:    //是否达到 命中次数(先判断,计数器在规则引擎内部递增, 所以是 "<", 不能是 "<=")
            if(actionMatched->hitOnCnt < (size_t)actionMatched->event_args.int_args.M)
                return ACTION_APPROVE;
            break;

        case SEO_emax_M_N:  //是否达到 命中次数/大小上限
            if(actionMatched->hitOnCnt <= (size_t)actionMatched->event_args.int_args.M
                    && actionMatched->hitOnByte/1024/1024 <= (size_t)actionMatched->event_args.int_args.N)//单位是MB
                return ACTION_APPROVE;
            break;

        case SEO_elimit_M: //标书中找不到 此定义
            if(actionMatched->hitOnCnt <= (size_t)actionMatched->event_args.int_args.M)
                return ACTION_APPROVE;
            break;

        case SEO_elimit_M_N: /* 每N秒 命中M次 */
            if(actionMatched->hitOnCnt <= (time(NULL) - actionMatched->hitOnTimeStamp) * actionMatched->event_args.int_args.M)
                return ACTION_APPROVE;
            break;

        case SEO_efreq:      /* 命中比率 每100个输出多少个*/
            if((actionMatched->hitOnCnt % 100) <= (size_t)(actionMatched->event_args.freq * 100))
                return ACTION_APPROVE;
            break;

        default:
            break;
    }
    return ACTION_DENIED;
}

/* 该函数在匹配线程中 */
static int
flow_sdt_match_rule(struct flow_info *flow, const struct pkt_info *pkt, int direction,
        ProtoRecord *pRec, SdtMatchResult *match_result, int rule_nums, uint8_t (*write_proto_log)(void *log),
        void *log, uint8_t *enqueue_flag)
{
    struct mac_packet_header _mac;
    sdt_out_status *o_stat;
    struct tbl_log *log_ptr = (struct tbl_log *)log;
    SdtMatchResult *res = NULL;
    if (log) {
        memcpy(&_mac, &((struct tbl_log *)log)->mac_hdr, sizeof(struct mac_packet_header));
    }

    int i,mi;
    uint32_t  uid_gid_hashcode;
    for(i=0; i<rule_nums;i++)
    {
        res = match_result + i;
        /* 对于无body体的规则只在IPP\TCP\UDP层次有输出，其他层次一律不能有输出*/
        // zhangqx-20250117: 只有协议匹配才走到这里, 协议判定暂不需要
        if(SMH_hint_rule_has_no_body == res->matchHintFlag
           /*&& PROTOCOL_IP!=pRec->proto_id && PROTOCOL_TCP!=pRec->proto_id && PROTOCOL_UDP!=pRec->proto_id*/){
            continue;
        }

        /* 无该协议的规则跳过处理. 例如仅有common类型字段, 在ip/tcp/udp 阶段匹配 */
        if (!bit_get(res->bitmap_proto_id, pschema_get_index(precord_get_schema(pRec->record)))) {
            continue;
        }

        //handle emax/elimit/efreq
        if(ACTION_DENIED == sdt_action_options_approve(match_result + i))
        {
            continue;
        }
        //printf("ACTION_APPROVE %s %zu %zu  %zu\n", __func__, match_result[i].hitOnCnt, match_result[i].hitOnByte, match_result[i].hitOnTimeStamp);

        struct tbl_log *log_new_ptr=NULL;
        bool  copy_log_flag = 0;
        if(i>0) copy_log_flag = 1;
        o_stat = get_group_name_status_by_rule_key(flow, &match_result[i], (struct tbl_log *)log, &log_new_ptr, copy_log_flag);
        if(o_stat == NULL)
        {
            DPI_LOG(DPI_LOG_ERROR, "WARN: get_group_name_status_by_rule_key code:%p %s:%u", o_stat, __FILE__, __LINE__);
            continue;
        }

        //产生用户规则组唯一识别的hashcode
        // uid_gid_hashcode = gen_uid_gid_hash_code(match_result[i].taskID, match_result[i].groupID);
        uid_gid_hashcode = gen_rule_hash_code(match_result + i);
        mi=_sdt_check_rule_exist(flow, uid_gid_hashcode);
        if(mi>=0){
            uint8_t  statis_flag = 0x00;
            if(SAE_report & match_result[i].action)
            {
                if(write_proto_log){
                    log_ptr->match_info[log_ptr->match_res_cnt] = o_stat;
                    log_ptr->add_task[log_ptr->match_res_cnt] = true;
                    log_ptr->match_res_cnt++;
                    //用于327实体文件是否删除；
                    *enqueue_flag = 1;
                    statis_flag |= 0x02;

                    // if(!copy_log_flag || !log_new_ptr){
                    //     struct tbl_log* p = (struct tbl_log *)log;
                    // #ifndef DPI_SDT_ZDY
                    //     write_sdt_rule_info(&match_result[i], p->record);
                    // #else
                    //     write_327ZDY_task_info(&match_result[i], p->record, (const char *)flow->path);
                    // #endif
                    //     *enqueue_flag=write_proto_log(log);
                    // } else {
                    //     struct tbl_log* p = (struct tbl_log *)log_new_ptr;
                    // #ifndef DPI_SDT_ZDY
                    //     write_sdt_rule_info(&match_result[i], p->record);
                    // #else
                    //     write_327ZDY_task_info(&match_result[i], p->record, (const char *)flow->path);
                    // #endif
                    //     write_proto_log((void*)log_new_ptr);
                    // }
                }
            }
            if(SPO_out_pkt==match_result[i].dumpOpt && pkt->pkt_len > 0)
            {
                statis_flag |= 0x02;
                sdt_in_pcap(flow, &match_result[i], flow->sdt_flow.sdt_rules_status[mi], pkt, 0);
            }
            _sdt_handle_rule_statistics(flow,
                    match_result,
                    flow->sdt_flow.sdt_rules_status[mi],
                    pRec->proto_id,
                    statis_flag);
            continue;
        }

        /* 规则第一次命中处理 */
        if(0==g_config.sdt_record_match_rule
                || flow->sdt_flow.sdt_rule_cnt>=g_config.sdt_match_max_rules)
        {   /* 不记录已命中规则模式 或 记录规则数已超过上限 */
            uint8_t  statis_flag = 0x01;
            if(SAE_report & match_result[i].action)
            {
                if(write_proto_log){
                    log_ptr->match_info[log_ptr->match_res_cnt] = o_stat;
                    log_ptr->add_task[log_ptr->match_res_cnt] = false;
                    log_ptr->match_res_cnt++;
                    //用于327实体文件是否删除；
                    *enqueue_flag = 1;
                    statis_flag |= 0x02;
                    // if(!copy_log_flag || !log_new_ptr)
                    //     *enqueue_flag=write_proto_log(log);
                    // else
                    //     write_proto_log((void*)log_new_ptr);
                }
            }
            if (SAE_packetDump & match_result[i].action)
            {
                if (!match_result[i].is_ipff)
                    statis_flag |= 0x02;
            }

            memcpy(&o_stat->mac_hdr, &_mac, sizeof(struct mac_packet_header));
            _sdt_match_rules_match_handle(flow, pkt, pRec, o_stat, &match_result[i]);

            _sdt_handle_rule_statistics(flow,
                    &match_result[i],
                    o_stat,
                    pRec->proto_id,
                    statis_flag);
        }else{/* 记录已命中规则模式 */
            /* 以下为规则第一次命中 */

            uint8_t  statis_flag = 0x01;
            flow->sdt_flow.sdt_rules_rd[flow->sdt_flow.sdt_rule_cnt] = uid_gid_hashcode;
            flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]= o_stat;
            o_stat->flag=1;

            if(SAE_report & match_result[i].action)
            {
                if(write_proto_log){
                    log_ptr->match_info[log_ptr->match_res_cnt] = o_stat;
                    log_ptr->add_task[log_ptr->match_res_cnt] = true;
                    log_ptr->match_res_cnt++;
                    //用于327实体文件是否删除；
                    *enqueue_flag = 1;
                    statis_flag |= 0x02;
                }
            }
            if (SAE_packetDump & match_result[i].action)
            {
                if (!res->is_ipff)
                    statis_flag |= 0x02;
            }

            memcpy(&o_stat->mac_hdr, &_mac, sizeof(struct mac_packet_header));
            _sdt_match_rules_match_handle(flow, pkt, pRec, o_stat, &match_result[i]);

            _sdt_handle_rule_statistics(flow, &match_result[i], o_stat, pRec->proto_id, statis_flag);
            flow->sdt_flow.sdt_rule_cnt++;
        }
    }

    return PKT_OK;
}

/**
* @brief  单包输出
* 单包输出 2023.03.08 19:26 --by chunli
* 1. 单包输出规则形式:
* 1.1 关键字ipff 与 应用层字段筛选 与 packet_dump这三个不可以同时一起
* 1.2 错误示例:ipff 6@*:*->*:*#5:ima,12:c,13:x,15:0,png,HTTP,www,shgjj,Mozilla,len=353 $HTTP.method="GET"; packet_dump(0,0);report();
* 1.3 正确示例:link 6@*:*->*:*#5:ima,12:c,13:x,15:0,png,HTTP,www,shgjj,Mozilla,len=353 $HTTP.method="GET"; packet_dump(0,0);report();
* 1.4 正确示例:ipff 6@*:*->*:*#5:ima,12:c,13:x,15:0,png,HTTP,www,shgjj,Mozilla,len=353 packet_dump(0,0); report();
* 1.5 正确示例:ipff 6@*:*->*:*#5:ima,12:c,13:x,15:0,png,HTTP,www,shgjj,Mozilla,len=353 $HTTP.method="GET"; report();
*/
static int _dpi_match_single_pkt(struct flow_info *flow, SdtMatchResult *result, const struct pkt_info *pkt)
{
  // 仅限is_ipff
  if (0 == result->is_ipff) return 0;

  // 仅限packetDump
  if (!(SAE_packetDump & result->action)) {
    return 0;
  }

  // 仅限dumpOpt
  if (!(SPO_out_pkt == result->dumpOpt || SPO_default == result->dumpOpt)) {
    return 0;
  }

  // handle emax/elimit/efreq
  if (ACTION_DENIED == sdt_action_options_approve(result)) {
    return 0;
  }
  // printf("ACTION_APPROVE %s %zu %zu  %zu\n", __func__, result->hitOnCnt, result->hitOnByte, result->hitOnTimeStamp);

  // 剩下的只有 packet_dump()
  sdt_in_pcap(flow, result, NULL, pkt, 1);
  return 1;
}

static void _match_action_report(struct flow_info *info, write_trans_log write_log, void *field_info, sdt_out_status *stat, ProtoRecord *prec, struct tbl_log *tbl)
{
    if (write_log == NULL || tbl == NULL) return;
    int direction = prec->direction;
    if (tbl->match_res_cnt <= 0 ) {
        write_log(info, field_info, prec, tbl);
    }
    tbl->match_info[tbl->match_res_cnt] = stat;
    tbl->match_res_cnt += 1;
}


/* 该函数在解析线程中 */
int  dpi_multi_mode_trans_match(struct flow_info *flow, const struct pkt_info *pkt, ProtoRecord *pRec,
                                SdtPrematchResult *PktPrematch, write_trans_log write_log, void *field_info,
                                void *tbl)
{
  SdtMatchResult *match_result = NULL;
  int             rule_nums    = 0;
  int             ret          = 0;
  int direction = pRec->direction;

  uint32_t rule_hash = 0;
  int      rule_idx = -1;
  SdtMatchResult *res = NULL;
  sdt_out_status *o_stat = NULL;
  // 空Body是直接算作 命中!
  // flow->sdt_flow.prematch 是流表模式
  match_result = sdtEngine_matchSdtRules(flow->sdt_flow.pEngine, pRec, &rule_nums, PktPrematch);
  if (!match_result || rule_nums <= 0) {
    return PKT_OK;
  }
  sdx_match_status_add(flow->thread_id, pRec->proto_id, 1);

  for (int i = 0; i < rule_nums; i++) {
    res = match_result + i;

    /* 对于无body体的规则只在IPP\TCP\UDP层次有输出，其他层次一律不能有输出*/
    // zhangqx-20250117: 只有单包匹配才走到这里, 协议判定暂不需要
    // if (SMH_hint_rule_has_no_body == res->matchHintFlag && 
    //     PROTOCOL_IP != pRec->proto_id && PROTOCOL_TCP != pRec->proto_id && PROTOCOL_UDP != pRec->proto_id) {
    //     continue;
    // }

    uint8_t statis_flag = 0x00;

    ret = _dpi_match_single_pkt(flow, res, pkt);
    statis_flag |= (ret << 1);

    // handle emax/elimit/efreq
    if (ACTION_DENIED == sdt_action_options_approve(res)) {
      continue;
    }

    if (flow->sdt_flow.sdt_rule_cnt < g_config.sdt_match_max_rules) {
      rule_hash = gen_rule_hash_code(res);
      rule_idx  = _sdt_check_rule_exist(flow, rule_hash);
    }

    if (rule_idx >= 0) {
      o_stat = flow->sdt_flow.sdt_rules_status[rule_idx];
    } else {
      o_stat = sdt_rule_hash_db_lookup(res);
      if (o_stat) {
        o_stat->flag = 1;
      }
    }

    // 记录规则数已达到上限 或 该规则已经被记录
    if (flow->sdt_flow.sdt_rule_cnt == g_config.sdt_match_max_rules || rule_idx >= 0) {
      if (SAE_report & res->action) {
        _match_action_report(flow, write_log, field_info, o_stat, pRec, tbl);
        statis_flag |= 0x02;
      }
      if (SPO_out_pkt == match_result[i].dumpOpt) {
        sdt_in_pcap(flow, &match_result[i], flow->sdt_flow.sdt_rules_status[rule_idx], pkt, 0);
        statis_flag |= 0x02;
      }
      _sdt_handle_rule_statistics(flow, &match_result[i], o_stat, pRec->proto_id, statis_flag);
      continue;
    }

    /* 规则第一次命中处理 */
    /* 不记录已命中规则模式 或 记录规则数已超过上限 */
    if (0 == g_config.sdt_record_match_rule || flow->sdt_flow.sdt_rule_cnt >= g_config.sdt_match_max_rules) {

      if (SAE_report & res->action) {
        _match_action_report(flow, write_log, field_info, o_stat, pRec, tbl);
      }
      _sdt_handle_rule_statistics(flow, &match_result[i], o_stat, pRec->proto_id, 0x03);

      if (g_config.sdt_mac_forward_flag && flow->pSDTMacHeader[direction]) {
        memcpy(&o_stat->mac_hdr, flow->pSDTMacHeader[direction], sizeof(struct mac_packet_header));
      }
      _sdt_match_rules_match_handle(flow, pkt, pRec, o_stat, &match_result[i]);
    } else {
      /* 记录已命中规则模式 */
      /* 以下为规则第一次命中 */
      flow->sdt_flow.sdt_rules_rd[flow->sdt_flow.sdt_rule_cnt]     = rule_hash;
      flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt] = sdt_rule_hash_db_lookup(&match_result[i]);
      if (flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]) {
        flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]->flag = 1;
      }
      if (SAE_report & res->action) {
        _match_action_report(flow, write_log, field_info, flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt], pRec, tbl);
      }

      _sdt_handle_rule_statistics(flow, &match_result[i], flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt],
                                  pRec->proto_id, 0x03);

      if (g_config.sdt_mac_forward_flag && flow->pSDTMacHeader[direction]) {
        memcpy(&flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]->mac_hdr, flow->pSDTMacHeader[direction],
               sizeof(struct mac_packet_header));
      }
      _sdt_match_rules_match_handle(flow, pkt, pRec, flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt],
                                    &match_result[i]);
      flow->sdt_flow.sdt_rule_cnt++;
    }
  }
  // pthread_mutex_unlock(&flow->mutex);
  return ret;
}

int
dpi_sdt_engine_proto_match_rules_with_more_acl(struct flow_info *flow,
                                    const struct pkt_info  *pkt,
                                    SdtEngine              *app_engine,
                                    ProtoRecord            *pRec,
                                    uint8_t (*write_proto_log)(void *log),
                                    void *log,
                                    uint8_t *enqueue_flag)
{
    SdtMatchResult *match_result    = NULL;
    int            rule_nums        = 0;
    int            ret              = 0;

    match_result=sdtEngine_matchSdtRules(app_engine, pRec, &rule_nums,flow->sdt_flow.prematch);
    if(!match_result || rule_nums <=0){
        return PKT_OK;
    }

    sdx_match_status_add(flow->thread_id, pRec->proto_id, 1);

    // pthread_mutex_lock(&flow->mutex);
    ret=flow_sdt_match_rule(flow, pkt, flow->direction, pRec, match_result,rule_nums, write_proto_log,log, enqueue_flag);
    // pthread_mutex_unlock(&flow->mutex);
    return ret;
}


  int
  dpi_sdt_engine_proto_match_rules(struct flow_info        *flow,
                                    const struct pkt_info  *pkt,
                                    SdxMatchThreadCtx      *match_ctx,
                                    ProtoRecord            *pRec,
                                    uint8_t (*write_proto_log)(void *log),
                                    void *log,
                                    uint8_t *enqueue_flag)
  {
      if(!flow){
        return PKT_DROP;
      }

      if(unlikely(1==g_config.stop_rcv_pkts)){
          return PKT_DROP;
      }

      int ret = PKT_OK;
      SdtEngine *app_engine = match_ctx->app_engine;

      /* 该流已经匹配到最大可匹配的规则数，后续不在进行规则匹配*/
      if(flow->sdt_flow.sdt_rule_cnt>=g_config.sdt_match_max_rules){
          /* 已经命中规则中有report或者out_pkt(单包模式) 任然需要继续进行规则匹配 */
          if(!sdt_check_rule_exist_report(flow))
          {
              return PKT_DROP;
          }
      }

    //ACL 多模
    sdt_acl_match_result_copy(&flow->acl_result, &pRec->acl_result);
    int loop_max = pRec->acl_result.aclHashCnt;
    int i = 0;
    for(i = 0; i < loop_max; i++)
    {
        ret |= dpi_sdt_engine_proto_match_rules_with_more_acl(flow, pkt, app_engine, pRec, write_proto_log, log, enqueue_flag);
    }

    if (((struct tbl_log *)log)->match_res_cnt > 0) {
        if (write_proto_log(log) != 0) {
            dpi_tbl_free(log);
        }
    }

      return ret;
  }


int
dpi_sdt_packet_match_process(struct flow_info *flow, struct pkt_info *pkt, const uint8_t *payload, uint32_t payload_len, int dir)
{
    uint32_t ip_len = 0;
    if(pkt->ethhdr==NULL || pkt->pkt_len<=0){
        return PKT_DROP;
    }
    int ret=PKT_OK;
    // pthread_mutex_lock(&flow->mutex);
    /* 先对之前已经匹配规则，按照动作类型处理数据 */
    ret=_sdt_stream_action_process(flow, pkt, dir);
    // pthread_mutex_unlock(&flow->mutex);
    if(PKT_DROP==ret){
        goto EXIT_DROP;
    }

    ProtoRecord pRec;
    memset(&pRec,  0, sizeof(ProtoRecord ));

    ret = sdt_init_prec(flow, pkt, &pRec, dir);
    if(ret==PKT_DROP){
        goto EXIT_DROP;;
    }

    /* 流已经命中规则，但是对于out_pkt和report还需继续匹配，无需缓存报文 */
    if(flow->sdt_flow.sdt_rule_cnt==g_config.sdt_rule_max_num){
        goto rule_match;
    }

    //char buff[256];
    //memset(buff, 0, sizeof(buff));
    //bintohex(pRec.pPayload.pBuff, pRec.pPayload.len, buff, sizeof(buff)-2);
    //printf("dpi_sdt_packet_match_process %s\n", buff);

    /* 包缓存功能，用于规则命中有pcap文件输出的规则 */
    ret=pkt_stream_add_item(&flow->sdt_flow.pkt_stream_head[0], flow->sdt_flow.pkt_stream_num[0], pRec);
    if(ret==1) {
        struct packet_stream * stream = (struct packet_stream * )list_entry((&flow->sdt_flow.pkt_stream_head[0])->prev, typeof(*stream), node);//获取刚刚加入双向流链表的对象
        flow->sdt_flow.pkt_stream_num[0]++;
        ret=pkt_single_stream_add_item(&flow->sdt_flow.single_stream_head[pRec.direction], stream);
        if(ret==1){
            flow->sdt_flow.single_stream_num[pRec.direction]++;
        }
    }else if(ret < 0){
        DPI_LOG(DPI_LOG_WARNING, "add pkt cache pkt to stream failed! ret=%d",ret);
    }

    if(flow->acl_result.sumInfo_matchModeFlag&SPMM_match_per_session)
    {
        /* tcp流模式匹配缓存：处理完tcp重传、乱序、踩踏在缓存到block中*/
        if( IPPROTO_TCP==pRec.ip_proto){
            if(payload && payload_len>0){
                _sdt_tcp_rsm(flow, dir, pRec.seq,
                            payload,
                            payload_len,
                            DISSECT_PKT_ORIGINAL);
            }
        }else if(IPPROTO_UDP==pRec.ip_proto){/* udp流模式匹配缓存：按照报文到达先后顺序组装block */
            _sdt_stream_data_move_block(&flow->sdt_flow.sdt_l4stream_cache[pRec.direction], payload, payload_len);
        }
    }

    //只有存在 规则体, 才需要准备 [协议字段]
    // if(SMH_hint_rule_has_body == flow->acl_result.sumInfo_matchHintFlag)
    // {
    //     write_shared_header(pRec.record, TBL_LOG_MAX_LEN, flow, dir);
    //     write_link(pRec.record, flow, dir);
    // }
    // pRec.record = flow->record;

rule_match:

    //预匹配中的 包长限制 len指的是IP的包长, 不是传输层载荷的长度
    //但是len已经被编译到正则的语法里面去了
    //这里用缓存适配一下
    if(pkt->ipversion == 4){ip_len=ntohs(pkt->iph->tot_len);}
    if(pkt->ipversion == 6){ip_len=ntohs((pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen));}

    uint8_t prematch_buff[2000];
    //过大判断
    if(ip_len > sizeof(prematch_buff) || payload_len > sizeof(prematch_buff))
    {
        goto EXIT_DROP;
    }
    memcpy(prematch_buff, payload, payload_len);
    memset(prematch_buff+ payload_len, 0, sizeof(prematch_buff) - payload_len);

    if (pRec.ip_proto == IPPROTO_TCP) {
      ret = dpi_sdt_tcp_match(flow, pkt, &pRec, prematch_buff, ip_len);
    } else if (pRec.ip_proto == IPPROTO_UDP) {
      ret = dpi_sdt_udp_match(flow, pkt, &pRec, prematch_buff, ip_len);
    } else if (pRec.ip_proto == IPPROTO_SCTP) {
      ret = dpi_sdt_sctp_match(flow, pkt, &pRec, prematch_buff, ip_len);
    } else
    {
#ifndef DPI_SDT_ZDY
    //327不输出ip
    //预匹配 标书要求使用ip_len
    ret = dpi_sdt_ip_match(flow, pkt, &pRec, prematch_buff, ip_len);
#endif
    }

    sdt_destroy_prec(&pRec);//只对本次的 prec 负责析构
    return ret;

EXIT_DROP:
    sdt_destroy_prec(&pRec);//只对本次的 prec 负责析构
    return PKT_DROP;
}

int
dpi_sdt_flow_link_match(struct flow_info *flow)
{
    /* sdt ip通联监测要素 在超时时做规则匹配 */
    if(likely(0==g_config.stop_rcv_pkts)){

        if(IPPROTO_TCP==flow->tuple.inner.proto){
            _sdt_tcp_flow_finish(flow);
        }
    }

    if(flow->msg){
        free(flow->msg);
        flow->msg=NULL;
    }


    return 0;
}


int
dpi_sdt_flow_timeout(struct flow_info *flow)
{
    pkt_stream_free_node(&flow->sdt_flow.pkt_stream_head[0]);
    pkt_stream_free_node(&flow->sdt_flow.pkt_stream_head[1]);

    int i=0;
    for(i=0;i<PKT_STREAM_DIR_NUM;i++){
        INIT_LIST_HEAD(&flow->sdt_flow.single_stream_head[i]);
        INIT_LIST_HEAD(&flow->sdt_flow.tcp_rsm_head[i]);
        INIT_LIST_HEAD(&flow->sdt_flow.pkt_stream_head[i]);

        if(flow->sdt_flow.sdt_ip_cache[i].buff){
            free(flow->sdt_flow.sdt_ip_cache[i].buff);
            flow->sdt_flow.sdt_ip_cache[i].buff=NULL;
        }

        if(flow->sdt_flow.sdt_l4stream_cache[i].buff){
            free(flow->sdt_flow.sdt_l4stream_cache[i].buff);
            flow->sdt_flow.sdt_l4stream_cache[i].buff=NULL;
        }
    }

    // 预匹配的清除动作, 要放在最后最后
    // 超时 的时候, 存在flush动作, 存在匹配动作, 需要flow->prematch
    if(flow->sdt_flow.prematch)
    {
        sdtEngine_matchSdtRulesFree(flow->sdt_flow.prematch);
        flow->sdt_flow.prematch  = NULL;
    }

    return 0;
}


static int app_match_rule(SdxMatchThreadCtx *match_ctx, struct tbl_log  *tbl, struct flow_info *flow)
{
    char __str[256];
    uint8_t enqueue_flag=0;
    int32_t precord_field_num = 0;

    ProtoRecord pRec;
    memset(&pRec, 0, sizeof(ProtoRecord));

    precord_t *record = tbl->record;
    pRec.record = tbl->record;
    int proto_id = tbl->proto_id;

    pRec.proto_id        = tbl->proto_id;
    pRec.direction       = tbl->flow->direction;

    pRec.tuple.ipversion = tbl->flow->tuple.inner.ip_version;
    if (tbl->flow->tuple.inner.ip_version == 4) {
        get_iparray_to_string(__str, sizeof(__str), tbl->flow->tuple.inner.ip_dst);
        pRec.tuple.IpDst.ipv4   = (int)inet_addr((const char *)__str);
        get_iparray_to_string(__str, sizeof(__str), tbl->flow->tuple.inner.ip_src);
        pRec.tuple.IpSrc.ipv4   = (int)inet_addr((const char *)__str);
    } else if (tbl->flow->tuple.inner.ip_version == 6) {
        memcpy(pRec.tuple.IpDst.ipv6, (const char *)tbl->flow->tuple.inner.ip_dst, sizeof(pRec.tuple.IpDst.ipv6));
        memcpy(pRec.tuple.IpSrc.ipv6, (const char *)tbl->flow->tuple.inner.ip_src,  sizeof(pRec.tuple.IpSrc.ipv6));
    }

    pRec.tuple.PortDst = tbl->flow->tuple.inner.port_dst;
    pRec.tuple.PortSrc = tbl->flow->tuple.inner.port_src;
    strncpy(pRec.proto_name, protocol_name_array[tbl->proto_id], strlen(protocol_name_array[tbl->proto_id]));

    pRec.raw_data_len=tbl->flow->src2dst_bytes+tbl->flow->dst2src_bytes;

    struct pkt_info  pkt;
    memset(&pkt, 0, sizeof(struct pkt_info));
    pkt.pkt_len = tbl->pkt_data_len;
    pkt.raw_pkt = tbl->pkt_data;
    pkt.ethhdr = (const struct dpi_ethhdr *)tbl->pkt_data;

    pRec.pPayload.pBuff=(uint8_t *)pkt.raw_pkt;
    pRec.pPayload.len=pkt.pkt_len;

    pRec.match_data_len = tbl->match_data_len;

    dpi_sdt_engine_proto_match_rules(flow, &pkt, match_ctx, &pRec, dpi_app_match_res_enqueue, (void *)tbl, &enqueue_flag);

#ifdef DPI_SDT_ZDY
    //删除未匹配记录的实体文件
    if(!enqueue_flag)
        del_not_match_tbl_file(tbl->record, tbl->proto_id);
#endif
    return 0;
}

/*
* 应用协议解析结果进行规则匹配线程
*/
void *app_fields_sdt_match_func(void * arg)
{
    SdxMatchThreadCtx *match_ctx = (SdxMatchThreadCtx*)arg;

    int  index;
    int  dequeue_num =0;
    long ring_id = match_ctx->ring_id;
    struct tbl_log  *tbl;
    struct tbl_log  *tbl_burst[TBL_MAX_BURST];
    uint8_t core_id = 0;
    pthread_t thread = pthread_self();
    char thread_name[16];
    int ret = 0;

    core_id = match_ctx->core_id;

    snprintf(thread_name, sizeof(thread_name), "dpi_match_%d", core_id);
    pthread_setname_np(thread, thread_name);

    ret = dpi_pthread_setaffinity(thread, core_id);
    if (!ret) {
        DPI_LOG(DPI_LOG_ERROR, "App fields sdt match thread failed to bind core_id!!!");
    }

    log_info("App fields sdt match thread on core %d", core_id);

    SdtEngine  *app_engine = sdtEngine_init(match_ctx->engine_id, g_config.sdt_switch_mult_hit);
//    assert(app_engine);

    match_ctx->app_engine = app_engine;

    ATOMIC_ADD_FETCH(&sdt_match_thfunc_signal);
    // DPI_LOG(DPI_LOG_DEBUG, "Running app fields sdt match thread on core %d", core_affinity);

    while (1) {
        // 线程 结束信号
        if(unlikely(1==g_config.exit_process)) {
            dpi_free(match_ctx);
            sdtEngine_fini(app_engine);
            ATOMIC_SUB_FETCH(&sdt_match_thfunc_signal);
            pthread_exit(0);
        }

        // 线程 暂停信号
        if (unlikely(g_sdt_hash_db_clear_flag))
        {
            // 规则即将重置, 匹配线程暂停, 等新规则到来再继续匹配
            ATOMIC_SUB_FETCH(&sdt_match_thfunc_signal);
            while(g_sdt_hash_db_clear_flag)
            {
                usleep(1000*100);
            }
            ATOMIC_ADD_FETCH(&sdt_match_thfunc_signal);
        }

        dequeue_num = rte_ring_sc_dequeue_burst(app_match_ring[ring_id],
                                               (void **)tbl_burst, TBL_MAX_BURST, NULL);
        if (dequeue_num <= 0) {
            usleep(1000*100);
            continue;
        }

        for (index = 0; index < dequeue_num; index++) {
            tbl = tbl_burst[index];
            struct flow_info *flow=tbl->flow;

            if(unlikely(NULL==flow)){
                DPI_LOG(DPI_LOG_ERROR, "find tbl->flow is NULL proto[%s]",protocol_name_array[tbl->proto_id]);
                dpi_tbl_free(tbl);
                continue;
            }
            //规则匹配
            app_match_rule(match_ctx, tbl, flow);

            dpi_tbl_free(tbl);
        }
    }
}


int init_app_match_ring(void)
{
    for (int i = 0; i < g_config.app_match_thread_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "app_match_ring_%d_%d",g_config.socketid, i);
        app_match_ring[i] = rte_ring_create(ring_name, g_config.tbl_ring_size, g_config.socketid, RING_F_SC_DEQ);
        if (app_match_ring[i] == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "error while create app match ring\n");
            exit(-1);
        }
        if (rte_ring_lookup(ring_name) != app_match_ring[i]) {
            DPI_LOG(DPI_LOG_ERROR, "Cannot lookup app match ring from its name\n");
            exit(-1);
        }
    }
    return 0;
}
