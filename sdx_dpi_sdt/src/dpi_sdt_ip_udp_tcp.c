#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <glib.h>

#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_tcp_reassemble.h"

#include "sdt_ip_protocols.h"
#include "sdt_action_out.h"
#include "dpi_sdt_match.h"
#include "dpi_sdt_link.h"
#include "dpi_pschema.h"

extern struct rte_mempool *tbl_log_mempool;

#define SDX_NEXT_MAX_HEADER   256
#define SDX_NEXT_MAX_CONTENT  3000

typedef enum _sdt_tran_type
{
  EM_TRAN_IP,
  EM_TRAN_TCP,
  EM_TRAN_UDP,
  EM_TRAN_SCTP,
}sdt_tran_type;

enum _sdt_ip_enum{
    EM_SDT_IP_FLAG,
    EM_SDT_IP_LEN,
    EM_SDT_IP_HEADER,
    EM_SDT_IP_TTL,
    EM_SDT_IP_CONTENT,
    EM_SDT_IP_CONTENT_LEN,

    EM_SDT_IP_MAX
};


enum _sdt_udp_enum{
    EM_SDT_UDP_HEADER,
    EM_SDT_UDP_PAYLOAD,
    EM_SDT_UDP_PAYLOAD_LEN,

    EM_SDT_UDP_MAX
};


enum _sdt_tcp_enum{
    EM_SDT_TCP_HEADER,
    EM_SDT_TCP_HEADER_LEN,
    EM_SDT_TCP_FLAG,
    EM_SDT_TCP_FLAG_FIN,
    EM_SDT_TCP_FLAG_SYN,
    EM_SDT_TCP_FLAG_RST,
    EM_SDT_TCP_FLAG_PSH,
    EM_SDT_TCP_FLAG_ACK,
    EM_SDT_TCP_FLAG_URG,
    EM_SDT_TCP_FLAG_ECE,
    EM_SDT_TCP_FLAG_CWR,
    EM_SDT_TCP_FLAG_NS,
    EM_SDT_TCP_WINDOWSIZE,
    EM_SDT_TCP_PAYLOAD,
    EM_SDT_TCP_PAYLOAD_LEN,

    EM_SDT_TCP_MAX
};



static dpi_field_table ip_field_array[] = {
     DPI_FIELD_D(EM_SDT_IP_FLAG,                 YV_FT_UINT16,       "ip_flag"),
     DPI_FIELD_D(EM_SDT_IP_LEN,                  YV_FT_UINT16,       "ip_len"),
     DPI_FIELD_D(EM_SDT_IP_HEADER,               YV_FT_BYTES,        "ip_header"),
     DPI_FIELD_D(EM_SDT_IP_TTL,                  YV_FT_UINT8,        "ip_ttl"),
     DPI_FIELD_D(EM_SDT_IP_CONTENT,              YV_FT_BYTES,        "ip_content"),
     DPI_FIELD_D(EM_SDT_IP_CONTENT_LEN,          YV_FT_UINT16,       "ip_contentlen"),
};


static dpi_field_table udp_field_array[] = {
    DPI_FIELD_D(EM_SDT_UDP_HEADER,               YV_FT_BYTES,       "udp_header"),
    DPI_FIELD_D(EM_SDT_UDP_PAYLOAD,              YV_FT_BYTES,       "udp_payload"),
    DPI_FIELD_D(EM_SDT_UDP_PAYLOAD_LEN,          YV_FT_UINT16,      "udp_payloadlen"),
};


static dpi_field_table tcp_field_array[] = {

    DPI_FIELD_D(EM_SDT_TCP_HEADER,                 YV_FT_BYTES,        "tcp_header"),
    DPI_FIELD_D(EM_SDT_TCP_HEADER_LEN,             YV_FT_UINT8,        "tcp_headerlen"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG,                   YV_FT_UINT16,       "tcp_flag"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_FIN,               YV_FT_UINT8,        "tcp_fin"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_SYN,               YV_FT_UINT8,        "tcp_syn"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_RST,               YV_FT_UINT8,        "tcp_rst"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_PSH,               YV_FT_UINT8,        "tcp_psh"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_ACK,               YV_FT_UINT8,        "tcp_ack"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_URG,               YV_FT_UINT8,        "tcp_urg"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_ECE,               YV_FT_UINT8,        "tcp_ece"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_CWR,               YV_FT_UINT8,        "tcp_cwr"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_NS,                YV_FT_UINT8,        "tcp_ns"),
    DPI_FIELD_D(EM_SDT_TCP_WINDOWSIZE,             YV_FT_UINT16,       "tcp_window"),
    DPI_FIELD_D(EM_SDT_TCP_PAYLOAD,                YV_FT_BYTES,        "tcp_payload"),
    DPI_FIELD_D(EM_SDT_TCP_PAYLOAD_LEN,            YV_FT_UINT16,       "tcp_payloadlen"),

};


static int _write_ip(struct flow_info *flow, int direction, const void *field_info, precord_t * record)
{
  int              i       = 0;
  int              idx     = 0;
  struct tbl_log  *log_ptr = NULL;
  struct pkt_info *pkt     = (struct pkt_info *)field_info;

  const uint8_t *content     = NULL;
  int            content_len = 0;

  const uint8_t *ip_header     = NULL;
  int            ip_header_len = 0;
  if (pkt->ipversion == 4) {
    ip_header     = (const uint8_t *)pkt->iph;
    ip_header_len = pkt->iph->ihl * 4;

    content     = (const uint8_t *)pkt->iph + ip_header_len;
    content_len = ntohs(pkt->iph->tot_len) - ip_header_len;
  } else if (pkt->ipversion == 6) {
    ip_header     = (const uint8_t *)pkt->iph6;
    ip_header_len = sizeof(struct dpi_ipv6hdr);

    content     = (const uint8_t *)pkt->iph6 + ip_header_len;
    content_len = ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
  } else {
    return PKT_DROP;
  }

    // #ifdef DPI_SDT_ZDY
    //     write_327ZDY_task_info(match_result, record, (const char *)flow->path);
    // #endif
    player_t *layer = precord_layer_put_new_layer(record, "ip");

    for(i=0;i<EM_SDT_IP_MAX;i++){
        switch(i){
        case EM_SDT_IP_FLAG:
            if(pkt->iph)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)ntohs(pkt->iph->frag_off));
            else
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_SDT_IP_LEN:
            if(pkt->iph)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,  (uint64_t)ntohs(pkt->iph->tot_len));
            else if(pkt->iph6)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,  (uint64_t)ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen));
            else
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_SDT_IP_HEADER:
            write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)ip_header, ip_header_len);
            break;
        case EM_SDT_IP_TTL:
            if(pkt->ipversion==4)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)pkt->iph->ttl);
            else
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim);
            break;
        case EM_SDT_IP_CONTENT:
            write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)content, content_len);
            break;
        case EM_SDT_IP_CONTENT_LEN:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)content_len);
            break;
        default:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }
    return 0;
}

static int write_ip_log(struct flow_info *flow, const void *field_info, ProtoRecord *prec, void *tbl)
{
    int i = 0;
    int idx = 0;
    struct pkt_info  *pkt=(struct pkt_info  *)field_info;
    struct tbl_log *log_ptr = (struct tbl_log *)tbl;

    const uint8_t *content=NULL;
    int     content_len=0;

    const uint8_t *ip_header=NULL;
    int      ip_header_len=0;
    if(pkt->ipversion==4){
        ip_header=(const uint8_t *)pkt->iph;
        ip_header_len=pkt->iph->ihl*4;

        content=(const uint8_t *)pkt->iph+ip_header_len;
        content_len=ntohs(pkt->iph->tot_len) - ip_header_len;
    }else if(pkt->ipversion==6){
        ip_header=(const uint8_t *)pkt->iph6;
        ip_header_len=sizeof(struct dpi_ipv6hdr);

        content=(const uint8_t *)pkt->iph6+ip_header_len;
        content_len=ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
    }else{
        return PKT_DROP;
    }
    // #ifdef DPI_SDT_ZDY
    //     write_327ZDY_task_info(match_result, record, (const char *)flow->path);
    // #endif

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_IP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->proto_id    = PROTOCOL_IP;

    return PKT_DROP;
}

static int _write_udp(struct flow_info *flow, int direction, const void *field_info, precord_t * record)
{
    int i = 0;
    int idx = 0;
    struct pkt_info  *pkt=(struct pkt_info  *)field_info;

    int      payload_len=0;
    const uint8_t *payload=NULL;
    int           ip_header_len=0;
    if(4==pkt->ipversion){
        ip_header_len=pkt->iph->ihl*4;
        payload_len = ntohs(pkt->iph->tot_len) - ip_header_len - sizeof( struct dpi_udphdr);
        payload     = (const uint8_t *)pkt->udph+sizeof( struct dpi_udphdr);
    }else if(6==pkt->ipversion){
        payload_len = ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - sizeof(struct dpi_udphdr);
        payload     = (const uint8_t *)pkt->udph+sizeof(struct dpi_udphdr);
    }else{
        return PKT_DROP;
    }

    if(payload_len<0 || payload_len>pkt->pkt_len){
        return PKT_DROP;
    }

    // dpi_precord_new_record(log_ptr->record, NULL, NULL);
    // write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    // precord_t *record = log_ptr->record;
    // #ifdef DPI_SDT_ZDY
    //     write_327ZDY_task_info(match_result, record, (const char *)flow->path);
    // #endif

#ifdef DPI_SDT_ZDY
    //更新共性字段
    precord_layer_remove(record, "common");
    precord_layer_remove(record, "327_common");
    write_common(record, TBL_LOG_MAX_LEN, flow, direction);
#endif
    player_t *layer = precord_layer_put_new_layer(record, "udp");
    for (i = 0; i < EM_SDT_UDP_MAX; i++) {
      switch (i) {
        case EM_SDT_UDP_HEADER:
          write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)pkt->udph, (uint32_t)sizeof(struct dpi_udphdr));
          break;
        case EM_SDT_UDP_PAYLOAD:
          write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)payload, (uint32_t)payload_len);
          break;
        case EM_SDT_UDP_PAYLOAD_LEN:
          write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, udp_field_array[i].type, NULL, (uint64_t)payload_len);
          break;
        default:
          write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
          break;
      }
    }
    return PKT_DROP;
}

static int write_udp_log(struct flow_info *flow, const void *field_info, ProtoRecord *prec, void *tbl)
{

    int i = 0;
    int idx = 0;
    struct tbl_log *log_ptr = (struct tbl_log *)tbl;
    struct pkt_info  *pkt=(struct pkt_info  *)field_info;

    append_proto_info_no_real_protocol_id(flow);
    // #ifdef DPI_SDT_ZDY
    //     write_327ZDY_task_info(match_result, record, (const char *)flow->path);
    // #endif

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_UDP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->flow        = flow;
    log_ptr->proto_id   = PROTOCOL_UDP;

    return PKT_DROP;
}

static int _write_tcp(struct flow_info *flow, int direction, const void *field_info, precord_t * record)
{
    int i = 0;
    int idx = 0;

    const  uint8_t *payload=NULL;
    int payload_len    = 0;
    int ipv6_option_len = 0;
    int ipv4_headerlen = 0;

    struct pkt_info  *pkt=(struct pkt_info  *)field_info;

    int tcp_header_len=pkt->tcph->doff*4;
    payload=(const uint8_t *)pkt->tcph + tcp_header_len;
    if(4==pkt->ipversion){
        ipv4_headerlen=pkt->iph->ihl*4;
        payload_len=ntohs(pkt->iph->tot_len)-ipv4_headerlen-tcp_header_len;
    }else if(6==pkt->ipversion){
        ipv6_option_len=(const uint8_t *)pkt->tcph-(const uint8_t *)pkt->iph6-sizeof(struct dpi_ipv6hdr);
        payload_len=ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - ipv6_option_len -tcp_header_len;
    }else{
        return PKT_DROP;
    }

    if(payload_len<0 ||payload_len>pkt->pkt_len || ipv6_option_len<0){
        return PKT_DROP;
    }

#ifdef DPI_SDT_ZDY
    //更新共性字段
    precord_layer_remove(record, "common");
    precord_layer_remove(record, "327_common");
    write_common(record, TBL_LOG_MAX_LEN, flow, direction);
#endif
    player_t *layer = precord_layer_put_new_layer(record, "tcp");

    // #ifdef DPI_SDT_ZDY
    //     write_327ZDY_task_info(match_result, record, (const char *)flow->path);
    // #endif

    for(i=0;i<EM_SDT_TCP_MAX;i++){
        switch(i){
        case EM_SDT_TCP_HEADER:
             write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)pkt->tcph, pkt->tcph->doff*4);
             break;
         case EM_SDT_TCP_HEADER_LEN:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->doff*4);
             break;
         case EM_SDT_TCP_FLAG:
         {
             uint16_t tcp_flags=pkt->tcph->doff<<12|
                        pkt->tcph->res1<<8 |
                        pkt->tcph->cwr<<7  |
                        pkt->tcph->ece<<6  |
                        pkt->tcph->urg<<5  |
                        pkt->tcph->ack<<4  |
                        pkt->tcph->psh<<3  |
                        pkt->tcph->rst<<2  |
                        pkt->tcph->syn<<1  |
                        pkt->tcph->fin;
             tcp_flags&=0x0FFF;
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, tcp_flags);
         }
             break;
         case EM_SDT_TCP_FLAG_FIN:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->fin);
             break;
         case EM_SDT_TCP_FLAG_SYN:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->syn);
             break;
         case EM_SDT_TCP_FLAG_RST:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->rst);
             break;
         case EM_SDT_TCP_FLAG_PSH:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->psh);
             break;
         case EM_SDT_TCP_FLAG_ACK:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->ack);
             break;
         case EM_SDT_TCP_FLAG_URG:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->urg);
             break;
         case EM_SDT_TCP_FLAG_ECE:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->ece);
             break;
         case EM_SDT_TCP_FLAG_CWR:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->cwr);
             break;
         case EM_SDT_TCP_FLAG_NS:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->res1&0x1);
             break;
         case EM_SDT_TCP_WINDOWSIZE:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, ntohs(pkt->tcph->window));
             break;
        case EM_SDT_TCP_PAYLOAD:
            write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)payload, (uint32_t)payload_len);
            break;
        case EM_SDT_TCP_PAYLOAD_LEN:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, payload_len);
            break;
        default:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

    return PKT_DROP;
}


static int write_tcp_log(struct flow_info *flow, const void *field_info, ProtoRecord *prec, void *tbl)
{
    int i = 0;
    int idx = 0;
    struct tbl_log *log_ptr = (struct tbl_log *)tbl;

    const  uint8_t *payload=NULL;
    int payload_len    = 0;
    int ipv6_option_len = 0;
    int ipv4_headerlen = 0;

    struct pkt_info  *pkt=(struct pkt_info  *)field_info;

    int tcp_header_len=pkt->tcph->doff*4;
    payload=(const uint8_t *)pkt->tcph + tcp_header_len;
    if(4==pkt->ipversion){
        ipv4_headerlen=pkt->iph->ihl*4;
        payload_len=ntohs(pkt->iph->tot_len)-ipv4_headerlen-tcp_header_len;
    }else if(6==pkt->ipversion){
        ipv6_option_len=(const uint8_t *)pkt->tcph-(const uint8_t *)pkt->iph6-sizeof(struct dpi_ipv6hdr);
        payload_len=ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - ipv6_option_len -tcp_header_len;
    }else{
        return PKT_DROP;
    }

    if(payload_len<0 ||payload_len>pkt->pkt_len || ipv6_option_len<0){
        return PKT_DROP;
    }

    // #ifdef DPI_SDT_ZDY
    //     write_327ZDY_task_info(match_result, record, (const char *)flow->path);
    // #endif
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_TCP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->flow        = flow;
    log_ptr->proto_id   = PROTOCOL_TCP;
    return PKT_DROP;
}

/////////////////////// build_ProtoRecord_ START ...

int build_ProtoRecord_IP(ProtoRecord *pDst, ProtoRecord *pSrc, int with_body, const struct pkt_info *pkt)
{
    int idx = 0,i;
    int str_len = 0;
    char tmp[512]={0};
    int  ret=PKT_DROP;
    const  uint8_t *payload = pkt->payload;
    uint32_t payload_len = pkt->payload_len;
    char header[SDX_NEXT_MAX_HEADER];   //不要"={0}", CPU 时钟宝贵
    char content[SDX_NEXT_MAX_CONTENT]; //不要"={0}", CPU 时钟宝贵

    //如果没有 协议字段规则, 没有必要写入操作. 浪费CPU时钟
    if(with_body)
    {
        memset(header, 0, sizeof(header));
        memset(content, 0, sizeof(content));
    }

    pDst->proto_id      = PROTOCOL_IP;
    pDst->direction     = pSrc->direction;
    if (pkt->iph) {
        pDst->tuple.IpDst.ipv4   =  get_uint32_ntohl(pkt->iph->daddr,0);
        pDst->tuple.IpSrc.ipv4   =  get_uint32_ntohl(pkt->iph->saddr,0);
        pDst->tuple.protoID      =  IP_PROTO_IPV4;
        pDst->tuple.ipversion    = 4;
    } else
    if (pkt->iph6) {
        memcpy(pDst->tuple.IpDst.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pDst->tuple.IpDst.ipv6));
        memcpy(pDst->tuple.IpSrc.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pDst->tuple.IpSrc.ipv6));
        pDst->tuple.protoID      = IP_PROTO_IPV6;
        pDst->tuple.ipversion    = 6;
    }

    strcpy(pDst->proto_name,"ip");

    return 0;
}

int build_ProtoRecord_TCP(ProtoRecord *pDst, ProtoRecord *pSrc, int with_body, const struct pkt_info *pkt)
{
    int str_len = 0;
    int idx = 0,i;
    uint8_t ip_version = pSrc->ip_version;
    const  uint8_t *payload = pkt->payload;
    uint32_t payload_len = pkt->payload_len;
    int  ret=PKT_DROP;
    char header[SDX_NEXT_MAX_HEADER];   //不要"={0}", CPU 时钟宝贵
    char content[SDX_NEXT_MAX_CONTENT]; //不要"={0}", CPU 时钟宝贵

    //如果没有 协议字段规则, 没有必要写入操作. 浪费CPU时钟
    if(with_body)
    {
        memset(header, 0, sizeof(header));
        memset(content, 0, sizeof(content));
    }

    pDst->proto_id            = PROTOCOL_TCP;
    pDst->direction           = pSrc->direction;
    pDst->tuple.ipversion     = ip_version;

    if (pkt->iph) {
        pDst->tuple.IpDst.ipv4   =  get_uint32_ntohl(pkt->iph->daddr,0);
        pDst->tuple.IpSrc.ipv4   =  get_uint32_ntohl(pkt->iph->saddr,0);
        pDst->tuple.protoID      =  IP_PROTO_IPV4;
    }
    else if (pkt->iph6)
    {
        memcpy(pDst->tuple.IpDst.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pDst->tuple.IpDst.ipv6));
        memcpy(pDst->tuple.IpSrc.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pDst->tuple.IpSrc.ipv6));
        pDst->tuple.protoID      = IP_PROTO_IPV6;
    }

    pDst->tuple.PortDst = ntohs(pkt->tcph->dest);
    pDst->tuple.PortSrc = ntohs(pkt->tcph->source);
    pDst->tuple.protoID = IP_PROTO_TCP;

    strncpy(pDst->proto_name,"tcp",strlen("tcp"));
    pDst->proto_name[sizeof("tcp")]='\0';

    return 0;
}

int build_ProtoRecord_UDP(ProtoRecord *pDst, ProtoRecord *pSrc, int with_body, const struct pkt_info *pkt)
{
    int str_len = 0;
    int idx = 0,i;
    uint8_t ip_version=pSrc->ip_version;
    int  ret=PKT_DROP;
    const  uint8_t *payload = pkt->payload;
    uint32_t payload_len = pkt->payload_len;
    char header[SDX_NEXT_MAX_HEADER];   //不要"={0}", CPU 时钟宝贵
    char content[SDX_NEXT_MAX_CONTENT]; //不要"={0}", CPU 时钟宝贵

    //钳位操作 -- 削峰电路
    if(payload_len*2 >= SDX_NEXT_MAX_CONTENT)
    {
        payload_len = SDX_NEXT_MAX_CONTENT/2 -1;
    }

    //钳位操作 -- 削峰电路
    int l4_len = pSrc->l4payload_start - pSrc->l4h_start;
    if(l4_len*2 >= SDX_NEXT_MAX_HEADER)
    {
        l4_len = SDX_NEXT_MAX_HEADER/2 -1;
    }

    //如果没有 协议字段规则, 没有必要写入操作. 浪费CPU时钟
    if(with_body)
    {
        memset(header, 0, sizeof(header));
        memset(content, 0, sizeof(content));
    }

    pDst->proto_id               = PROTOCOL_UDP;
    pDst->direction              = pSrc->direction;
    pDst->tuple.ipversion        = ip_version;

    if (pkt->iph){
        pDst->tuple.IpDst.ipv4   =  get_uint32_ntohl(pkt->iph->daddr,0);
        pDst->tuple.IpSrc.ipv4   =  get_uint32_ntohl(pkt->iph->saddr,0);
        pDst->tuple.protoID      = IP_PROTO_IPV4;
    } else if (pkt->iph6) {
        memcpy(pDst->tuple.IpDst.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pDst->tuple.IpDst.ipv6));
        memcpy(pDst->tuple.IpSrc.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pDst->tuple.IpSrc.ipv6));
        pDst->tuple.protoID      = IP_PROTO_IPV6;
    }

    pDst->tuple.PortDst = ntohs(pkt->udph->source);
    pDst->tuple.PortSrc = ntohs(pkt->udph->dest);
    pDst->tuple.protoID = IP_PROTO_UDP;
    strcpy(pDst->proto_name,"udp");
    return 0;
}
/////////////////////// build_ProtoRecord_ END ...

int dpi_sdt_ip_match(struct flow_info *flow, struct pkt_info *pkt, ProtoRecord *pRec, const uint8_t *payload, uint32_t payload_len)
{
    if(!pkt){
        return PKT_OK;
    }
    int ret = 0;
    struct tbl_log *tbl = NULL;

    if(payload==NULL || (int)payload_len<0 || payload_len>g_config.max_pkt_len ){
        return PKT_OK;
    }

    //ACL 多模
    SdtPrematchResult *ipff_prematch = NULL;
    for(uint32_t i = 0; i < flow->acl_result.aclHashCnt; i++)
    {
        ipff_prematch = sdtPrematch_prematch(flow->sdt_flow.pEngine,
                flow->acl_result.aclHashCode[i],
                IP_PROTO_IP, //参考B.1筛选规则语法规范 -- 注意->b项
                payload, payload_len,
                ipff_prematch); //循环操作, 预匹配 ACL 多模
    }

    if(NULL == ipff_prematch){
        return PKT_OK;
    }

    //合并 IPFF 结果 到 LINK 中去
    flow->sdt_flow.prematch = sdtEngine_matchSdtRulesMerge(flow->sdt_flow.prematch, ipff_prematch);

    /* 该流已经匹配到最大可匹配的规则数据，后续不在进行规则匹配*/
    if(flow->sdt_flow.sdt_rule_cnt>=g_config.sdt_match_max_rules)
    {
        if(!sdt_check_rule_exist_report(flow))
        {
            sdtEngine_matchSdtRulesFree(ipff_prematch);
            return PKT_DROP;
        }
    }

    // 规则体是否包含了 为IP的协议字段
    static int proto_idx = -1;  //(使用static加速)
    static int common_proto_idx = -1;  //(使用static加速)
    if (-1 == proto_idx) {
      proto_idx = pschema_get_index(dpi_pschema_get_proto("ip"));
      common_proto_idx = pschema_get_index(dpi_pschema_get_proto("common"));
    }

    int acl_hit_include_ptoto_ip       = bit_get(flow->acl_result.bitmap_proto_id, proto_idx);
    int acl_hit_include_ptoto_common   = bit_get(flow->acl_result.bitmap_proto_id, common_proto_idx);
    int acl_hit_include_rule_body      = (SMH_hint_rule_has_body    & flow->acl_result.sumInfo_matchHintFlag);
    int acl_hit_include_rule_no_body   = (SMH_hint_rule_has_no_body & flow->acl_result.sumInfo_matchHintFlag);

    // HTTP/EMAIL/SQL... 协议在此跳过
    // 空body 规则, 需要输出, 不能在这里被丢掉
    // link tcp@*:*<>*:80,8001,8004 #0x77 77 77 2e 73 68 67 6a 6a 2e 63 6f 6d;
    // link tcp@*:*<>*:80,8090,8080; tls.sni~"api.weibo";
    if (0 == acl_hit_include_ptoto_ip && acl_hit_include_ptoto_common == 0 && acl_hit_include_rule_body &&  0 == acl_hit_include_rule_no_body) {
      sdtEngine_matchSdtRulesFree(ipff_prematch);
      return PKT_OK;
    }

    if (g_config.enable_ipff_match == 0) {
        sdtEngine_matchSdtRulesFree(ipff_prematch);
        return PKT_OK;
    }

    // 内存不够直接 return
    if (dpi_tbl_create(&tbl) == NULL) {
      sdtEngine_matchSdtRulesFree(ipff_prematch);
      return PKT_DROP;
    }
    init_log_ptr_data(tbl, flow, PROTOCOL_IP);

    ProtoRecord ip;
    memset(&ip, 0, sizeof(ProtoRecord));
    // 此时 record 只有 link 和 common
    ip.record = dpi_precord_create("");
    //只有存在 规则体, 才需要准备 ProtoRecord 对象

    //将 公共 REC 转换为 IP_REC
    pkt->payload        = payload;
    pkt->payload_len    = payload_len;

    //有Body 才需要准备record, 因为空Body不会参与规则匹配运算直接就会有命中结果
    tcp_write_shared_header(ip.record, TBL_LOG_MAX_LEN, flow, pRec->direction);
    _write_ip(flow, pRec->direction, pkt, ip.record);
    build_ProtoRecord_IP(&ip, pRec, acl_hit_include_rule_body, pkt);
    sdt_acl_match_result_copy(&flow->acl_result, &ip.acl_result);

    //ACL 多模
    int loop_max = ip.acl_result.aclHashCnt;
    if (loop_max > 0) {

      for (int i = 0; i < loop_max; i++) {
        ret |= dpi_multi_mode_trans_match(flow, pkt, &ip, ipff_prematch, write_ip_log, pkt, tbl);
      }

      if (tbl->match_res_cnt > 0) {
        tbl->record = ip.record;
        ip.record = NULL;
        if (dpi_app_match_res_enqueue(tbl) != 0) {
           dpi_tbl_free(tbl);
        }
      }
    }
    dpi_tbl_free(tbl);
    if (ip.record) precord_destroy(ip.record);
    sdtEngine_matchSdtRulesFree(ipff_prematch);

    //能走到这里来, 说明前面的预匹配是成功的.
    if(PKT_DROP==ret){
        return PKT_DROP;
    }

    return PKT_OK;
}

int
dpi_sdt_udp_match(struct flow_info *flow, struct pkt_info *pkt, ProtoRecord *pRec, const  uint8_t *payload, uint32_t payload_len)
{
    int ret = 0;
    if(0 == payload_len)
    {
        return PKT_OK;
    }

    //ACL 多模
    SdtPrematchResult *ipff_prematch = NULL;
    struct tbl_log *tbl = NULL;

    for(uint32_t i = 0; i < flow->acl_result.aclHashCnt; i++)
    {
        ipff_prematch = sdtPrematch_prematch(flow->sdt_flow.pEngine,
                flow->acl_result.aclHashCode[i],
                IP_PROTO_UDP, //参考B.1筛选规则语法规范 -- 注意->b项
                payload, payload_len,
                ipff_prematch); //循环操作, 预匹配 ACL 多模
    }
    if(NULL == ipff_prematch){
        return PKT_OK;
    }

    //合并 IPFF 结果 到 LINK 中去
    flow->sdt_flow.prematch = sdtEngine_matchSdtRulesMerge(flow->sdt_flow.prematch, ipff_prematch);

    /* 该流已经匹配到最大可匹配的规则数据，后续不在进行规则匹配*/
    if(flow->sdt_flow.sdt_rule_cnt>=g_config.sdt_match_max_rules)
    {
        if(!sdt_check_rule_exist_report(flow))
        {
            sdtEngine_matchSdtRulesFree(ipff_prematch);
            return PKT_DROP;
        }
    }

    // 规则体是否包含了 为UDP的协议字段
    static int proto_idx = -1;  //(使用static加速)
    static int common_proto_idx = -1;  //(使用static加速)
    if (-1 == proto_idx) {
      proto_idx = pschema_get_index(dpi_pschema_get_proto("udp"));
      common_proto_idx = pschema_get_index(dpi_pschema_get_proto("common"));
    }


    int acl_hit_include_ptoto_udp      = bit_get(flow->acl_result.bitmap_proto_id, proto_idx);
    int acl_hit_include_ptoto_common   = bit_get(flow->acl_result.bitmap_proto_id, common_proto_idx);
    int acl_hit_include_rule_body      = (SMH_hint_rule_has_body    & flow->acl_result.sumInfo_matchHintFlag);
    int acl_hit_include_rule_no_body   = (SMH_hint_rule_has_no_body & flow->acl_result.sumInfo_matchHintFlag);

    // HTTP/EMAIL/SQL... 协议在此跳过
    // 空body 规则, 需要输出, 不能在这里被丢掉
    // link tcp@*:*<>*:80,8001,8004 #0x77 77 77 2e 73 68 67 6a 6a 2e 63 6f 6d;
    // link tcp@*:*<>*:80,8090,8080; tls.sni~"api.weibo";
    if (0 == acl_hit_include_ptoto_udp && acl_hit_include_ptoto_common == 0 && acl_hit_include_rule_body && 0 == acl_hit_include_rule_no_body) {
      sdtEngine_matchSdtRulesFree(ipff_prematch);
      return PKT_OK;
    }

    if (g_config.enable_ipff_match == 0) {
        sdtEngine_matchSdtRulesFree(ipff_prematch);
        return PKT_OK;
    }

    if (dpi_tbl_create(&tbl) == NULL) {
      sdtEngine_matchSdtRulesFree(ipff_prematch);
      return PKT_DROP;
    }
    init_log_ptr_data(tbl, flow, PROTOCOL_UDP);
    ProtoRecord udp;
    memset(&udp, 0, sizeof(ProtoRecord));
    udp.record = dpi_precord_create("");
    //将 IP_REC 合并为 TCP_REC
    pkt->payload        = payload;
    pkt->payload_len    = payload_len;
    tcp_write_shared_header(udp.record, TBL_LOG_MAX_LEN, flow, pRec->direction);
    _write_udp(flow, pRec->direction, pkt,  udp.record);
    build_ProtoRecord_UDP(&udp, pRec, acl_hit_include_rule_body, pkt);
    sdt_acl_match_result_copy(&flow->acl_result, &udp.acl_result);

    //ACL 多模
    int loop_max = udp.acl_result.aclHashCnt;
    if (loop_max > 0) {

      for (int i = 0; i < loop_max; i++) {
        ret |= dpi_multi_mode_trans_match(flow, pkt, &udp, ipff_prematch, write_udp_log, pkt, tbl);
      }

      if (tbl->match_res_cnt > 0) {
        tbl->record = udp.record;
        udp.record = NULL;
        if (dpi_app_match_res_enqueue(tbl) != 0) {
            dpi_tbl_free(tbl);
        }
      }
    }
    dpi_tbl_free(tbl);
    if (udp.record) precord_destroy(udp.record);

    sdtEngine_matchSdtRulesFree(ipff_prematch);
    //能走到这里来, 说明前面的预匹配是成功的.
    if(PKT_DROP==ret){
        return ret;
    }
    return PKT_OK;
}

#include <arpa/inet.h>
int show_flow(struct flow_info *flow, char *buff, int size)
{
    char buff_s[64];
    char buff_d[64];
    memset(buff_s, 0, sizeof(buff_s));
    memset(buff_d, 0, sizeof(buff_d));
    int af = 4 == flow->tuple.inner.ip_version ? AF_INET : AF_INET6;
    inet_ntop(af, flow->tuple.inner.ip_src, buff_s, sizeof(buff_s));
    inet_ntop(af, flow->tuple.inner.ip_dst, buff_d, sizeof(buff_d));
    unsigned short ps = ntohs(flow->tuple.inner.port_src);
    unsigned short pd = ntohs(flow->tuple.inner.port_dst);
    return snprintf(buff, size, "%u ip.addr==%s && tcp.port==%u && ip.addr==%s && tcp.port==%u", flow->tuple.inner.proto, buff_s, ps, buff_d, pd);
}

int
dpi_sdt_tcp_match(struct flow_info *flow, struct pkt_info *pkt, ProtoRecord *pRec, const  uint8_t *payload, uint32_t payload_len)
{
    if(0 == payload_len)
    {
        return PKT_OK;
    }

    //ACL 多模
    SdtPrematchResult *ipff_prematch = NULL;
    struct tbl_log *tbl = NULL;
    //MARK_DEBUG
    //char buff[64];
    //memset(buff, 0, sizeof(buff));
    //int rc = bintohex(payload, payload_len, buff, sizeof(buff)-2);

    for(uint32_t i = 0; i < flow->acl_result.aclHashCnt; i++)
    {
        ipff_prematch = sdtPrematch_prematch(flow->sdt_flow.pEngine,
                flow->acl_result.aclHashCode[i],
                IP_PROTO_TCP, //参考B.1筛选规则语法规范 -- 注意->b项
                payload, payload_len,
                ipff_prematch); //循环操作, 预匹配 ACL 多模
    }
    if(NULL == ipff_prematch){
        return PKT_OK;
    }

    //MARK_DEBUG
    //printf("%s ", buff);
    //for(uint32_t i = 0; i < flow->acl_result.aclHashCnt; i++)
    //{
    //    printf("%08X ", flow->acl_result.aclHashCode[i]);
    //}
    //char buffsession[1024];
    //memset(buffsession, 0, sizeof(buffsession));
    //show_flow(flow, buffsession, sizeof(buffsession));
    //printf("ipff_prematch_GOOD %s\n", buffsession);

    //合并 IPFF 结果 到 LINK 中去
    flow->sdt_flow.prematch = sdtEngine_matchSdtRulesMerge(flow->sdt_flow.prematch, ipff_prematch);

    /* 该流已经匹配到最大可匹配的规则数据，后续不在进行规则匹配*/
    if(flow->sdt_flow.sdt_rule_cnt>=g_config.sdt_match_max_rules)
    {
        if(!sdt_check_rule_exist_report(flow))
        {
            sdtEngine_matchSdtRulesFree(ipff_prematch);
            return PKT_DROP;
        }
    }

    // 规则体是否包含了 为TCP的协议字段
    static int proto_idx = -1;  //(使用static加速)
    static int common_proto_idx = -1;  //(使用static加速)
    if (-1 == proto_idx) {
      proto_idx = pschema_get_index(dpi_pschema_get_proto("tcp"));
      common_proto_idx = pschema_get_index(dpi_pschema_get_proto("common"));
    }

    int acl_hit_include_ptoto_tcp      = bit_get(flow->acl_result.bitmap_proto_id, proto_idx);
    int acl_hit_include_ptoto_common   = bit_get(flow->acl_result.bitmap_proto_id, common_proto_idx);
    int acl_hit_include_rule_body      = (SMH_hint_rule_has_body    & flow->acl_result.sumInfo_matchHintFlag);
    int acl_hit_include_rule_no_body   = (SMH_hint_rule_has_no_body & flow->acl_result.sumInfo_matchHintFlag);

    // HTTP/EMAIL/SQL... 协议在此跳过
    // 空body 规则, 需要输出, 不能在这里被丢掉
    // link tcp@*:*<>*:80,8001,8004 #0x77 77 77 2e 73 68 67 6a 6a 2e 63 6f 6d;
    // link tcp@*:*<>*:80,8090,8080; tls.sni~"api.weibo";
    // [2024-12 牡丹江] IPFF 6@*:*<>*:*;cable_linename1 ^ "1-24-E-R-S64"; 无法进行匹配  
    if ( (0 == acl_hit_include_ptoto_tcp && 0 == acl_hit_include_ptoto_common)
        && acl_hit_include_rule_body && 0 == acl_hit_include_rule_no_body) {
      sdtEngine_matchSdtRulesFree(ipff_prematch);
      return PKT_OK;
    }

    if (g_config.enable_ipff_match == 0) {
        sdtEngine_matchSdtRulesFree(ipff_prematch);
        return PKT_OK;
    }

    if (dpi_tbl_create(&tbl) == NULL) {
      sdtEngine_matchSdtRulesFree(ipff_prematch);
      return PKT_DROP;
    }
    init_log_ptr_data(tbl, flow, PROTOCOL_TCP);

    ProtoRecord tcp;
    memset(&tcp, 0, sizeof(ProtoRecord));
    tcp.record = dpi_precord_create("");

    //将 IP_REC 合并为 TCP_REC
    pkt->payload        = payload;
    pkt->payload_len    = payload_len;
    //有Body 才需要准备record, 因为空Body不会参与规则匹配运算直接就会有命中结果
    #ifdef DPI_SDT_ZDY
        write_common(tcp.record, TBL_LOG_MAX_LEN, flow, pRec->direction);
    #else
        tcp_write_shared_header(tcp.record, TBL_LOG_MAX_LEN, flow, pRec->direction);
    #endif
    _write_tcp(flow, pRec->direction, pkt, tcp.record);
    build_ProtoRecord_TCP(&tcp, pRec, acl_hit_include_rule_body, pkt);
    sdt_acl_match_result_copy(&flow->acl_result, &tcp.acl_result);

    tcp.pPayload.pBuff  =(uint8_t *)payload;
    tcp.pPayload.len    =payload_len;
    //ACL 多模
    int ret = PKT_OK;
    int loop_max = tcp.acl_result.aclHashCnt;
    if (loop_max > 0) {

      for (int i = 0; i < loop_max; i++) {
        ret |= dpi_multi_mode_trans_match(flow, pkt, &tcp, ipff_prematch, write_tcp_log, pkt, tbl);
      }

      if (tbl->match_res_cnt > 0) {
        #ifdef DPI_SDT_ZDY
        tbl->record = tcp.record;
        tcp.record  = NULL;
        #else
        tbl->record = dpi_precord_create("");
        write_shared_header(tbl->record, TBL_LOG_MAX_LEN, flow, pRec->direction);
        write_link(tbl->record, flow, pRec->direction);
        #endif
        if (dpi_app_match_res_enqueue(tbl) != 0) {
            dpi_tbl_free(tbl);
        }
      }
    }

    dpi_tbl_free(tbl);
    sdtEngine_matchSdtRulesFree(ipff_prematch);
    if (tcp.record) {
        precord_destroy(tcp.record);
    }

    //能走到这里来, 说明前面的预匹配是成功的.
    if(ret)
    {
        return PKT_DROP;
    }
    return PKT_OK;
}

int
dpi_sdt_sctp_match(struct flow_info *flow, struct pkt_info *pkt, ProtoRecord *pRec, const  uint8_t *payload, uint32_t payload_len)
{
    //ACL 多模
    SdtPrematchResult *ipff_prematch = NULL;
    for(uint32_t i = 0; i < flow->acl_result.aclHashCnt; i++)
    {
        ipff_prematch = sdtPrematch_prematch(flow->sdt_flow.pEngine,
                flow->acl_result.aclHashCode[i],
                IP_PROTO_SCTP, //参考B.1筛选规则语法规范 -- 注意->b项
                payload, payload_len,
                ipff_prematch); //循环操作, 预匹配 ACL 多模
    }
    if(NULL == ipff_prematch){
        return PKT_OK;
    }

    //合并 IPFF 结果 到 LINK 中去
    flow->sdt_flow.prematch = sdtEngine_matchSdtRulesMerge(flow->sdt_flow.prematch, ipff_prematch);

    //业务 ...
    // dpi_dissect_sctp() //匹配发生在这里
    //业务 ...
    // 后续实现 需关注 pRec->record 所有权问题, 防止内存泄露和double free

    sdtEngine_matchSdtRulesFree(ipff_prematch);
    //能走到这里来, 说明前面的预匹配是成功的.
    return PKT_OK;
}

static void
_sdt_init_ip_dissector(void)
{
    dpi_register_proto_schema(ip_field_array,EM_SDT_IP_MAX,"ip");
    map_fields_info_register(ip_field_array, PROTOCOL_IP, EM_SDT_IP_MAX, "ip");
    return;
}

static void
_sdt_init_udp_dissector(void)
{
    dpi_register_proto_schema(udp_field_array,EM_SDT_UDP_MAX,"udp");
    map_fields_info_register(udp_field_array, PROTOCOL_UDP, EM_SDT_UDP_MAX, "udp");
    return;
}

static void
_sdt_init_tcp_dissector(void)
{
    dpi_register_proto_schema(tcp_field_array,EM_SDT_TCP_MAX,"tcp");
    map_fields_info_register(tcp_field_array, PROTOCOL_TCP, EM_SDT_TCP_MAX, "tcp");
    return;
}

static __attribute((constructor)) void before_sdt_init_network(void){
    register_tbl_array(TBL_LOG_IP, 1, "ip", _sdt_init_ip_dissector);
    register_tbl_array(TBL_LOG_UDP, 1, "udp", _sdt_init_udp_dissector);
    register_tbl_array(TBL_LOG_TCP, 1, "tcp", _sdt_init_tcp_dissector);
}
