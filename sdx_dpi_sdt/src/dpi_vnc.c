
/****************************************************************************************
 * 文 件 名 : dpi_vnc.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: jingk            2018/11/24
编码: jingk            2018/11/24
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <yaProtoRecord/precord_schema.h>

#include "dpi_vnc.h"
#include "dpi_pschema.h"

enum vnc_index_em{
    EM_VNC_SERVER_VERSION,
    EM_VNC_CLIENT_VERSION,
    EM_VNC_SECURITY_TYPE,
    EM_VNC_SERVER_CHANLLEGE,
    EM_VNC_CLIENT_RESPONSE,
    EM_VNC_AUTHENTICATION_RESULT,
    EM_VNC_SHARE_DESKTOP_FLAG,
    EM_VNC_FRAME_BUFFER_WIDTH,
    EM_VNC_FRAME_BUFFER_HEIGHT,
    EM_VNC_BIT_PER_PIXEL,
    EM_VNC_DEPTH,
    EM_VNC_BIGENDIAN_FLAG,
    EM_VNC_TRUE_COLOR_FLAG,
    EM_VNC_RED_MAX,
    EM_VNC_GREEN_MAX,
    EM_VNC_BLUE_MAX,
    EM_VNC_RED_SHIFT,
    EM_VNC_GREEN_SHIFT,
    EM_VNC_BLUE_SHIFT,
    EM_VNC_DESKTOP_NAME_LEN,
    EM_VNC_DESKTOP_NAME,
    EM_VNC_MESSAGE_TPYE,
    EM_VNC_PIXEL_INFO,
    EM_VNC_NUM_ENCODING,
    EM_VNC_ENCODING_DATA,
    EM_VNC_INCREMENTAL_UPDATE,
    EM_VNC_X_POSITION,
    EM_VNC_Y_POSITION,
    EM_VNC_WIDTH,
    EM_VNC_HEIGHT,
    EM_VNC_KEY_EVENT_DOWN_FLAG,
    EM_VNC_KEY_VALUE,
    EM_VNC_PT_EVENT_MOUSE_BUTTON,
    EM_VNC_PT_X_POSITION,
    EM_VNC_PT_Y_POSITION,
    EM_VNC_CUT_TEXT_LEN,
    EM_VNC_TEXT_CONTENT,
    EM_VNC_NUM_RECT,
    EM_VNC_RECT_DATA,
    EM_VNC_FIRST_COLOR,
    EM_VNC_NUM_COLOR,
    EM_VNC_COLOR_DATA,

    EM_VNC_HOST,
    EM_VNC_FILE,

    EM_VNC_MAX
};

static dpi_field_table vnc_field_array[] = {
    DPI_FIELD_D(EM_VNC_SERVER_VERSION,        EM_F_TYPE_NULL, "SerVersion"),
    DPI_FIELD_D(EM_VNC_CLIENT_VERSION,        EM_F_TYPE_NULL, "CliVersion"),
    DPI_FIELD_D(EM_VNC_SECURITY_TYPE,         EM_F_TYPE_NULL, "SecurityType"),
    DPI_FIELD_D(EM_VNC_SERVER_CHANLLEGE,      EM_F_TYPE_NULL, "ServerChanllege"),
    DPI_FIELD_D(EM_VNC_CLIENT_RESPONSE,       EM_F_TYPE_NULL, "ClientResponse"),
    DPI_FIELD_D(EM_VNC_AUTHENTICATION_RESULT, EM_F_TYPE_NULL, "AuthenResult"),
    DPI_FIELD_D(EM_VNC_SHARE_DESKTOP_FLAG,    EM_F_TYPE_NULL, "ShareDesktopFlag"),
    DPI_FIELD_D(EM_VNC_FRAME_BUFFER_WIDTH,    EM_F_TYPE_NULL, "FrameBufWidth"),
    DPI_FIELD_D(EM_VNC_FRAME_BUFFER_HEIGHT,   EM_F_TYPE_NULL, "FrameBufHeight"),
    DPI_FIELD_D(EM_VNC_BIT_PER_PIXEL,         EM_F_TYPE_NULL, "BitPerPixel"),
    DPI_FIELD_D(EM_VNC_DEPTH,                 EM_F_TYPE_NULL, "Depth"),
    DPI_FIELD_D(EM_VNC_BIGENDIAN_FLAG,        EM_F_TYPE_NULL, "BigEndianFlag"),
    DPI_FIELD_D(EM_VNC_TRUE_COLOR_FLAG,       EM_F_TYPE_NULL, "TrueColorFlag"),
    DPI_FIELD_D(EM_VNC_RED_MAX,               EM_F_TYPE_NULL, "RedMax"),
    DPI_FIELD_D(EM_VNC_GREEN_MAX,             EM_F_TYPE_NULL, "GreenMax"),
    DPI_FIELD_D(EM_VNC_BLUE_MAX,              EM_F_TYPE_NULL, "BlueMax"),
    DPI_FIELD_D(EM_VNC_RED_SHIFT,             EM_F_TYPE_NULL, "RedShift"),
    DPI_FIELD_D(EM_VNC_GREEN_SHIFT,           EM_F_TYPE_NULL, "GreenShift"),
    DPI_FIELD_D(EM_VNC_BLUE_SHIFT,            EM_F_TYPE_NULL, "BlueShift"),
    DPI_FIELD_D(EM_VNC_DESKTOP_NAME_LEN,      EM_F_TYPE_NULL, "DesktopNameLen"),
    DPI_FIELD_D(EM_VNC_DESKTOP_NAME,          EM_F_TYPE_NULL, "DesktopName"),
    DPI_FIELD_D(EM_VNC_MESSAGE_TPYE,          EM_F_TYPE_NULL, "MessageType"),
    DPI_FIELD_D(EM_VNC_PIXEL_INFO,            EM_F_TYPE_NULL, "PixelInformation"),
    DPI_FIELD_D(EM_VNC_NUM_ENCODING,          EM_F_TYPE_NULL, "NumofEncoding"),
    DPI_FIELD_D(EM_VNC_ENCODING_DATA,         EM_F_TYPE_NULL, "EncodingTypeData"),
    DPI_FIELD_D(EM_VNC_INCREMENTAL_UPDATE,    EM_F_TYPE_NULL, "IncrementalUpdate"),
    DPI_FIELD_D(EM_VNC_X_POSITION,            EM_F_TYPE_NULL, "XPosition"),
    DPI_FIELD_D(EM_VNC_Y_POSITION,            EM_F_TYPE_NULL, "YPosition"),
    DPI_FIELD_D(EM_VNC_WIDTH,                 EM_F_TYPE_NULL, "Width"),
    DPI_FIELD_D(EM_VNC_HEIGHT,                EM_F_TYPE_NULL, "Height"),
    DPI_FIELD_D(EM_VNC_KEY_EVENT_DOWN_FLAG,   EM_F_TYPE_NULL, "KyEventDownFlag"),
    DPI_FIELD_D(EM_VNC_KEY_VALUE,             EM_F_TYPE_NULL, "KeyValue"),
    DPI_FIELD_D(EM_VNC_PT_EVENT_MOUSE_BUTTON, EM_F_TYPE_NULL, "PtEventMouseButton"),
    DPI_FIELD_D(EM_VNC_PT_X_POSITION,         EM_F_TYPE_NULL, "PtXPosition"),
    DPI_FIELD_D(EM_VNC_PT_Y_POSITION,         EM_F_TYPE_NULL, "PtYPosition"),
    DPI_FIELD_D(EM_VNC_CUT_TEXT_LEN,          EM_F_TYPE_NULL, "CutTextLength"),
    DPI_FIELD_D(EM_VNC_TEXT_CONTENT,          EM_F_TYPE_NULL, "TextContent"),
    DPI_FIELD_D(EM_VNC_NUM_RECT,              EM_F_TYPE_NULL, "NumofRect"),
    DPI_FIELD_D(EM_VNC_RECT_DATA,             EM_F_TYPE_NULL, "RectangleData"),
    DPI_FIELD_D(EM_VNC_FIRST_COLOR,           EM_F_TYPE_NULL, "FirstColor"),
    DPI_FIELD_D(EM_VNC_NUM_COLOR,             EM_F_TYPE_NULL, "NumofColor"),
    DPI_FIELD_D(EM_VNC_COLOR_DATA,            EM_F_TYPE_NULL, "ColorData"),
    DPI_FIELD_D(EM_VNC_HOST,                  EM_F_TYPE_NULL, "Host"),
    DPI_FIELD_D(EM_VNC_FILE,                  EM_F_TYPE_NULL, "File"),

};

static dpi_field_table vnc_field_array_sdt[] = {
    DPI_FIELD_D(EM_VNC_SERVER_VERSION,        YV_FT_BYTES, "SerVersion"),
    DPI_FIELD_D(EM_VNC_CLIENT_VERSION,        YV_FT_BYTES, "CliVersion"),
    DPI_FIELD_D(EM_VNC_SECURITY_TYPE,         YV_FT_BYTES, "SecurityType"),
    DPI_FIELD_D(EM_VNC_SERVER_CHANLLEGE,      YV_FT_BYTES, "ServerChanllege"),
    DPI_FIELD_D(EM_VNC_CLIENT_RESPONSE,       YV_FT_BYTES, "ClientResponse"),
    DPI_FIELD_D(EM_VNC_AUTHENTICATION_RESULT, YV_FT_BYTES, "AuthenResult"),
    DPI_FIELD_D(EM_VNC_SHARE_DESKTOP_FLAG,    YV_FT_BYTES, "ShareDesktopFlag"),
    DPI_FIELD_D(EM_VNC_FRAME_BUFFER_WIDTH,    YV_FT_UINT16, "FrameBufWidth"),
    DPI_FIELD_D(EM_VNC_FRAME_BUFFER_HEIGHT,   YV_FT_UINT16, "FrameBufHeight"),
    DPI_FIELD_D(EM_VNC_BIT_PER_PIXEL,         YV_FT_UINT8, "BitPerPixel"),
    DPI_FIELD_D(EM_VNC_DEPTH,                 YV_FT_UINT8, "Depth"),
    DPI_FIELD_D(EM_VNC_BIGENDIAN_FLAG,        YV_FT_UINT8, "BigEndianFlag"),
    DPI_FIELD_D(EM_VNC_TRUE_COLOR_FLAG,       YV_FT_UINT8, "TrueColorFlag"),
    DPI_FIELD_D(EM_VNC_RED_MAX,               YV_FT_UINT16, "RedMax"),
    DPI_FIELD_D(EM_VNC_GREEN_MAX,             YV_FT_UINT16, "GreenMax"),
    DPI_FIELD_D(EM_VNC_BLUE_MAX,              YV_FT_UINT16, "BlueMax"),
    DPI_FIELD_D(EM_VNC_RED_SHIFT,             YV_FT_UINT8, "RedShift"),
    DPI_FIELD_D(EM_VNC_GREEN_SHIFT,           YV_FT_UINT8, "GreenShift"),
    DPI_FIELD_D(EM_VNC_BLUE_SHIFT,            YV_FT_UINT8, "BlueShift"),
    DPI_FIELD_D(EM_VNC_DESKTOP_NAME_LEN,      YV_FT_UINT16, "DesktopNameLen"),
    DPI_FIELD_D(EM_VNC_DESKTOP_NAME,          YV_FT_BYTES, "DesktopName"),
    DPI_FIELD_D(EM_VNC_MESSAGE_TPYE,          YV_FT_UINT8, "MessageType"),
    DPI_FIELD_D(EM_VNC_PIXEL_INFO,            YV_FT_BYTES, "PixelInformation"),
    DPI_FIELD_D(EM_VNC_NUM_ENCODING,          YV_FT_BYTES, "NumofEncoding"),
    DPI_FIELD_D(EM_VNC_ENCODING_DATA,         YV_FT_BYTES, "EncodingTypeData"),
    DPI_FIELD_D(EM_VNC_INCREMENTAL_UPDATE,    YV_FT_BYTES, "IncrementalUpdate"),
    DPI_FIELD_D(EM_VNC_X_POSITION,            YV_FT_BYTES, "XPosition"),
    DPI_FIELD_D(EM_VNC_Y_POSITION,            YV_FT_BYTES, "YPosition"),
    DPI_FIELD_D(EM_VNC_WIDTH,                 YV_FT_UINT16, "Width"),
    DPI_FIELD_D(EM_VNC_HEIGHT,                YV_FT_UINT16, "Height"),
    DPI_FIELD_D(EM_VNC_KEY_EVENT_DOWN_FLAG,   YV_FT_BYTES, "KyEventDownFlag"),
    DPI_FIELD_D(EM_VNC_KEY_VALUE,             YV_FT_BYTES, "KeyValue"),
    DPI_FIELD_D(EM_VNC_PT_EVENT_MOUSE_BUTTON, YV_FT_BYTES, "PtEventMouseButton"),
    DPI_FIELD_D(EM_VNC_PT_X_POSITION,         YV_FT_BYTES, "PtXPosition"),
    DPI_FIELD_D(EM_VNC_PT_Y_POSITION,         YV_FT_BYTES, "PtYPosition"),
    DPI_FIELD_D(EM_VNC_CUT_TEXT_LEN,          YV_FT_UINT16, "CutTextLength"),
    DPI_FIELD_D(EM_VNC_TEXT_CONTENT,          YV_FT_BYTES, "TextContent"),
    DPI_FIELD_D(EM_VNC_NUM_RECT,              YV_FT_BYTES, "NumofRect"),
    DPI_FIELD_D(EM_VNC_RECT_DATA,             YV_FT_BYTES, "RectangleData"),
    DPI_FIELD_D(EM_VNC_FIRST_COLOR,           YV_FT_UINT16, "FirstColor"),
    DPI_FIELD_D(EM_VNC_NUM_COLOR,             YV_FT_UINT16, "NumofColor"),
    DPI_FIELD_D(EM_VNC_COLOR_DATA,            YV_FT_BYTES, "ColorData"),
    DPI_FIELD_D(EM_VNC_HOST,                  YV_FT_BYTES, "Host"),
    DPI_FIELD_D(EM_VNC_FILE,                  YV_FT_BYTES, "File"),

};

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;



typedef enum {
    VNC_SECURITY_TYPE_INVALID       =  0,
    VNC_SECURITY_TYPE_NONE          =  1,
    VNC_SECURITY_TYPE_VNC           =  2,
    VNC_SECURITY_TYPE_RA2           =  5,
    VNC_SECURITY_TYPE_RA2ne         =  6,
    VNC_SECURITY_TYPE_TIGHT         = 16,
    VNC_SECURITY_TYPE_ULTRA         = 17,
    VNC_SECURITY_TYPE_TLS           = 18,
    VNC_SECURITY_TYPE_VENCRYPT      = 19,
    VNC_SECURITY_TYPE_GTK_VNC_SASL  = 20,
    VNC_SECURITY_TYPE_MD5_HASH_AUTH = 21,
    VNC_SECURITY_TYPE_XVP           = 22,
    VNC_SECURITY_TYPE_ARD           = 30,
    VNC_TIGHT_AUTH_TGHT_ULGNAUTH    = 119,
    VNC_TIGHT_AUTH_TGHT_XTRNAUTH    = 130
} vnc_security_types_e;

struct ser_frame_paras
{
    uint8_t  set_flag;
    uint16_t frame_buf_width;
    uint16_t frame_buf_height;
    uint8_t  bit_per_pixel;
    uint8_t  depth;
    uint8_t  big_endian_flag;
    uint8_t  true_color_flag;
    uint16_t red_max;
    uint16_t green_max;
    uint16_t blue_max;
    uint8_t  red_shift;
    uint8_t  green_shift;
    uint8_t  blue_shift;
    uint32_t desktop_name_len;
    char     desktop_name[128];
};

typedef enum {
    /* Required */
    VNC_CLIENT_MESSAGE_TYPE_SET_PIXEL_FORMAT      =   0,
    VNC_CLIENT_MESSAGE_TYPE_SET_ENCODINGS         =   2,
    VNC_CLIENT_MESSAGE_TYPE_FRAMEBUF_UPDATE_REQ   =   3,
    VNC_CLIENT_MESSAGE_TYPE_KEY_EVENT             =   4,
    VNC_CLIENT_MESSAGE_TYPE_POINTER_EVENT         =   5,
    VNC_CLIENT_MESSAGE_TYPE_CLIENT_CUT_TEXT       =   6,
    /* Optional */
    VNC_CLIENT_MESSAGE_TYPE_MIRRORLINK                = 128,
    VNC_CLIENT_MESSAGE_TYPE_ENABLE_CONTINUOUS_UPDATES = 150,  /* TightVNC */
    VNC_CLIENT_MESSAGE_TYPE_FENCE                     = 248,  /* TigerVNC */
    VNC_CLIENT_MESSAGE_TYPE_XVP                       = 250,
    VNC_CLIENT_MESSAGE_TYPE_SETR_DESKTOP_SIZE         = 251,
    VNC_CLIENT_MESSAGE_TYPE_TIGHT                     = 252,
    VNC_CLIENT_MESSAGE_TYPE_GII                       = 253,
    VNC_CLIENT_MESSAGE_TYPE_QEMU                      = 255
} vnc_client_message_types_e;

typedef enum {
    VNC_SERVER_MESSAGE_TYPE_FRAMEBUFFER_UPDATE        =   0,
    VNC_SERVER_MESSAGE_TYPE_SET_COLORMAP_ENTRIES      =   1,
    VNC_SERVER_MESSAGE_TYPE_RING_BELL                 =   2,
    VNC_SERVER_MESSAGE_TYPE_CUT_TEXT                  =   3,
    VNC_SERVER_MESSAGE_TYPE_MIRRORLINK                = 128,
    VNC_SERVER_MESSAGE_TYPE_END_CONTINUOUS_UPDATES    = 150,  /* TightVNC */
    VNC_SERVER_MESSAGE_TYPE_FENCE                     = 248,  /* TigerVNC */
    VNC_SERVER_MESSAGE_TYPE_XVP                       = 250,
    VNC_SERVER_MESSAGE_TYPE_TIGHT                     = 252,
    VNC_SERVER_MESSAGE_TYPE_GII                       = 253,
    VNC_SERVER_MESSAGE_TYPE_QEMU                      = 255
} vnc_server_message_types_e;

typedef enum {
    VNC_SESSION_STATE_SERVER_VERSION,
    VNC_SESSION_STATE_CLIENT_VERSION,

    VNC_SESSION_STATE_SECURITY,
    VNC_SESSION_STATE_SECURITY_TYPES,

    VNC_SESSION_STATE_TIGHT_TUNNELING_CAPABILITIES,
    VNC_SESSION_STATE_TIGHT_TUNNEL_TYPE_REPLY,
    VNC_SESSION_STATE_TIGHT_AUTH_CAPABILITIES,
    VNC_SESSION_STATE_TIGHT_AUTH_TYPE_REPLY,
    VNC_SESSION_STATE_TIGHT_UNKNOWN_PACKET3,

    VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE,
    VNC_SESSION_STATE_VNC_AUTHENTICATION_RESPONSE,

    VNC_SESSION_STATE_ARD_AUTHENTICATION_CHALLENGE,
    VNC_SESSION_STATE_ARD_AUTHENTICATION_RESPONSE,

    VNC_SESSION_STATE_SECURITY_RESULT,

    VNC_SESSION_STATE_CLIENT_INIT,
    VNC_SESSION_STATE_SERVER_INIT,

    VNC_SESSION_STATE_TIGHT_INTERACTION_CAPS,

    VNC_SESSION_STATE_NORMAL_TRAFFIC
} vnc_session_state_e;

struct vnc_session
{
    char      server_version[VNC_VERSION_LEN + 1];
    char      client_version[VNC_VERSION_LEN + 1];
    uint8_t   security_types[16];
    uint8_t   security_types_num;
    uint8_t   security_type_selected;
    uint8_t   server_challege_flag;
    uint8_t   client_challege_flag;
    uint8_t   auth_from_server[16];
    uint8_t   auth_from_client[16];
    const char* share_flag;
    const char* auth_result;
    struct    ser_frame_paras sfp;
    vnc_session_state_e vnc_next_state;
};


int is_client_or_server_version(const uint8_t* payload, const uint32_t payload_len)
{
    if( (payload_len == 12) && (payload[7] == 0x2e) && (payload[11] == 0x0a) && (strncasecmp((const char*)payload, "RFB" ,3) == 0) )
        return true;

    return false;
}

uint8_t  check_security_types(uint8_t* types, const uint8_t* payload, const uint32_t payload_len)
{
    uint8_t tmp = payload[0];
    if(tmp == (payload_len - 1)){
      uint8_t i = 0;
      for(; i < tmp; i++){
        types[i] = payload[i + 1];
      }
      return tmp;
    }
    return 0;
}

static void set_next_state_by_security_type(struct vnc_session* session, const uint8_t* payload, const uint32_t payload_len)
{
    uint8_t tmp = payload[0];
    if(payload_len == 1){
        switch(tmp){
        case VNC_SECURITY_TYPE_INVALID:
            session->security_type_selected = VNC_SECURITY_TYPE_INVALID;
            session->vnc_next_state = VNC_SESSION_STATE_SECURITY_TYPES;
            break;

        case VNC_SECURITY_TYPE_NONE:// need to add version control
           session->security_type_selected = VNC_SECURITY_TYPE_NONE;
           session->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;
           break;

        case VNC_SECURITY_TYPE_VNC:
           session->security_type_selected = VNC_SECURITY_TYPE_VNC;
           session->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE;
           break;

        case VNC_SECURITY_TYPE_ARD:
           session->security_type_selected = VNC_SECURITY_TYPE_ARD;
           session->vnc_next_state = VNC_SESSION_STATE_ARD_AUTHENTICATION_CHALLENGE;
           break;

        case VNC_SECURITY_TYPE_RA2:
        case VNC_SECURITY_TYPE_RA2ne:
        case VNC_SECURITY_TYPE_TIGHT:
        case VNC_SECURITY_TYPE_ULTRA:
        case VNC_SECURITY_TYPE_TLS:
        case VNC_SECURITY_TYPE_VENCRYPT:
        case VNC_SECURITY_TYPE_GTK_VNC_SASL:
        case VNC_SECURITY_TYPE_MD5_HASH_AUTH:
        case VNC_SECURITY_TYPE_XVP:
        case VNC_TIGHT_AUTH_TGHT_ULGNAUTH:
        case VNC_TIGHT_AUTH_TGHT_XTRNAUTH:
        default:
            break;
        }
    }
}

static int set_frame_parameters(struct ser_frame_paras* para, const uint8_t* payload, const uint32_t len)
{
    if(len <= 20)
        return false;
    para->set_flag = 1;
    para->frame_buf_width = ntohs(*(const uint16_t*)payload);
    para->frame_buf_height = ntohs(*(const uint16_t*)(payload + 2));
    para->bit_per_pixel = payload[4];
    para->depth = payload[5];
    para->big_endian_flag = payload[6];
    para->true_color_flag = payload[7];
    para->red_max   = ntohs(*((const uint16_t*)(payload + 8)));
    para->green_max = ntohs(*((const uint16_t*)(payload + 10)));
    para->blue_max  = ntohs(*((const uint16_t*)(payload + 12)));
    para->red_shift = payload[14];
    para->green_shift = payload[15];
    para->blue_shift = payload[16];
    para->desktop_name_len = ntohl(*(const uint32_t*)(payload + 20));

    if(para->desktop_name_len > 0){
            rte_memcpy(para->desktop_name, payload + 24, DPI_MIN(para->desktop_name_len, 128));
    }

    return true;
}

uint8_t get_server_message_type(const uint8_t* payload, uint32_t offset)
{
    return payload[offset];
}

uint8_t get_client_message_type(const uint8_t* payload)
{
    return payload[0];
}

static void write_server_frame_parameters_log(precord_t *record, int* idx, struct ser_frame_paras* para)
{
    struct ser_frame_paras* tmp_para = para;

    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->frame_buf_width);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->frame_buf_height);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->bit_per_pixel);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->depth);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->big_endian_flag);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->true_color_flag);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->red_max);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->green_max);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->blue_max);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->red_shift);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->green_shift);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->blue_shift);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->desktop_name_len);

    if(tmp_para->desktop_name_len == 0)
    {
        write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
    }
    else
    {
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, tmp_para->desktop_name, tmp_para->desktop_name_len);
    }
}


static int write_vnc_log(struct flow_info *flow, int direction)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;

    struct vnc_session* session = (struct vnc_session*)flow->app_session;
    if(!session)
        return 0;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "vnc");

    int local_idx=idx;
    for(i=0;i<EM_VNC_MAX;i++){
        switch(i){
        case EM_VNC_SERVER_VERSION:
            write_one_str_reconds(log_ptr->record
                                 , &idx
                                 , TBL_LOG_MAX_LEN
                                 , session->server_version
                                 , (uint32_t)strlen((char *)session->server_version));
            break;
        case EM_VNC_CLIENT_VERSION:
            write_one_str_reconds(log_ptr->record
                                 , &idx
                                 , TBL_LOG_MAX_LEN
                                 , session->client_version
                                 , (uint32_t)strlen((char *)session->client_version));
            break;
        case EM_VNC_SECURITY_TYPE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)session->security_types, (uint32_t)strlen((char *)session->security_types));
            break;
        case EM_VNC_SERVER_CHANLLEGE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)session->auth_from_server, (uint32_t)strlen((char *)session->auth_from_server));
            break;
        case EM_VNC_CLIENT_RESPONSE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)session->auth_from_client, (uint32_t)strlen((char *)session->auth_from_client));
            break;
        case EM_VNC_AUTHENTICATION_RESULT:
            write_one_str_reconds(log_ptr->record
                                 , &idx
                                 , TBL_LOG_MAX_LEN
                                 , session->auth_result, session->auth_result ? strlen(session->auth_result) : 0);
            break;
        case EM_VNC_SHARE_DESKTOP_FLAG:
            write_one_str_reconds(log_ptr->record
                                 , &idx
                                 , TBL_LOG_MAX_LEN
                                 , session->share_flag, session->share_flag ? strlen(session->share_flag) : 0);
            break;
        case EM_VNC_FRAME_BUFFER_WIDTH:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.frame_buf_width);
            break;



        case EM_VNC_FRAME_BUFFER_HEIGHT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.frame_buf_height);
            break;
        case EM_VNC_BIT_PER_PIXEL:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.bit_per_pixel);
            break;
        case EM_VNC_DEPTH:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.depth);
            break;
        case EM_VNC_BIGENDIAN_FLAG:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.big_endian_flag);
            break;
        case EM_VNC_TRUE_COLOR_FLAG:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.true_color_flag);
            break;
        case EM_VNC_RED_MAX:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.red_max);
            break;



        case EM_VNC_GREEN_MAX:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.green_max);
            break;
        case EM_VNC_BLUE_MAX:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.blue_max);
            break;
        case EM_VNC_RED_SHIFT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.red_shift);
            break;
        case EM_VNC_GREEN_SHIFT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.green_shift);
            break;
        case EM_VNC_BLUE_SHIFT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.blue_shift);
            break;
        case EM_VNC_DESKTOP_NAME_LEN:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.desktop_name_len);
            break;
        case EM_VNC_DESKTOP_NAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, session->sfp.desktop_name, session->sfp.desktop_name_len);
            break;

        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_VNC;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
        log_ptr=NULL;
    }
    return 1;

}



static void write_vnc_session_log(struct tbl_log* log_ptr, struct vnc_session* session, int* idx)
{
   /** write vnc session */
    struct tbl_log* plog = log_ptr;

    write_one_str_reconds(plog->record
                         , idx
                         , TBL_LOG_MAX_LEN
                         , session->server_version
                         , sizeof(session->server_version) - 1);

    write_one_str_reconds(plog->record
                         , idx
                         , TBL_LOG_MAX_LEN
                         , session->client_version
                         , sizeof(session->client_version) - 1);

    write_one_num_reconds(plog->record, idx, TBL_LOG_MAX_LEN
                         , session->security_type_selected);

    write_multi_num_reconds(plog->record
                           , idx
                           , TBL_LOG_MAX_LEN
                           , session->auth_from_server
                           , sizeof(session->auth_from_server));

    write_multi_num_reconds(plog->record
                           , idx
                           , TBL_LOG_MAX_LEN
                           , session->auth_from_client
                           , sizeof(session->auth_from_client));

    write_one_str_reconds(plog->record
                         , idx
                         , TBL_LOG_MAX_LEN
                         , session->auth_result, session->auth_result ? strlen(session->auth_result) : 0);

    write_one_str_reconds(plog->record
                         , idx
                         , TBL_LOG_MAX_LEN
                         , session->share_flag, session->share_flag ? strlen(session->share_flag) : 0);

    write_server_frame_parameters_log(plog->record
                                     , idx
                                     , &session->sfp);

}


void write_vnc_pixel_information_log(struct tbl_log* log_ptr, int* idx
                                    , const uint8_t* payload, const uint32_t payload_len)
{
    uint32_t tmp = payload_len;
    struct tbl_log* plog = log_ptr;
    char content[512];
    memset(content, 0, sizeof(content));

    if(tmp == 20){
        write_one_num_reconds(plog->record
                             , idx
                             , TBL_LOG_MAX_LEN
                             , payload[0]);

        uint16_t red_max   = ntohs(*(const uint16_t*)(payload + 8));
        uint16_t green_max = ntohs(*(const uint16_t*)(payload + 10));
        uint16_t blue_max  = ntohs(*(const uint16_t*)(payload + 12));

        /** write pixel information */
        snprintf(content, sizeof(content), "Bits per pixel:%d, Depth:%d, Big endian flag:%d,"
                "True color flag:%d Red maxmum:%d, Green maximum:%d, Blue maximum:%d,"
                "Red shift:%d, Green shift:%d, Blue shift:%d", payload[4], payload[5], payload[6], payload[7],
        red_max, green_max, blue_max, payload[14], payload[15], payload[16]);

        write_one_str_reconds(plog->record, idx, TBL_LOG_MAX_LEN, content, strnlen(content, 512));
        write_n_empty_reconds(plog->record, idx, TBL_LOG_MAX_LEN, 21);
    }else{
        write_n_empty_reconds(plog->record, idx, TBL_LOG_MAX_LEN, 23);
    }
}

void write_tail_info_log(struct flow_info* flow, struct tbl_log* log_ptr, int idx)
{
    struct tbl_log* plog = log_ptr;
    plog->thread_id = flow->thread_id;
    plog->log_type = TBL_LOG_VNC;
    plog->log_len = idx;
    plog->content_len = 0;
    plog->content_ptr = NULL;

    if(write_tbl_log(plog) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
    return;
}

static void update_common_session_info(struct flow_info* flow, const uint8_t* payload, const uint32_t payload_len)
{
    struct vnc_session* session = (struct vnc_session*)flow->app_session;

    if(payload_len == 20){
        uint16_t red_max   = ntohs(*(const uint16_t*)(payload + 8));
        uint16_t green_max = ntohs(*(const uint16_t*)(payload + 10));
        uint16_t blue_max  = ntohs(*(const uint16_t*)(payload + 12));

        session->sfp.bit_per_pixel = payload[4];
        session->sfp.depth = payload[5];
        session->sfp.big_endian_flag = payload[6];
        session->sfp.true_color_flag = payload[7];
        session->sfp.red_max = red_max;
        session->sfp.green_max = green_max;
        session->sfp.blue_max = blue_max;
        session->sfp.red_shift = payload[14];
        session->sfp.green_shift = payload[15];
        session->sfp.blue_shift = payload[16];
    }
}

void vnc_client_set_pixel_format(struct flow_info* flow
                                , int direction
                                , const uint8_t* payload
                                , const uint32_t payload_len)
{
    int idx = 0;
    struct tbl_log* log_ptr;

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    update_common_session_info(flow, payload, payload_len);
    /** write common information */
    write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);

    /** write pixel formation */
    write_vnc_pixel_information_log(log_ptr, &idx, payload, payload_len);

    /** write tail information */
    write_tail_info_log(flow,log_ptr, idx);
}

void write_common_session_info(struct tbl_log* log_ptr
                              , struct flow_info* flow
                              , const uint8_t* payload
                              , const uint32_t payload_len
                              , int direction, int* idx)
{
    write_tbl_log_common(flow, direction, log_ptr, idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "vnc");

    write_vnc_session_log(log_ptr, (struct vnc_session*)flow->app_session, idx);
}

void vnc_client_set_encodings(struct flow_info* flow
                             , int direction
                             , const uint8_t* payload
                             , const uint32_t payload_len)
{
    int idx = 0;
    uint16_t num;
    struct tbl_log* log_ptr;

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
     }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    if(payload_len == 52){
        /** write common information */
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);

        write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload, 1);
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

        num = ntohs(*(const uint16_t*)(payload + 2));
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, num);
        write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload + 4, num * 4);

        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 19);

        /** write tail information */
        write_tail_info_log(flow, log_ptr, idx);
    }
}

void vnc_client_framebuffer_update_request(struct flow_info* flow
                                          , int direction
                                          , const uint8_t* payload
                                          , const uint32_t payload_len)
{
   int idx = 0;
   struct tbl_log* log_ptr;
   uint16_t tmp[4];

   if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
   dpi_precord_new_record(log_ptr->record, NULL, NULL);
   if(payload_len == 10){
        /** write common information */
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);

        memset(tmp, 0, sizeof(tmp));
        rte_memcpy(tmp, payload + 2, sizeof(tmp));
        write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload, 1);

        /** skip encoding fields */
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 3);

        write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload + 1, 1);
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ntohl(tmp[0]));
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ntohl(tmp[1]));
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ntohl(tmp[2]));
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ntohl(tmp[3]));

        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 14);

        /** write tail information */
        write_tail_info_log(flow, log_ptr, idx);
    }
}

static void write_client_key_log(precord_t *record, const uint8_t* payload, int* idx)
{
    const uint32_t* key = (const uint32_t*)(payload + 4);
    write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN, payload, 1);
    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 8);
    write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN, payload + 1, 1);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, ntohl(*key));
    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 12);
}

void vnc_client_key_event(struct flow_info* flow
                         , int direction
                         , const uint8_t* payload
                         , const uint32_t payload_len)
{
    int idx = 0;
    struct tbl_log* log_ptr;

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    if(payload_len == 8){
        /** write common information */
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);
        write_client_key_log(log_ptr->record, payload, &idx);
        write_tail_info_log(flow, log_ptr, idx);
    }

}

void write_client_pointer_log(precord_t *record, const uint8_t* payload, int* idx)
{
    uint16_t xpos = ntohs(*(const uint16_t*)(payload + 2));
    uint16_t ypos = ntohs(*(const uint16_t*)(payload + 4));

    write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN, payload, 1);
    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 10);
    write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN, payload + 1, 1);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, xpos);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, ypos);
    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 9);
}

void vnc_client_pointer_event(struct flow_info* flow
                             , int direction
                             , const uint8_t* payload
                             , const uint32_t payload_len)
{
    int idx = 0;
    struct tbl_log* log_ptr;

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
     DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
         return ;
    }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    if(payload_len == 6){
         write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);
         write_client_pointer_log(log_ptr->record, payload, &idx);
         write_tail_info_log(flow, log_ptr, idx);
     }

}

static void write_client_cut_text_log(precord_t *record, const uint8_t* payload, int* idx)
{
    uint32_t len = ntohl(*(const uint32_t*)(payload + 4));

    write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN, payload, 1);
    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 13);
    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, len);
    write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char*)(payload + 8), len);
    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 7);
}

void vnc_client_cut_text(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len)
{
    int idx = 0;
    struct tbl_log* log_ptr;
    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
     DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
         return ;
    }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);

    if(payload_len >= 8){
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);
        write_client_cut_text_log(log_ptr->record, payload, &idx);
        write_tail_info_log(flow, log_ptr, idx);
    }
}


void vnc_client_to_server(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len)
{
    uint32_t offset = 0;
    uint8_t  message_type = 254;

    if(payload_len > 1){
        message_type = get_client_message_type(payload);
    }

    switch(message_type) {

    case VNC_CLIENT_MESSAGE_TYPE_SET_PIXEL_FORMAT :
        vnc_client_set_pixel_format(flow, direction, payload, payload_len);
        break;

    case VNC_CLIENT_MESSAGE_TYPE_SET_ENCODINGS :
        vnc_client_set_encodings(flow, direction, payload, payload_len);
        break;

    case VNC_CLIENT_MESSAGE_TYPE_FRAMEBUF_UPDATE_REQ :
        vnc_client_framebuffer_update_request(flow, direction, payload, payload_len);
        break;

    case VNC_CLIENT_MESSAGE_TYPE_KEY_EVENT :
        vnc_client_key_event(flow, direction, payload, payload_len);
        break;

    case VNC_CLIENT_MESSAGE_TYPE_POINTER_EVENT:
        vnc_client_pointer_event(flow, direction, payload, payload_len);
        break;

    case VNC_CLIENT_MESSAGE_TYPE_CLIENT_CUT_TEXT :
        vnc_client_cut_text(flow, direction, payload, payload_len);
        break;

    case VNC_CLIENT_MESSAGE_TYPE_MIRRORLINK :
    case VNC_CLIENT_MESSAGE_TYPE_ENABLE_CONTINUOUS_UPDATES :
    case VNC_CLIENT_MESSAGE_TYPE_FENCE :
    default :
        break;
    }
}

void vnc_server_framebuffer_update(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint32_t* offset)
{
    int idx = 0;
    struct tbl_log* log_ptr;
    uint32_t pos = 0;
    char data[64];

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    if(payload_len >= *offset){
        pos = *offset;
        uint16_t tmp = ntohs(*(const uint16_t*)(payload + pos + 2));
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);
        // write message type
        write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload + pos, 1);
        pos += 4;

        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 15);

        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tmp);
        if(pos + 8 <= payload_len){
            snprintf(data, 64, "X:%u, Y:%u, Width:%u, Height:%u", get_uint16_ntohs(payload, pos), get_uint16_ntohs(payload, pos+2), get_uint16_ntohs(payload, pos+4), get_uint16_ntohs(payload, pos+6));
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, data, strlen(data));
        }
        else{
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        }
        *offset = pos + tmp * 12;

        /** write n empty recond */
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 5);

        write_tail_info_log(flow, log_ptr, idx);
    }

}

void vnc_server_set_colormap_entries(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint32_t* offset)
{
    int idx = 0;
    struct tbl_log* log_ptr;
    uint32_t pos = 0;

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    if(payload_len >= *offset){
        pos = *offset;
        uint16_t first_color = ntohs(*(const uint16_t*)(payload + pos + 2));
        uint16_t num = ntohs(*(const uint16_t*)(payload + pos + 4));
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);

        write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (payload + pos), 1);
        ++pos;

        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 17);

        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, first_color);
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, num);
        pos += 5;
        if((pos + (num * 6)) <= payload_len)
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload + pos, num * 6);
        else
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        pos += (num * 6);

        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 2);

        write_tail_info_log(flow, log_ptr, idx);
        *offset = pos;
    }
}


void vnc_server_ring_bell(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint32_t* offset)
{
    int idx = 0;
    struct tbl_log* log_ptr;

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    if(payload_len >= 1){
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload[*offset]);
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 22);
        write_tail_info_log(flow, log_ptr, idx);
        ++(*offset);
    }
}

void vnc_server_cut_text(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t  payload_len, uint32_t* offset)
{
    int idx = 0;
    struct tbl_log* log_ptr;
    int pos = 0;

    if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    if(payload_len >= 8){
        pos = *offset;
        uint32_t tmp = ntohl(*(const uint32_t*)(payload + pos + 4));
        write_common_session_info(log_ptr, flow, payload, payload_len, direction, &idx);
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, payload[pos]);
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 13);
        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, tmp);

        // update offset
        pos += 8;

        // write content;
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const char*)(payload + pos), tmp);
        pos += tmp;

        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 7);
        write_tail_info_log(flow, log_ptr, idx);

        *offset = pos;
   }
}



void vnc_server_to_client(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len)
{
    uint32_t offset = 0;
    uint8_t  message_type = 254;

again:
    message_type =  get_server_message_type(payload, offset);

    switch(message_type) {

    case VNC_SERVER_MESSAGE_TYPE_FRAMEBUFFER_UPDATE :
        vnc_server_framebuffer_update(flow, direction, payload, payload_len, &offset);
        break;

    case VNC_SERVER_MESSAGE_TYPE_SET_COLORMAP_ENTRIES :
        vnc_server_set_colormap_entries(flow, direction, payload, payload_len, &offset);
        break;

    case VNC_SERVER_MESSAGE_TYPE_RING_BELL :
        vnc_server_ring_bell(flow, direction, payload, payload_len, &offset);
        break;

    case VNC_SERVER_MESSAGE_TYPE_CUT_TEXT :
        vnc_server_cut_text(flow, direction, payload, payload_len, &offset);
        break;

    case VNC_SERVER_MESSAGE_TYPE_MIRRORLINK :
    case VNC_SERVER_MESSAGE_TYPE_END_CONTINUOUS_UPDATES :
    case VNC_SERVER_MESSAGE_TYPE_FENCE :
    default :
        offset = payload_len;
        break;
    }

    if (offset < payload_len) {
        goto again;
    }
}

void handle_client_or_server_message(struct flow_info* flow, const uint8_t* payload, const uint32_t payload_len, int direction)
{

    if(payload_len > 0 && direction == FLOW_DIR_SRC2DST){
        vnc_client_to_server(flow, direction, payload, payload_len);
    }
    else{
        vnc_server_to_client(flow, direction, payload, payload_len);
    }
}

static int dissect_vnc_tcp(struct flow_info *flow
                    , int direction
                    , uint32_t seq
                    , const uint8_t *payload
                    , const uint32_t payload_len
                    , uint8_t flag)
{
    int retval;
    struct vnc_session* session;

    if(flow->app_session == NULL){
        flow->app_session = dpi_malloc(sizeof(struct vnc_session));

        if(flow->app_session == NULL){
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }

        memset(flow->app_session, 0, sizeof(struct vnc_session));
        session =  (struct vnc_session*)(flow->app_session);
        session->vnc_next_state = VNC_SESSION_STATE_SERVER_VERSION;
    }
    else{
        session = (struct vnc_session*)flow->app_session;
    }

    switch(session->vnc_next_state){

        case VNC_SESSION_STATE_SERVER_VERSION: // S2C
            if(is_client_or_server_version(payload, payload_len)){
                rte_memcpy(session->server_version, payload + sizeof(VNC_VERSION_PREFIX) - 1, VNC_VERSION_LEN);
                session->vnc_next_state = VNC_SESSION_STATE_CLIENT_VERSION;
            }
            return PKT_OK;

        case VNC_SESSION_STATE_CLIENT_VERSION: // C2S
            if(is_client_or_server_version(payload, payload_len)){
                rte_memcpy(session->client_version, payload + sizeof(VNC_VERSION_PREFIX) - 1, VNC_VERSION_LEN);
                session->vnc_next_state = VNC_SESSION_STATE_SECURITY;
                write_vnc_log(flow, direction);
            }
            return PKT_OK;

        case VNC_SESSION_STATE_SECURITY: // S2C
            if((session->security_types_num = check_security_types(session->security_types, payload, payload_len))){
                session->vnc_next_state = VNC_SESSION_STATE_SECURITY_TYPES;
            }
            return PKT_OK;

        case VNC_SESSION_STATE_SECURITY_TYPES: // C2S
            set_next_state_by_security_type(session, payload, payload_len);
            write_vnc_log(flow, direction);
            return PKT_OK;

        case VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE: // S2C
            if((payload_len == 16) && (direction == FLOW_DIR_DST2SRC)){
                rte_memcpy(session->auth_from_server, payload, sizeof(session->auth_from_server));
                session->server_challege_flag = 1;
                session->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_RESPONSE;
            }
            return PKT_OK;

        case VNC_SESSION_STATE_VNC_AUTHENTICATION_RESPONSE: // C2S
            if((payload_len == 16) && (direction == FLOW_DIR_SRC2DST)){
                rte_memcpy(session->auth_from_client, payload, sizeof(session->auth_from_client));
                session->client_challege_flag = 1;
                session->vnc_next_state = VNC_SESSION_STATE_SECURITY_RESULT;
            }
            return PKT_OK;

        case VNC_SESSION_STATE_ARD_AUTHENTICATION_CHALLENGE :// S2C
            if(direction == FLOW_DIR_DST2SRC){
                //reserved
                session->vnc_next_state = VNC_SESSION_STATE_ARD_AUTHENTICATION_RESPONSE;
            }
            return PKT_OK;
        case VNC_SESSION_STATE_ARD_AUTHENTICATION_RESPONSE : // C2S
            if(direction == FLOW_DIR_SRC2DST){
                //reserved
                session->vnc_next_state = VNC_SESSION_STATE_SECURITY_RESULT;
            }
            return PKT_OK;

        case VNC_SESSION_STATE_SECURITY_RESULT: // S2C
            if((payload_len == 4) && (direction == FLOW_DIR_DST2SRC)){
                session->auth_result= (payload[3] & 0x01) ? "FAIL" : "OK" ;
                session->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;
                write_vnc_log(flow, direction);
            }
            return PKT_OK;

        case VNC_SESSION_STATE_CLIENT_INIT: // C2S
            if((payload_len == 1) && (direction == FLOW_DIR_SRC2DST)){
                session->share_flag = payload[0] ? "True" : "False" ;
                session->vnc_next_state = VNC_SESSION_STATE_SERVER_INIT;
            }
            return PKT_OK;

        case VNC_SESSION_STATE_SERVER_INIT: // S2C
            if(direction == FLOW_DIR_DST2SRC){
                if(set_frame_parameters(&(session->sfp), payload, payload_len)){
                    session->vnc_next_state = VNC_SESSION_STATE_NORMAL_TRAFFIC;
                    write_vnc_log(flow, direction);
                }
            }
            return PKT_OK;

        case VNC_SESSION_STATE_NORMAL_TRAFFIC:
            handle_client_or_server_message(flow, payload, payload_len, direction);
            break;

        default:
            break;
    }

    return PKT_OK;
}

static void identify_vnc_tcp(struct flow_info *flow
                     , const uint8_t *payload
                     , const uint16_t payload_len)
{
    if(g_config.protocol_switch[PROTOCOL_VNC] == 0)
        return;

    if(is_client_or_server_version(payload, payload_len)){
        flow->real_protocol_id = PROTOCOL_VNC;
    }
}


static void init_vnc_dissector(void)
{
    dpi_register_proto_schema(vnc_field_array_sdt, EM_VNC_MAX, "vnc");

    port_add_proto_head(IPPROTO_TCP, TCP_PORT_VNC,  PROTOCOL_VNC);

    tcp_detection_array[PROTOCOL_VNC].identify_type = DPI_IDENTIFY_CONTENT;
    tcp_detection_array[PROTOCOL_VNC].proto = PROTOCOL_VNC;
    tcp_detection_array[PROTOCOL_VNC].identify_func = identify_vnc_tcp;
    tcp_detection_array[PROTOCOL_VNC].dissect_func  = dissect_vnc_tcp;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_VNC].excluded_protocol_bitmask, PROTOCOL_VNC);


    map_fields_info_register(vnc_field_array_sdt,PROTOCOL_VNC, EM_VNC_MAX,"vnc");

    pschema_t *schema = dpi_pschema_get_proto("vnc");
    pschema_register_field(schema, "Path", YA_FT_STRING, "desc");
    pschema_register_field(schema, "ClientApp", YA_FT_STRING, "desc");
    pschema_register_field(schema, "ClientDomain", YA_FT_STRING, "desc");

    return ;
}


static __attribute((constructor)) void before_init_vnc(void){
    register_tbl_array(TBL_LOG_VNC, 0, "vnc", init_vnc_dissector);
}
