/****************************************************************************************
 * 文 件 名 : dpi_forward_data_collect.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: hongll           2022/08/12
编码: hongll           2022/08/12
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <rte_malloc.h>
#include <rte_prefetch.h>
#include <rte_lcore.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <rte_atomic.h>

#include "dpi_forward_kafka.h"
#include "dpi_forward_eth.h"
#include "dpi_log.h"
#include "dpi_detect.h"
#include "sdt_action_out.h"
#include "sdx_rdkafka_producer_consumer.h"


extern struct global_config g_config;
extern YV_kafka_handle_t *g_kafka_handle;

extern rte_atomic64_t sdt_pcap_out_forward_pkts;
extern rte_atomic64_t sdt_pcap_out_forward_ok_pkts;
extern rte_atomic64_t sdt_pcap_out_forward_fail_pkts;

void forward_collect_send_kafka(sdt_out_status  *out_elem, struct packet_stream  *pkt_elm, const char *filename)
{
    if(out_elem == NULL && pkt_elm == NULL)
	    return;

	const struct mac_packet_header *ethdr = (const struct mac_packet_header *)pkt_elm->pkt_data;
	struct collect_field_info info;

    memset(&info, 0, sizeof(struct collect_field_info));
	info.DBEGINTIME			= (uint64_t)out_elem->pcap_status_ptr->pcap_time;
    strncpy((char*)info.NGROUPNO, out_elem->match_result->groupID, sizeof(info.NGROUPNO));

    if(*out_elem->match_result->groupName)
    	memcpy(info.SGROUPNAME, (uint8_t *)out_elem->match_result->groupName, strlen(out_elem->match_result->groupName));

    if(1==g_config.sdx_config.sdx_mac_packet_header_flag){
	    memcpy((uint8_t*)&info.LINENO1, ethdr->Datasrc.Global_LineNO, 4);
	    memcpy((uint8_t*)&info.LINENO2, ethdr->Datasrc.Global_LineNO+4, 4);
	    memcpy((uint8_t*)&info.LINENO3, ethdr->Datasrc.Global_LineNO+8, 4);
	    memcpy((uint8_t*)&info.LINENO4, ethdr->Datasrc.Global_LineNO+12, 4);
    }

    //sLineName
	snprintf((char*)info.TASK_ID, sizeof(info.TASK_ID) - 1, "%s", out_elem->match_result->taskID);
    //SSYSFROM
    info.NFILELENGTH = out_elem->pcap_status_ptr->pkt_bytes;
    memcpy(info.SFILEPATH, filename, sizeof(info.SFILEPATH)-1);
    memcpy(info.DATAFROM, g_config.data_from, sizeof(info.DATAFROM) - 1);
    info.SIGTYPE = g_config.sigtype;

    if (g_config.sdt_send_kafka) {
        DPMHeader dmpheader;
        PAYLOAD_HEADER payload_header;
        memset(&dmpheader, 0, sizeof(DPMHeader));
        memset(&payload_header, 0, sizeof(PAYLOAD_HEADER));
        kafka_producer_send_message(g_kafka_handle, &dmpheader, &payload_header, (const uint8_t*)&info, sizeof(struct collect_field_info));
    }

    return;
}


