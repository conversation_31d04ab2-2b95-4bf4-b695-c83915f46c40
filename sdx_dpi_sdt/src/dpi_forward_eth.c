/****************************************************************************************
 * �� �� �� : dpi_forward.c
 * ��Ŀ���� :
 * ģ �� �� :
 * ��    �� :
 * ����ϵͳ : LINUX
 * �޸ļ�¼ : ��
 * ��    �� : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
���: hongll           2022/08/12
����: hongll           2022/08/12
�޸�:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* ��˾���ܼ���Ȩ˵��
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <rte_malloc.h>
#include <rte_prefetch.h>
#include <rte_lcore.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "dpi_log.h"
#include "dpi_detect.h"
#include "dpi_forward_eth.h"


extern struct rte_mempool *pktmbuf_pool[2];
extern struct global_config g_config;


uint32_t g_nb_ports = 0;
uint32_t fwd_enabled_port_mask = 0x3;  // for test only!!

/* list of enabled ports */
uint32_t fwd_dst_ports[RTE_MAX_ETHPORTS];

#define MAX_PKT_BURST				(32)

#define MAX_RX_QUEUE_PER_LCORE		(16)
#define MAX_TX_QUEUE_PER_PORT		(16)
struct lcore_queue_conf {
	unsigned n_rx_port;
	unsigned rx_port_list[MAX_RX_QUEUE_PER_LCORE];
} __rte_cache_aligned;
struct lcore_queue_conf lcore_qconf[RTE_MAX_LCORE];

static struct rte_eth_dev_tx_buffer *tx_buffer[RTE_MAX_ETHPORTS];


/* Per-port statistics struct */
struct l2fwd_port_statistics {
	uint64_t tx;
	uint64_t rx;
	uint64_t dropped;
} __rte_cache_aligned;
struct l2fwd_port_statistics port_statistics[RTE_MAX_ETHPORTS];




void init_ether_forward(uint8_t type) {

	if (!(FWD_MASK & type))
		return;

	// ���ߵĶ˿ڲ�����ת��
	if (READ_FROM_PCAP == g_config.data_source /*&& portid != g_config.pcap_port_id*/) {
		printf("Notice: We won't transfer the SDT [out] packets by offline status!\n");
		return;
	}

	int i, ret;
	uint32_t nb_ports;
	uint8_t portid;
	unsigned rx_lcore_id;
	unsigned nb_ports_in_mask = 0;
	uint16_t last_port = 0;
	struct lcore_queue_conf *qconf;

	rx_lcore_id = 0;
	qconf = NULL;

	/* reset fwd_dst_ports */
	for (portid = 0; portid < RTE_MAX_ETHPORTS; portid++)
		fwd_dst_ports[portid] = 0;


#ifdef _DPI_DPDK_17
	nb_ports = rte_eth_dev_count();
#else
	nb_ports = rte_eth_dev_count_avail();
#endif

	g_nb_ports = nb_ports;

	fwd_enabled_port_mask = 0;

	RTE_ETH_FOREACH_DEV(portid) {

		// ���ݿ��õĶ˿ںţ���ȡ fwd_enabled_port_mask  ����
		fwd_enabled_port_mask |= (1 << portid);
	}

	/* check port mask to possible port mask */
	if (fwd_enabled_port_mask & ~((1 << nb_ports) - 1))
		rte_exit(EXIT_FAILURE, "pkt_fwd: Invalid portmask; possible (0x%x)\n", (1 << nb_ports) - 1);

	/*
	 * Each logical core is assigned a dedicated TX queue on each port.
	 */
	RTE_ETH_FOREACH_DEV(portid) {
		/* skip ports that are not enabled */
		if ((fwd_enabled_port_mask & (1 << portid)) == 0)
			continue;

		if (nb_ports_in_mask % 2) {
			fwd_dst_ports[portid] = last_port;
			fwd_dst_ports[last_port] = portid;
		}
		else
			last_port = portid;

		nb_ports_in_mask++;
	}
	if (nb_ports_in_mask % 2) {
		printf("Notice: odd number of ports in portmask.\n");
		fwd_dst_ports[last_port] = last_port;
	}

	for (portid = 0; portid < nb_ports; portid++) {

		// ���ߵĶ˿ڲ�����ת��
		if (READ_FROM_PCAP == g_config.data_source /*&& portid != g_config.pcap_port_id*/)
			continue;

		/* get the lcore_id for this port */
#if 1
		while (rte_lcore_is_enabled(rx_lcore_id) == 0
			|| lcore_qconf[rx_lcore_id].n_rx_port == g_config.nb_rxq) {
#else
		while (1) {

			if (READ_FROM_PCAP == g_config.data_source && portid == g_config.pcap_port_id) {
				if (lcore_qconf[rx_lcore_id].n_rx_port != g_config.nb_rxq)
					break;
			}
			else {
				if (!(rte_lcore_is_enabled(rx_lcore_id) == 0 || lcore_qconf[rx_lcore_id].n_rx_port == g_config.nb_rxq))
					break;
			}
#endif

			rx_lcore_id++;

			if (rx_lcore_id >= RTE_MAX_LCORE) {
				rte_exit(EXIT_FAILURE, "pkt_fwd: Not enough cores.\n");
			}
		}
		if (rx_lcore_id >= RTE_MAX_LCORE)
			continue;

		if (qconf != &lcore_qconf[rx_lcore_id]) {
			/* Assigned a new logical core in the loop above. */
			qconf = &lcore_qconf[rx_lcore_id];
		}
		qconf->rx_port_list[qconf->n_rx_port] = portid;
		qconf->n_rx_port++;

		}

	for (portid = 0; portid < nb_ports; portid++) {

		// ���ߵĶ˿ڲ�����ת��
		if (READ_FROM_PCAP == g_config.data_source /*&& portid != g_config.pcap_port_id*/)
			continue;

		/* Initialize TX buffers */
		tx_buffer[portid] = rte_zmalloc_socket("fwd_tx_buffer", RTE_ETH_TX_BUFFER_SIZE(MAX_PKT_BURST), 0, g_config.socketid);
		if (tx_buffer[portid] == NULL)
			rte_exit(EXIT_FAILURE, "pkt_fwd: Cannot allocate buffer for tx on port %u\n",
				portid);

		rte_eth_tx_buffer_init(tx_buffer[portid], MAX_PKT_BURST);

		ret = rte_eth_tx_buffer_set_err_callback(tx_buffer[portid],
			rte_eth_tx_buffer_count_callback,
			&port_statistics[portid].dropped);
		if (ret < 0)
			rte_exit(EXIT_FAILURE, "pkt_fwd: Cannot set error callback for tx buffer on port %u\n", portid);

	}
}

int do_forward(int portid, const uint8_t *pkt, int pkt_len, int *ok_num, int *fail_num)
{
	struct rte_mbuf *mbuf = rte_pktmbuf_alloc(pktmbuf_pool[g_config.socketid]);
	if (!mbuf)    {
        printf("Error on rte_pktmbuf_alloc, mbuf pool not enough\n");
        return -1;
    }

    if(pkt_len > g_config.mbuf_size)
    {
        pkt_len = g_config.mbuf_size;
    }

	mbuf->data_len = pkt_len;
	mbuf->pkt_len  = pkt_len;
    memcpy(((uint8_t *)mbuf->buf_addr + mbuf->data_off), pkt, pkt_len);

    if(0 == g_config.sdx_config.sdx_tx_port_num)
    {
        printf("ERROR: sdx_tx_port_num is 0, please checking eth dev pcie address is available\n");
        rte_pktmbuf_free(mbuf);
        return -3;
    }

    int sent = rte_eth_tx_burst(g_config.sdx_config.sdx_tx_port_list[portid], 0, &mbuf, 1);
    if (sent)
    {
        *ok_num += sent;
    }
    else
    {
        *fail_num += 1;
        rte_pktmbuf_free(mbuf);
        return -4;
    }
	return 0;
}
