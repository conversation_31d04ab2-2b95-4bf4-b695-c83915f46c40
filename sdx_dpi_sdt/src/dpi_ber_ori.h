#ifndef __DPI_BER_ORI__
#define __DPI_BER_ORI__

#define BER_CLASS_UNI    0
#define BER_CLASS_APP    1
#define BER_CLASS_CON    2
#define BER_CLASS_PRI    3
#define BER_CLASS_ANY   99            /* don't check class nor tag */

#define BER_UNI_TAG_EOC                    0    /* 'end-of-content' */
#define BER_UNI_TAG_BOOLEAN                1
#define BER_UNI_TAG_INTEGER                2
#define BER_UNI_TAG_BITSTRING              3
#define BER_UNI_TAG_OCTETSTRING            4
#define BER_UNI_TAG_NULL                   5
#define BER_UNI_TAG_OID                    6    /* OBJECT IDENTIFIER */
#define BER_UNI_TAG_ObjectDescriptor       7
#define BER_UNI_TAG_EXTERNAL               8
#define BER_UNI_TAG_REAL                   9
#define BER_UNI_TAG_ENUMERATED            10
#define BER_UNI_TAG_EMBEDDED_PDV          11
#define BER_UNI_TAG_UTF8String            12
#define BER_UNI_TAG_RELATIVE_OID          13

/* UNIVERSAL 14-15
 * Reserved for future editions of this
 * Recommendation | International Standard
 */
#define BER_UNI_TAG_SEQUENCE               16    /* SEQUENCE, SEQUENCE OF */
#define BER_UNI_TAG_SET                    17    /* SET, SET OF */
/* UNIVERSAL 18-22 Character string types */
#define BER_UNI_TAG_NumericString          18
#define BER_UNI_TAG_PrintableString        19
#define BER_UNI_TAG_TeletexString          20  /* TeletextString, T61String */
#define BER_UNI_TAG_VideotexString         21
#define BER_UNI_TAG_IA5String              22
/* UNIVERSAL 23-24 Time types */
#define BER_UNI_TAG_UTCTime                23
#define BER_UNI_TAG_GeneralizedTime        24
/* UNIVERSAL 25-30 Character string types */
#define BER_UNI_TAG_GraphicString          25
#define BER_UNI_TAG_VisibleString          26  /* VisibleString, ISO64String */
#define BER_UNI_TAG_GeneralString          27
#define BER_UNI_TAG_UniversalString        28
#define BER_UNI_TAG_CHARACTERSTRING        29
#define BER_UNI_TAG_BMPString              30
/* UNIVERSAL 31- ...
 * Reserved for addenda to this Recommendation | International Standard
 */
/* field types */
enum ftenum {
    FT_NONE,    /* used for text labels with no value */
    FT_PROTOCOL,
    FT_BOOLEAN, /* TRUE and FALSE come from <glib.h> */
    FT_CHAR,    /* 1-octet character as 0-255 */
    FT_UINT8,
    FT_UINT16,
    FT_UINT24,    /* really a UINT32, but displayed as 6 hex-digits if FD_HEX*/
    FT_UINT32,
    FT_UINT40,    /* really a UINT64, but displayed as 10 hex-digits if FD_HEX*/
    FT_UINT48,    /* really a UINT64, but displayed as 12 hex-digits if FD_HEX*/
    FT_UINT56,    /* really a UINT64, but displayed as 14 hex-digits if FD_HEX*/
    FT_UINT64,
    FT_INT8,
    FT_INT16,
    FT_INT24,    /* same as for UINT24 */
    FT_INT32,
    FT_INT40, /* same as for UINT40 */
    FT_INT48, /* same as for UINT48 */
    FT_INT56, /* same as for UINT56 */
    FT_INT64,
    FT_IEEE_11073_SFLOAT,
    FT_IEEE_11073_FLOAT,
    FT_FLOAT,
    FT_DOUBLE,
    FT_ABSOLUTE_TIME,
    FT_RELATIVE_TIME,
    FT_STRING,
    FT_STRINGZ, /* for use with proto_tree_add_item() */
    FT_UINT_STRING, /* for use with proto_tree_add_item() */
    FT_ETHER,
    FT_BYTES,
    FT_UINT_BYTES,
    FT_IPv4,
    FT_IPv6,
    FT_IPXNET,
    FT_FRAMENUM,    /* a UINT32, but if selected lets you go to frame with that number */
    FT_PCRE,    /* a compiled Perl-Compatible Regular Expression object */
    FT_GUID,    /* GUID, UUID */
    FT_OID,     /* OBJECT IDENTIFIER */
    FT_EUI64,
    FT_AX25,
    FT_VINES,
    FT_REL_OID, /* RELATIVE-OID */
    FT_SYSTEM_ID,
    FT_STRINGZPAD,    /* for use with proto_tree_add_item() */
    FT_FCWWN,
    FT_NUM_TYPES /* last item number plus one */
};

#define IS_FT_INT(ft)    ((ft)==FT_INT8||(ft)==FT_INT16||(ft)==FT_INT24||(ft)==FT_INT32||(ft)==FT_INT40||(ft)==FT_INT48||(ft)==FT_INT56||(ft)==FT_INT64)
#define IS_FT_UINT32(ft) ((ft)==FT_CHAR||(ft)==FT_UINT8||(ft)==FT_UINT16||(ft)==FT_UINT24||(ft)==FT_UINT32||(ft)==FT_FRAMENUM)
#define IS_FT_UINT(ft)   ((ft)==FT_CHAR||(ft)==FT_UINT8||(ft)==FT_UINT16||(ft)==FT_UINT24||(ft)==FT_UINT32||(ft)==FT_UINT40||(ft)==FT_UINT48||(ft)==FT_UINT56||(ft)==FT_UINT64||(ft)==FT_FRAMENUM)
#define IS_FT_TIME(ft)   ((ft)==FT_ABSOLUTE_TIME||(ft)==FT_RELATIVE_TIME)
#define IS_FT_STRING(ft) ((ft)==FT_STRING||(ft)==FT_STRINGZ||(ft)==FT_STRINGZPAD)


#define BER_FLAGS_OPTIONAL     0x00000001
#define BER_FLAGS_IMPLTAG      0x00000002
#define BER_FLAGS_NOOWNTAG     0x00000004
#define BER_FLAGS_NOTCHKTAG    0x00000008

typedef struct ber_ctx
{
    int type;
    void *session;
    void *info;
}ber_ctx_t;

typedef struct _dpi_ber_hd{
    int8_t          ber_class;
    int32_t         tag;
    uint32_t        flags;
    uint16_t        len;
    int8_t          pc;
    uint16_t        index;
}dpi_ber_hd;


typedef int (*ber_callback)(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, ber_ctx_t *ctx);
typedef int (*ber_type_fn)(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, ber_ctx_t *ctx);

typedef struct _ber_sequence_t {
    uint8_t       ber_class;
    uint32_t      tag;
    uint32_t      flags;
    ber_callback  func;
} ber_sequence_t;


typedef struct _ber_choice_t {
    uint32_t      value;
    uint8_t       ber_class;
    uint32_t      tag;
    uint32_t      flags;
    ber_callback  func;
} ber_choice_t;

int dpi_ber_identifier(struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *ber_class, uint8_t *pc, uint32_t *tag);
int dpi_ber_length(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *length, uint8_t *ind);
int dpi_ber_integer64(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, int64_t *value);
int dpi_ber_integer(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *value);
/* this function dissects a BER sequence
 */
int dpi_ber_sequence(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, const ber_sequence_t *seq, ber_ctx_t *ctx) ;
int dpi_ber_choice(struct dpi_pkt_st *pkt, uint32_t offset, const ber_choice_t *choice, ber_ctx_t *ctx, int *branch_taken);
int dpi_ber_tagged_type(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, uint8_t tag_impl, ber_ctx_t *ctx, ber_type_fn type);

int dpi_ber_octet_string_pkt(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, struct dpi_pkt_st *pkt_string);


/* 8.7 Encoding of an octetstring value */
int dpi_ber_constrained_octet_string(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len);
int dpi_ber_octet_string(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len);
/* 8.8 Encoding of a null value */
int dpi_ber_null(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset);
int dpi_ber_boolean(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *value);
int dpi_ber_constrained_sequence_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx);
int dpi_ber_sequence_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx);
int dpi_ber_constrained_set_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx);
int dpi_ber_set_of(uint8_t implicit_tag, struct dpi_pkt_st *pkt, int offset, const ber_sequence_t *seq, ber_ctx_t *ctx);

int dpi_ber_object_identifier_str(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len);
int dpi_ber_byte_string(uint8_t implicit_tag, struct dpi_pkt_st *pkt, uint32_t offset, char *val, int max_len, int *val_len);

#endif

