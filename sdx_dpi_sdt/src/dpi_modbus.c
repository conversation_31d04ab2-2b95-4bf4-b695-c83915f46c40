/****************************************************************************************
 * 文 件 名 : dpi_modbus.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liyj         2021/07/22
编码: liyj         2021/07/22
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>

#include "dpi_pint.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "charsets.h"

extern struct rte_mempool *tbl_log_mempool;

/* Modbus protocol function codes */
#define READ_COILS                  1
#define READ_DISCRETE_INPUTS        2
#define READ_HOLDING_REGS           3
#define READ_INPUT_REGS             4
#define WRITE_SINGLE_COIL           5
#define WRITE_SINGLE_REG            6
#define READ_EXCEPT_STAT            7
#define DIAGNOSTICS                 8
#define GET_COMM_EVENT_CTRS         11
#define GET_COMM_EVENT_LOG          12
#define WRITE_MULT_COILS            15
#define WRITE_MULT_REGS             16
#define REPORT_SLAVE_ID             17
#define READ_FILE_RECORD            20
#define WRITE_FILE_RECORD           21
#define MASK_WRITE_REG              22
#define READ_WRITE_REG              23
#define READ_FIFO_QUEUE             24
#define ENCAP_INTERFACE_TRANSP      43
#define UNITY_SCHNEIDER             90

/* Modbus protocol exception codes */
#define ILLEGAL_FUNCTION            0x01
#define ILLEGAL_ADDRESS             0x02
#define ILLEGAL_VALUE               0x03
#define SLAVE_FAILURE               0x04
#define ACKNOWLEDGE                 0x05
#define SLAVE_BUSY                  0x06
#define MEMORY_ERR                  0x08
#define GATEWAY_UNAVAILABLE         0x0a
#define GATEWAY_TRGT_FAIL           0x0b

/* return codes of function classifying packets as query/response */
#define QUERY_PACKET            0
#define RESPONSE_PACKET         1
#define CANNOT_CLASSIFY         2

typedef struct value_string {
    uint32_t value;
    const char *string;
}value_string;


#define MODBUS_PORT            502

typedef struct{
    uint8_t        exc_code_flag;
    uint8_t        reference_num_flag;
    
    uint8_t        opttype[16];
    uint8_t        func_string[32];
    uint8_t        exc_string[32];
    uint8_t        diag_string[32];
    uint8_t        bit_res[1024];
    uint8_t        register_res[1024];
    uint8_t        unit_id;
    uint8_t        function_code;
    uint8_t        exc_code;
    uint8_t        padding;
    uint16_t       diag_code;
    uint16_t       transaction_id;
    uint16_t       protocol_id;
    uint16_t       reference_num;
    uint16_t       len;
    uint16_t       bit_cnt;
    uint32_t       word_cnt;
    uint32_t       byte_cnt;
    uint32_t       data;

}modbus_info_t;

typedef struct {
    uint8_t  function_code;      // 功能码
    uint16_t trans_id;           // 识别码
    uint16_t base_address;       // 寄存器序号
    uint16_t num_reg;            // 寄存器个数
} modbus_request_info_t;

typedef struct ListNode{
    struct ListNode *next;
    modbus_request_info_t modbus_info;
} ListNode_t;

// 存放query的信息，id，register，
typedef struct modbus_session{
    ListNode_t *head;
}modbus_session_t;


/* Modbus diagnostic subfunction codes */
#define RETURN_QUERY_DATA                 0x00
#define RESTART_COMMUNICATION_OPTION      0x01
#define RETURN_DIAGNOSTIC_REGISTER        0x02
#define CHANGE_ASCII_INPUT_DELIMITER      0x03
#define FORCE_LISTEN_ONLY_MODE            0x04
#define CLEAR_COUNTERS_AND_DIAG_REG       0x0A
#define RETURN_BUS_MESSAGE_COUNT          0x0B
#define RETURN_BUS_COMM_ERROR_COUNT       0x0C
#define RETURN_BUS_EXCEPTION_ERROR_COUNT  0x0D
#define RETURN_SLAVE_MESSAGE_COUNT        0x0E
#define RETURN_SLAVE_NO_RESPONSE_COUNT    0x0F
#define RETURN_SLAVE_NAK_COUNT            0x10
#define RETURN_SLAVE_BUSY_COUNT           0x11
#define RETURN_BUS_CHAR_OVERRUN_COUNT     0x12
#define CLEAR_OVERRUN_COUNTER_AND_FLAG    0x14

/* Encapsulation Interface codes */
#define CANOPEN_REQ_RESP   0x0D
#define READ_DEVICE_ID     0x0E

/* Translate function to string, as given on p6 of
 * "Open Modbus/TCP Specification", release 1 by Andy Swales.
 */
static const value_string function_code_vals[] = {
    { READ_COILS,             "Read Coils" },
    { READ_DISCRETE_INPUTS,   "Read Discrete Inputs" },
    { READ_HOLDING_REGS,      "Read Holding Registers" },
    { READ_INPUT_REGS,        "Read Input Registers" },
    { WRITE_SINGLE_COIL,      "Write Single Coil" },
    { WRITE_SINGLE_REG,       "Write Single Register" },
    { READ_EXCEPT_STAT,       "Read Exception Status" },
    { DIAGNOSTICS,            "Diagnostics" },
    { GET_COMM_EVENT_CTRS,    "Get Comm. Event Counters" },
    { GET_COMM_EVENT_LOG,     "Get Comm. Event Log" },
    { WRITE_MULT_COILS,       "Write Multiple Coils" },
    { WRITE_MULT_REGS,        "Write Multiple Registers" },
    { REPORT_SLAVE_ID,        "Report Slave ID" },
    { READ_FILE_RECORD,       "Read File Record" },
    { WRITE_FILE_RECORD,      "Write File Record" },
    { MASK_WRITE_REG,         "Mask Write Register" },
    { READ_WRITE_REG,         "Read Write Register" },
    { READ_FIFO_QUEUE,        "Read FIFO Queue" },
    { ENCAP_INTERFACE_TRANSP, "Encapsulated Interface Transport" },
    { UNITY_SCHNEIDER,        "Unity (Schneider)" },
    { 0,                      NULL }
};

/* Translate exception code to string */
static const value_string exception_code_vals[] = {
    { ILLEGAL_FUNCTION,    "Illegal function" },
    { ILLEGAL_ADDRESS,     "Illegal data address" },
    { ILLEGAL_VALUE,       "Illegal data value" },
    { SLAVE_FAILURE,       "Slave device failure" },
    { ACKNOWLEDGE,         "Acknowledge" },
    { SLAVE_BUSY,          "Slave device busy" },
    { MEMORY_ERR,          "Memory parity error" },
    { GATEWAY_UNAVAILABLE, "Gateway path unavailable" },
    { GATEWAY_TRGT_FAIL,   "Gateway target device failed to respond" },
    { 0,                    NULL }
};

/* Translate Modbus Encapsulation Interface (MEI) code to string */
static const value_string encap_interface_code_vals[] = {
    { CANOPEN_REQ_RESP, "CANopen Request/Response " },
    { READ_DEVICE_ID,   "Read Device Identification" },
    { 0,                NULL }
};

/* Translate Modbus Diagnostic subfunction code to string */
static const value_string diagnostic_code_vals[] = {
    { RETURN_QUERY_DATA,                "Return Query Data" },
    { RESTART_COMMUNICATION_OPTION,     "Restart Communications Option" },
    { RETURN_DIAGNOSTIC_REGISTER,       "Return Diagnostic Register" },
    { CHANGE_ASCII_INPUT_DELIMITER,     "Change ASCII Input Delimiter" },
    { FORCE_LISTEN_ONLY_MODE,           "Force Listen Only Mode" },
    { CLEAR_COUNTERS_AND_DIAG_REG,      "Clear Counters and Diagnostic Register" },
    { RETURN_BUS_MESSAGE_COUNT,         "Return Bus Message Count" },
    { RETURN_BUS_COMM_ERROR_COUNT,      "Return Bus Communication Error Count" },
    { RETURN_BUS_EXCEPTION_ERROR_COUNT, "Return Bus Exception Error Count" },
    { RETURN_SLAVE_MESSAGE_COUNT,       "Return Slave Message Count" },
    { RETURN_SLAVE_NO_RESPONSE_COUNT,   "Return Slave No Response Count" },
    { RETURN_SLAVE_NAK_COUNT,           "Return Slave NAK Count" },
    { RETURN_SLAVE_BUSY_COUNT,          "Return Slave Busy Count" },
    { RETURN_BUS_CHAR_OVERRUN_COUNT,    "Return Bus Character Overrun Count" },
    { CLEAR_OVERRUN_COUNTER_AND_FLAG,   "Clear Overrun Counter and Flag" },
    { 0,                                NULL }
};

    enum modbus_index_em{
        EM_MODBUS_OPTTYPE,
        EM_MODBUS_TRANS_ID,
        EM_MODBUS_PROT_ID,
        EM_MODBUS_LENGTH,
        EM_MODBUS_UNIT_ID,
        EM_MODBUS_FUCTION,
        EM_MODBUS_FUCTION_EXP,
        EM_MODBUS_EXCEPTION,
        EM_MODBUS_EXCEPTION_EXP,
        EM_MODBUS_DIAGLOG,
        EM_MODBUS_DIAGLOG_EXP,
        EM_MODBUS_REFERENCE_NUM,
        EM_MODBUS_BIT_CNT,
        EM_MODBUS_BYTE_CNT,
        EM_MODBUS_WORD_CNT,
        EM_MODBUS_REGISTER,
        EM_MODBUS_BIT,
        EM_MODBUS_DATA,
        EM_MODBUS_PADDING,

        
        EM_MODBUS_MAX
    };

static dpi_field_table modbus_field_array[] = {
    DPI_FIELD_D(EM_MODBUS_OPTTYPE,                            EM_F_TYPE_STRING,             "opttype"),
    DPI_FIELD_D(EM_MODBUS_TRANS_ID,                           EM_F_TYPE_UINT16,             "trans_id"),
    DPI_FIELD_D(EM_MODBUS_PROT_ID,                            EM_F_TYPE_UINT16,             "prot_id"),
    DPI_FIELD_D(EM_MODBUS_LENGTH,                             EM_F_TYPE_UINT16,             "length"),
    DPI_FIELD_D(EM_MODBUS_UNIT_ID,                            EM_F_TYPE_UINT8,              "unit_id"),
    DPI_FIELD_D(EM_MODBUS_FUCTION,                            EM_F_TYPE_UINT8,              "func_code"),
    DPI_FIELD_D(EM_MODBUS_FUCTION_EXP,                        EM_F_TYPE_STRING,             "func_exp"),
    DPI_FIELD_D(EM_MODBUS_EXCEPTION,                          EM_F_TYPE_UINT8,              "exc_code"),
    DPI_FIELD_D(EM_MODBUS_EXCEPTION_EXP,                      EM_F_TYPE_STRING,             "exc_exp"),
    DPI_FIELD_D(EM_MODBUS_DIAGLOG,                            EM_F_TYPE_UINT16,             "diag_code"),
    DPI_FIELD_D(EM_MODBUS_DIAGLOG_EXP,                        EM_F_TYPE_STRING,             "diag_exp"),
    DPI_FIELD_D(EM_MODBUS_REFERENCE_NUM,                      EM_F_TYPE_UINT16,             "ref_num"),
    DPI_FIELD_D(EM_MODBUS_BIT_CNT,                            EM_F_TYPE_UINT16,             "bit_cnt"),
    DPI_FIELD_D(EM_MODBUS_BYTE_CNT,                           EM_F_TYPE_UINT32,             "byte_cnt"),
    DPI_FIELD_D(EM_MODBUS_WORD_CNT,                           EM_F_TYPE_UINT32,             "word_cnt"),
    DPI_FIELD_D(EM_MODBUS_REGISTER,                           EM_F_TYPE_STRING,             "register_content"),
    DPI_FIELD_D(EM_MODBUS_BIT,                                EM_F_TYPE_STRING,             "bit_content"),
    DPI_FIELD_D(EM_MODBUS_DATA,                               EM_F_TYPE_UINT32,             "data"),
    DPI_FIELD_D(EM_MODBUS_PADDING,                            EM_F_TYPE_UINT8,              "padding"),
};

static dpi_field_table modbus_field_array_sdt[] = {
    DPI_FIELD_D(EM_MODBUS_OPTTYPE,                            YV_FT_BYTES,                  "opttype"),
    DPI_FIELD_D(EM_MODBUS_TRANS_ID,                           YV_FT_UINT16,                 "trans_id"),
    DPI_FIELD_D(EM_MODBUS_PROT_ID,                            YV_FT_UINT16,                 "prot_id"),
    DPI_FIELD_D(EM_MODBUS_LENGTH,                             YV_FT_UINT16,                 "length"),
    DPI_FIELD_D(EM_MODBUS_UNIT_ID,                            YV_FT_UINT8,                  "unit_id"),
    DPI_FIELD_D(EM_MODBUS_FUCTION,                            YV_FT_UINT8,                  "func_code"),
    DPI_FIELD_D(EM_MODBUS_FUCTION_EXP,                        YV_FT_BYTES,                  "func_exp"),
    DPI_FIELD_D(EM_MODBUS_EXCEPTION,                          YV_FT_UINT8,                  "exc_code"),
    DPI_FIELD_D(EM_MODBUS_EXCEPTION_EXP,                      YV_FT_BYTES,                  "exc_exp"),
    DPI_FIELD_D(EM_MODBUS_DIAGLOG,                            YV_FT_UINT16,                 "diag_code"),
    DPI_FIELD_D(EM_MODBUS_DIAGLOG_EXP,                        YV_FT_BYTES,                  "diag_exp"),
    DPI_FIELD_D(EM_MODBUS_REFERENCE_NUM,                      YV_FT_UINT16,                 "ref_num"),
    DPI_FIELD_D(EM_MODBUS_BIT_CNT,                            YV_FT_UINT16,                 "bit_cnt"),
    DPI_FIELD_D(EM_MODBUS_BYTE_CNT,                           YV_FT_UINT32,                 "byte_cnt"),
    DPI_FIELD_D(EM_MODBUS_WORD_CNT,                           YV_FT_UINT32,                 "word_cnt"),
    DPI_FIELD_D(EM_MODBUS_REGISTER,                           YV_FT_BYTES,                  "register_content"),
    DPI_FIELD_D(EM_MODBUS_BIT,                                YV_FT_BYTES,                  "bit_content"),
    DPI_FIELD_D(EM_MODBUS_DATA,                               YV_FT_UINT32,                 "data"),
    DPI_FIELD_D(EM_MODBUS_PADDING,                            YV_FT_UINT8,                  "padding"),
};


static const char *
try_val_to_str(uint32_t val, const value_string *vs)
{
    int i=0;
    if(vs){
        while(vs[i].string){
            if(vs[i].value==val){
                return vs[i].string;
            }
            i++;
        }
    }
    return NULL;
}



static int write_modbus_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;
    
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return 0;
    }

    modbus_info_t *info=( modbus_info_t *)field_info;
    if(!info){
        return 0;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "modbus");

    for(i=0;i<EM_MODBUS_MAX;i++){
        switch(i){
        case EM_MODBUS_OPTTYPE:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            (const uint8_t *)info->opttype, strlen((const char*)info->opttype));
            break;
        case EM_MODBUS_TRANS_ID:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->transaction_id);
            break;
        case EM_MODBUS_PROT_ID:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->protocol_id);
            break;
        case EM_MODBUS_LENGTH:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->len);
            break;
        case EM_MODBUS_UNIT_ID:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->unit_id);
            break;
        case EM_MODBUS_FUCTION:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->function_code);
            break;
        case EM_MODBUS_FUCTION_EXP:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            (const uint8_t *)info->func_string, strlen((const char*)info->func_string));
            break;
        case EM_MODBUS_EXCEPTION:
            if(info->exc_code_flag)
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
                    NULL, info->exc_code);
            else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_MODBUS_EXCEPTION_EXP:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            (const uint8_t *)info->exc_string, strlen((const char*)info->exc_string));
            break;
        case EM_MODBUS_DIAGLOG:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->diag_code);
            break;
        case EM_MODBUS_DIAGLOG_EXP:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            (const uint8_t *)info->diag_string, strlen((const char*)info->diag_string));
            break;
        case EM_MODBUS_REFERENCE_NUM:
            if(info->reference_num_flag)
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
                NULL, info->reference_num);
            else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_MODBUS_BIT_CNT:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->bit_cnt);
            break;
        case EM_MODBUS_BYTE_CNT:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->byte_cnt);
            break;
        case EM_MODBUS_WORD_CNT:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->word_cnt);
            break;
        case EM_MODBUS_REGISTER:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            (const uint8_t *)info->register_res, strlen((const char*)info->register_res));
            break;
        case EM_MODBUS_BIT:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            (const uint8_t *)info->bit_res, strlen((const char*)info->bit_res));
            break;
        case EM_MODBUS_DATA:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->data);
            break;
        case EM_MODBUS_PADDING:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, modbus_field_array[i].type, 
            NULL, info->padding);
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

   
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_MODBUS;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->flow        = flow;
    
    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}



static void identify_modbus(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_MODBUS] == 0)
        return;
    uint16_t chksum;
    uint32_t length;
    uint8_t type;
    uint8_t offset;

    if (ntohs(flow->tuple.inner.port_dst) != MODBUS_PORT 
       && ntohs(flow->tuple.inner.port_src) != MODBUS_PORT) {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_MODBUS);
        return;
    }


    /* Make sure there's at least enough data to determine it's a Modbus TCP packet */
    if (payload_len < 8){
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_MODBUS);
        return;
    }

    /* check that it actually looks like Modbus/TCP */
    /* protocol id == 0 */
    if(get_uint16_ntohs(payload, 2) != 0 ){
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_MODBUS);
        return;
    }

    /* length is at least 2 (unit_id + function_code) */
    if(get_uint16_ntohs(payload, 4) < 2 ){
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_MODBUS);
        return;
    }

    flow->real_protocol_id = PROTOCOL_MODBUS;
    return;
}

static int
classify_mbtcp_packet(struct flow_info *flow)
{
    /* see if nature of packets can be derived from src/dst ports */
    /* if so, return as found */
    /*                        */
    /* XXX Update Oct 2012 - It can be difficult to determine if a packet is a query or response; some way to track  */
    /* the Modbus/TCP transaction ID for each pair of messages would allow for detection based on a new seq. number. */
    /* Otherwise, we can stick with this method; a configurable port option has been added to allow for usage of     */
    /* user ports either than the default of 502.                                                                    */
    if (( flow->port_src == MODBUS_PORT ) && ( flow->port_dst != MODBUS_PORT ))
        return RESPONSE_PACKET;
    if (( flow->port_src != MODBUS_PORT ) && ( flow->port_dst == MODBUS_PORT ))
        return QUERY_PACKET;

    /* else, cannot classify */
    return CANNOT_CLASSIFY;
}

static ListNode_t* find_node(ListNode_t *head, uint8_t function_code, uint16_t transaction_id){
    if (head == NULL) return NULL;
    ListNode_t * tmp = head->next;
    while(tmp){
        if (tmp->modbus_info.function_code == function_code && tmp->modbus_info.trans_id == transaction_id)
            return tmp;
        tmp = tmp->next;
    }
    return NULL;
}

static int list_head_insert_node(ListNode_t *head, ListNode_t *node)
{
    if (!head || !node)
        return -1;

    node->next = head->next;
    head->next = node;

    return 0;
}


static int delete_node(ListNode_t *head, ListNode_t *delete){
    if (head == NULL || delete == NULL) return 0;
    ListNode_t * tmp = head;
    while(tmp->next){
        if (tmp->next == delete){
            tmp->next = tmp->next->next;
            free(delete);
            return 1;
        }
        tmp = tmp->next;
    }
    return 0;
}

static int 
dissect_modbus_request(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, modbus_info_t *info){
    int offset = 0;

    modbus_session_t *session;
    session = (modbus_session_t *)flow->app_session;
    if (session == NULL ) return -1;

    switch (info->function_code) {
        case READ_COILS:
        case READ_DISCRETE_INPUTS:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->bit_cnt = get_uint16_ntohs(payload, offset);
            offset += 2;

            // 添加到队列中
            if (find_node(session->head, info->function_code, info->transaction_id) == NULL){
                ListNode_t *tmp = (ListNode_t *)malloc(sizeof(ListNode_t));
                tmp->modbus_info.trans_id = info->transaction_id;
                tmp->modbus_info.function_code = info->function_code;
                tmp->modbus_info.base_address = info->reference_num;
                tmp->modbus_info.num_reg = info->bit_cnt;

                list_head_insert_node(session->head, tmp);
            }
            break;
        }
        case READ_INPUT_REGS:
        case READ_HOLDING_REGS:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->word_cnt = get_uint16_ntohs(payload, offset);
            offset += 2;

            // 添加到队列中
            if (find_node(session->head, info->function_code, info->transaction_id) == NULL){
                ListNode_t *tmp = (ListNode_t *)malloc(sizeof(ListNode_t));
                tmp->modbus_info.trans_id = info->transaction_id;
                tmp->modbus_info.function_code = info->function_code;
                tmp->modbus_info.base_address = info->reference_num;
                tmp->modbus_info.num_reg = info->word_cnt;

                list_head_insert_node(session->head, tmp);
            }
            break;
        }
        case WRITE_SINGLE_COIL:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->data = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->padding = get_uint8_t(payload, offset);
            offset += 1;

            break;
        }
        case WRITE_SINGLE_REG:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->data = get_uint16_ntohs(payload, offset);
            offset += 2;

            break;
        }
        case DIAGNOSTICS:{
            info->diag_code = get_uint16_ntohs(payload, offset);
            offset += 2;

            const char    *diag_string = "";
            diag_string = try_val_to_str(info->diag_code, diagnostic_code_vals);
            if (diag_string != NULL) {
                strcpy((char*)info->diag_string, diag_string);
            }

            info->data = get_uint16_ntohs(payload, offset);
            offset += 2;

            break;
        }
        case WRITE_MULT_COILS:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->bit_cnt = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->byte_cnt = get_uint8_t(payload, offset);
            offset += 1;
            info->data = info->byte_cnt==1?get_uint8_t(payload, offset):get_uint16_ntohs(payload, offset);
            offset += 1;
            
            break;
        }
        case WRITE_MULT_REGS:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->word_cnt = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->byte_cnt = get_uint8_t(payload, offset);
            offset += 1;
            int data_offset = 0;
            uint16_t data16;
            uint8_t *reg_res[16];
            uint16_t reg_num = info->reference_num;
            while (data_offset <= (int)info->byte_cnt) {
                data16 = get_uint16_ntohs(payload, offset);
                snprintf((char *)reg_res, sizeof(reg_res) - 1, "register:%u:%u ", reg_num, data16);
                strcat((char *)info->bit_res, (const char *)reg_res);
                data_offset += 2;
                offset += 2;
                reg_num += 1;

                if ((info->word_cnt + info->reference_num) < (uint16_t)(reg_num+1)) {
                    break;
                }
            }

            break;
        }
        default :{
            break;
        }
    }

    return 0;
}
static int 
dissect_modbus_response(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, modbus_info_t *info){
    int offset = 0;

    modbus_session_t *session;
    session = (modbus_session_t *)flow->app_session;
    if (session == NULL ) return -1;

    switch (info->function_code) {
        case READ_COILS:
        case READ_DISCRETE_INPUTS:{
            info->byte_cnt = (uint32_t)get_uint8_t(payload, offset);
            offset += 1;
            ListNode_t *res = find_node(session->head, info->function_code, info->transaction_id);
            if (res != NULL) {
                int data_offset = 0,ii;
                uint8_t data_per_bit;
                uint8_t data8;
                uint8_t *bit_res[16];
                uint16_t reg_num = res->modbus_info.base_address;
                while (data_offset < (int)info->byte_cnt) {
                    data8 = get_uint8_t(payload, offset);
                    for (ii = 0; ii < 8; ii++) {
                        data_per_bit = (data8 & (1 << ii)) > 0;
                        snprintf((char *)bit_res, sizeof(bit_res) - 1, "bit:%u:%u ", reg_num, data_per_bit);
                        strcat((char *)info->bit_res, (const char *)bit_res);
                        data_offset += 1;
                        reg_num =+ 1;
                        offset += 1;

                        /* If all the requested bits have been read, stop now */
                        if ((res->modbus_info.base_address + res->modbus_info.num_reg) <= reg_num) {
                            break;
                        }
                    }
                }

                if (delete_node(session->head, res) != 1){}
                    //printf("failed\n");
            }

            break;
        }
        case WRITE_SINGLE_COIL:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->data = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->padding = get_uint8_t(payload, offset);
            offset += 1;

            break;
        }
        case WRITE_SINGLE_REG:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->data = get_uint16_ntohs(payload, offset);
            offset += 2;

            break;
        }
        case DIAGNOSTICS:{
            info->diag_code = get_uint16_ntohs(payload, offset);
            offset += 2;
            
            const char    *diag_string = "";
            diag_string = try_val_to_str(info->diag_code, diagnostic_code_vals);
            if (diag_string != NULL) {
                strcpy((char*)info->diag_string, diag_string);
            }

            info->data = get_uint16_ntohs(payload, offset);
            offset += 2;

            break;
        }
        case WRITE_MULT_COILS:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->bit_cnt = get_uint16_ntohs(payload, offset);
            offset += 2;

            break;
        }
        case WRITE_MULT_REGS:{
            info->reference_num_flag=1;
            info->reference_num = get_uint16_ntohs(payload, offset);
            offset += 2;
            info->word_cnt = get_uint16_ntohs(payload, offset);
            offset += 2;
            
            break;
        }
        case READ_INPUT_REGS:
        case READ_HOLDING_REGS:{
            info->byte_cnt = (uint32_t)get_uint8_t(payload, offset);
            offset += 1;
            ListNode_t *res = find_node(session->head, info->function_code, info->transaction_id);
            if (res != NULL) {
                int data_offset = 0;
                uint16_t data16;
                uint8_t *reg_res[16];
                uint16_t reg_num = res->modbus_info.base_address;
                while (data_offset < (int)info->byte_cnt) {
                    data16 = get_uint16_ntohs(payload, offset);
                    snprintf((char *)reg_res, sizeof(reg_res) - 1, "register:%u:%u ", reg_num, data16);
                    strcat((char *)info->bit_res, (const char *)reg_res);
                    data_offset += 2;
                    offset += 2;
                    reg_num += 1;

                    if ((res->modbus_info.base_address + res->modbus_info.num_reg) <= reg_num) {
                        break;
                    }
                }

                if (delete_node(session->head, res) != 1){}
                    //printf("failed\n");
            }

            break;
        }
        default :{
            break;
        }
    }

    return 1;
}


static int dissect_modbus(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if (payload_len < 8)
        return PKT_DROP;

    // 1，按帧解析，无需组包
    // 2，一条流的session存储query包信息，response需要对应信息解析
    const char    *pkt_type_str = "";
    const char    *err_str = "";
    const char    *func_string = "";
    const char    *exc_string = "";
    uint8_t        unit_id, exception_code, subfunction_code,mei_type;
    uint16_t       transaction_id, protocol_id, diagnostic_code;
    uint32_t       remain_len;
    int            offset, packet_type;

    modbus_session_t *session;
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(modbus_session_t));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(modbus_session_t));
        session = (modbus_session_t *)flow->app_session;
        session->head = (ListNode_t*)malloc(sizeof(ListNode_t));
        session->head->next = NULL;
    }
    session = (modbus_session_t *)flow->app_session;

    modbus_info_t modbus_info;
    memset((void*)&modbus_info, 0 ,sizeof(modbus_info_t));

    offset = 0;

    modbus_info.transaction_id = get_uint16_ntohs(payload, offset);
    offset += 2;

    modbus_info.protocol_id = get_uint16_ntohs(payload, offset);
    offset += 2;

    modbus_info.len = get_uint16_ntohs(payload, offset);
    offset += 2;

    modbus_info.unit_id = get_uint8_t(payload, offset);
    offset += 1;

    modbus_info.function_code = get_uint8_t(payload, offset) & 0x7F;
    offset += 1;

    if(6u + modbus_info.len > payload_len)
        return PKT_DROP;

    /* "Request" or "Response" */
    packet_type = classify_mbtcp_packet(flow);

    switch ( packet_type ) {
        case QUERY_PACKET :
            pkt_type_str="Query";
            break;
        case RESPONSE_PACKET :
            pkt_type_str="Response";
            break;
        case CANNOT_CLASSIFY :
            err_str="Unable to classify as query or response.";
            pkt_type_str="unknown";
            break;
        default :
            break;
    }
    
    /* Find exception - last bit set in function code */
    if (get_uint8_t(payload, 7) & 0x80) {
        modbus_info.exc_code_flag = 1;
        modbus_info.exc_code = get_uint8_t(payload, offset);
    }
    
    //else {
    //    modbus_info.exc_code = 0;
    //}

    if ((modbus_info.function_code == ENCAP_INTERFACE_TRANSP) && (modbus_info.exc_code == 0))  {
        mei_type = get_uint8_t(payload, offset);
        func_string = try_val_to_str(mei_type, encap_interface_code_vals);
        subfunction_code = 1;
    }
    else if ((modbus_info.function_code == DIAGNOSTICS) && (modbus_info.exc_code == 0))  {
        diagnostic_code = get_uint16_ntohs(payload, offset);
        func_string = try_val_to_str(diagnostic_code, diagnostic_code_vals);
        subfunction_code = 1;
    }
    else {
        func_string = try_val_to_str(modbus_info.function_code, function_code_vals);
        subfunction_code = 0;
    }

    if ( modbus_info.exc_code != 0 )
        err_str="Exception returned ";



    // 解析fuction 剩余的再次递归解析 request 进行存储 链表 
    // 头插法方便寻找和删除（要不要删除？）

    remain_len = payload_len - 8;

    if (modbus_info.exc_code != 0) {
        exc_string = try_val_to_str(modbus_info.exc_code, exception_code_vals);
    } else {
        /* Follow different dissection path depending on whether packet is query or response */
        if (packet_type == QUERY_PACKET) {
            dissect_modbus_request(flow, direction, payload+8, remain_len, &modbus_info);
        }
        else if (packet_type == RESPONSE_PACKET) {
            dissect_modbus_response(flow, direction, payload+8, remain_len, &modbus_info);
        }
    }

    // get func code's & exception code's description
    if (exc_string != NULL) {
        strcpy((char*)modbus_info.exc_string, exc_string);
    }
    if (func_string != NULL) {
        strcpy((char*)modbus_info.func_string, func_string);
    } else {
        strcpy((char*)modbus_info.func_string, "unknown function");
    }
    strcpy((char*)modbus_info.opttype, pkt_type_str);

    write_modbus_log(flow, direction, &modbus_info, NULL);

    remain_len = payload_len - 6 - modbus_info.len;
    if (remain_len >= 8) {
        dissect_modbus(flow, direction, seq, payload+6+modbus_info.len, remain_len, flag);
    }

    return PKT_OK;
}


static void exit_modbus(struct flow_info *flow, void *session)
{
    modbus_session_t *app_session = (modbus_session_t *)session;
    ListNode_t  *node, *tmp;

    if (app_session == NULL)
        return;

    for (node=app_session->head; node!=NULL; node=tmp)
    {
        tmp = node->next;
        free(node);
    }
}

static void init_modbus_dissector(void)
{
    dpi_register_proto_schema(modbus_field_array,EM_MODBUS_MAX,"modbus");
    port_add_proto_head(IPPROTO_TCP, MODBUS_PORT, PROTOCOL_MODBUS);

    tcp_detection_array[PROTOCOL_MODBUS].proto = PROTOCOL_MODBUS;
    tcp_detection_array[PROTOCOL_MODBUS].identify_func = identify_modbus;
    tcp_detection_array[PROTOCOL_MODBUS].dissect_func = dissect_modbus;
    tcp_detection_array[PROTOCOL_MODBUS].exit_func = exit_modbus;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_MODBUS].excluded_protocol_bitmask, PROTOCOL_MODBUS);


    map_fields_info_register(modbus_field_array_sdt, PROTOCOL_MODBUS, EM_MODBUS_MAX,"modbus");

    return;
}


static __attribute((constructor)) void    before_init_modbus(void){
    register_tbl_array(TBL_LOG_MODBUS, 0, "modbus", init_modbus_dissector);
}


