#include <rte_ring.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/time.h>
#include <string.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <errno.h>
#include <stdlib.h>
#include <assert.h>
#include <fcntl.h>
#include <string.h>
#include <time.h>
#include <endian.h>
#include <pcap/pcap.h>

#include <rte_ether.h>
#include <rte_ethdev.h>

#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "sdt_action_out.h"
#include "post.h"
#include "dpi_forward_eth.h"
#include "dpi_forward_kafka.h"
#include "dpi_utils.h"
#include "sdx/LineConvert.h"

#include "cJSON.h"
extern int cloneIndex, freeIndex;
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;;
extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];
struct rte_hash *g_sdt_hash_db;
struct rte_ring *sdt_out_ring[SDT_MAX_OUT_RING];

extern rte_atomic64_t sdt_pcap_out_forward_pkts;
extern rte_atomic64_t sdt_pcap_out_forward_ok_pkts;
extern rte_atomic64_t sdt_pcap_out_forward_fail_pkts;
extern rte_atomic64_t sdt_pcap_success_pkts;
extern rte_atomic64_t sdt_event_success_pkts;
extern rte_atomic64_t sdt_pcap_fail_pkts;
extern rte_atomic64_t sdt_event_fail_pkts;
extern rte_atomic64_t sdt_pcap_out_pkts;
extern rte_atomic64_t sdt_syslog_fail_pkts;
extern rte_atomic64_t sdt_syslog_success_pkts;

int g_sdt_hash_db_clear_flag = 0;

static int sdt_out_thfunc_cnt = 0;
static int sdt_out_thfunc_signal = 0;

struct mac_forward_cnt_t mac_forward_cnt[20]; //默认最大10个，Task最大10个

SdxOutThreadStatus *g_sdx_output_thread_status;

static void sdt_group_hash_db_clean_(void);


pid_t gettid(void);
pid_t gettid(void)
{
    return syscall(SYS_gettid);
}

int sdt_init_rules_to_hash_db_hit(int action, SdtMatchResult *result, sdt_out_status *status)
{
    switch(action)
    {
        case SAE_packetDump:
            if(result->packetDump_args.minute>0){
                status->pcap_status.max_time       = result->packetDump_args.minute;
            }else{
                status->pcap_status.max_time       = g_config.web_config.pcap_truncation;
            }

            if(result->packetDump_args.size>0){
                status->pcap_status.max_pcap_size  = result->packetDump_args.size;
            }else{
                status->pcap_status.max_pcap_size  = g_config.web_config.pcap_size;
            }
            break;
        default:
            break;
    }
    return 0;
}

#if 1
int
sdt_init_rules_to_hash_db(SdtMatchResult *rules_result, sdt_out_status  *rule_elem)
{
    if(!rules_result){
        return -1;
    }

    rule_elem->match_result = rules_result;
    int ret = PKT_DROP;
    for(int chunli = 0; chunli < 8 *(int)sizeof(rules_result->action); chunli++)
    {
        uint32_t action = 1 << chunli;
        if(rules_result->action & action)
        {
            ret = sdt_init_rules_to_hash_db_hit(action, rules_result, rule_elem);
            if(PKT_DROP == ret)
            {
                return PKT_DROP;
            }
        }
    }

    rule_elem->event_status.max_event_num = g_config.sdt_out_event_max_line;
    rule_elem->event_status.max_time      = g_config.write_tbl_maxtime;

    return 0;
}

int
sdt_rule_hash_db_insert(SdtMatchResult *rules_result)
{
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    sdt_out_status  *lookup_result=NULL;
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d",
            rules_result->unitID,
            rules_result->taskID,
            rules_result->groupID,
            rules_result->ruleId);
    int pos = rte_hash_lookup_data(g_sdt_hash_db,(const void *)rule_key, (void *)&lookup_result);
    if(pos>=0){
        return 0;
    }

    sdt_out_status  *rule_elem=NULL;
    rule_elem=(sdt_out_status  *)malloc(sizeof(sdt_out_status));
    if(!rule_elem){
        DPI_LOG(DPI_LOG_ERROR, "rule_hash_code:%u insert hash db failed, malloc failed!",rules_result->ruleHashCode);
        return -1;
    }
    memset(rule_elem, 0, sizeof(sdt_out_status));
    /* copy match_result to rule_elem->match_result */

    //rule_elem->match_result=rules_result;
    sdt_init_rules_to_hash_db(rules_result, rule_elem);

    if(!rule_elem->match_result){
        return -1;
    }

    int retval = rte_hash_add_key_data(g_sdt_hash_db, (const void *)rule_key, (void *)rule_elem);
    if(retval<0){
        DPI_LOG(DPI_LOG_ERROR, "rte_hash_add_key_data rule_hash_code:%u insert hash db failed! hash table numbers:%d",
                                                           rule_elem->match_result->ruleHashCode,
                                                           rte_hash_count(g_sdt_hash_db));
        free(rule_elem);
        rule_elem = NULL;
    }

    return 0;
}

static int
sdt_rule_hash_db_clean_(void)
{
    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        retval = rte_hash_del_key(g_sdt_hash_db, (const void *)rule_key);
        if (retval < 0){
            continue;
        }
        if(next_hop){
            free(next_hop);
            next_hop=NULL;
        }
    }

    return 0;
}

int
sdt_rule_hash_db_clean(void)
{
    //通知管理线程去 clean
    g_sdt_hash_db_clear_flag = 1;
    printf("wait g_sdt_hash_db_clear ...\n");

    //自旋等待 -- 全部就绪

    log_debug("等待 sdt_match 线程的信号...");
    while (sdt_match_thfunc_signal > 0)
    {
        usleep(1000*100);
    }
    log_debug("sdt_match 线程已全部暂停!");

    log_debug("等待 tbl_out 线程的信号...");
    while (tbl_out_thfunc_signal > 0)
    {
        usleep(1000*100);
    }
    log_debug("tbl_out 线程已全部暂停!");

    log_debug("等待 sdt_out 线程的信号...");
    while(sdt_out_thfunc_signal != sdt_out_thfunc_cnt)
    {
        usleep(1000*100);
    }
    log_debug("sdt_out 线程已全部暂停!");

    //开始干活
    sdt_clean_rule_data_status();
    sdt_group_hash_db_clean_();
    sdt_rule_hash_db_clean_();

    g_sdt_hash_db_clear_flag = 0;
    printf("wait g_sdt_hash_db_clear OK\n");

    return 0;
}


sdt_out_status *
sdt_rule_hash_db_lookup(SdtMatchResult *match_result)
{
    if(!match_result){
        return NULL;
    }

    sdt_out_status  *lookup_result=NULL;
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d",
            match_result->unitID,
            match_result->taskID,
            match_result->groupID,
            match_result->ruleId);
    int pos = rte_hash_lookup_data( g_sdt_hash_db,
                                    (const void *)rule_key,
                                    (void **)&lookup_result);
    if(pos>=0){
        return lookup_result;
    }


    sdt_out_status  *rule_elem=NULL;
    rule_elem=(sdt_out_status  *)malloc(sizeof(sdt_out_status));
    if(!rule_elem){
        DPI_LOG(DPI_LOG_ERROR, "rule_hash_code:%u insert hash db failed, malloc failed!",
                                                          match_result->ruleHashCode);
        return NULL;
    }
    memset(rule_elem, 0, sizeof(sdt_out_status));
    sdt_init_rules_to_hash_db(match_result, rule_elem);

    int retval = rte_hash_add_key_data(g_sdt_hash_db, (const void *)rule_key, (void *)rule_elem);
    if(retval<0){
        DPI_LOG(DPI_LOG_ERROR, "rte_hash_add_key_data rule_hash_code:%u insert hash db failed! hash table numbers:%d",
                                                           rule_elem->match_result->ruleHashCode,
                                                           rte_hash_count(g_sdt_hash_db));
        free(rule_elem);
        return NULL;
    }

    return rule_elem;
}

sdt_out_status *
sdt_rule_hash_lookup_key(const char *uintid, const char *taskid, const char *groupid, uint32_t rule_id)
{
    sdt_out_status  *lookup_result=NULL;
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d", uintid, taskid, groupid, rule_id);
    int pos = rte_hash_lookup_data( g_sdt_hash_db, (const void *)rule_key, (void **)&lookup_result);
    if(pos>=0){
        return lookup_result;
    }
    return NULL;
}
#endif



/** 抽象层
 * 新增以"规则组"为单位输出一个pcap文件, 并保留以"规则"为单位输出pcap的功能.
 * 解决 两者之间属性不同,所有权不同,操作不同的问题
 **/
struct TaskPropertyExtract
{
    void  *self;

    SdtMatchResult*             (*fn_match_result) (void *self);
    void                        (*fn_rule_id) (void *self, char *, int);
    struct mac_packet_header*   (*fn_mac_hdr) (void *self);
};
static int  (*sdt_create_pcap_file)(sdt_out_status  *rule_node, struct packet_stream  *pkt_elm, int thread_index);
static void (*sdt_fragment_pcap_file)(SdxOutThreadCtx *sdx_out_ctx, sdt_out_status  *rule_node);


static int SdxRecIPRule_init(SdxRecIPRule *rec)
{
    FILE    *fp;
    int     len = 0;
    char    *file_name = rec->filename_writing;

    len = snprintf(file_name, FILENAME_MAX,
            "/tmp/tbls/sdt/pcap/"
            /*, g_config.tbl_out_dir, time_to_date(g_config.g_now_time)*/);

    if (access(file_name, F_OK))
        mkdirs(file_name);

    if (g_config.show_task_id)
    {
        len += snprintf(file_name + len, FILENAME_MAX - len,
                    "/%s-%s-pcap-%u-%s.json",
                    g_config.sdt_out_produce_data_dev_name,
                    g_config.sdx_config.sdx_ip_str,
                    dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL),
                    g_config.task_id);
    }
    else
    {
        len += snprintf(file_name + len, FILENAME_MAX - len,
                    "/%s-%s-pcap-%u.json",
                    g_config.sdt_out_produce_data_dev_name,
                    g_config.sdx_config.sdx_ip_str,
                    dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL));
    }

    strncpy(rec->filename_finished, file_name, len);
    strcpy(file_name + len, ".tmp");

    if ((fp = fopen(file_name, "w")) == NULL)
    {
        DPI_LOG(DPI_LOG_ERROR, "Error open: %s, %s", file_name, strerror(errno));
        return -1;
    }

    rec->fp          = fp;
    rec->create_time = g_config.g_now_time;
    rec->write_bytes = 0;

    return 0;
}


static void SdxRecIPRule_uninit(SdxRecIPRule *rec)
{
    if (!rec || !rec->fp)
        return;

    fclose(rec->fp);

    //空文件删除
    if(0 == rec->write_bytes) {
        remove(rec->filename_writing);
    } else {
        if (rename(rec->filename_writing, rec->filename_finished) == -1) {
            DPI_LOG(DPI_LOG_ERROR, "Error rename: %s, \"%s\"", rec->filename_writing, strerror(errno));
        }
    }

    rec->fp = NULL;
    rec->create_time = 0;
    rec->write_bytes = 0;
    memset(rec->filename_writing, 0, FILENAME_MAX);
    memset(rec->filename_finished, 0, FILENAME_MAX);
}


static int SdxRecIPRule_write_one_record(SdxRecIPRule *rec, struct stu_rule_pcap  *pcap_status, struct TaskPropertyExtract *extra) // struct sdt_out_status_in_group *group)
{
    SdtMatchResult *match_result = extra->fn_match_result(extra->self);

    if (match_result == NULL || rec == NULL)
    {
        return -1;
    }

    if (rec->fp == NULL)
    {
        // 写时创建, 避免产生空文件
        if (SdxRecIPRule_init(rec) != 0)
            return -1;
    }

    char   *buff     = (char*)dpi_malloc(MAX_CONTENT_SIZE);
    int     buff_len = 0;
    memset(buff, 0, MAX_CONTENT_SIZE);

    /** <SDX-测试大纲> B.6 要求字段 */
    cJSON   *elem = cJSON_CreateObject();
    cJSON_AddNumberToObject(elem, "DBEGINTIME",     pcap_status->pcap_time);
    cJSON_AddStringToObject(elem, "NGROUPNO",       match_result->groupID);
    cJSON_AddStringToObject(elem, "SGROUPNAME",     match_result->groupName);

    uint32_t HW[4] = {0};
    sdx_convert_linename(extra->fn_mac_hdr(extra->self)->Datasrc.Global_LineNO, buff, HW);
    cJSON_AddNumberToObject(elem, "LINENO1",        HW[0]);
    cJSON_AddNumberToObject(elem, "LINENO2",        HW[1]);
    cJSON_AddNumberToObject(elem, "LINENO3",        HW[2]);
    cJSON_AddNumberToObject(elem, "LINENO4",        HW[3]);
    cJSON_AddStringToObject(elem, "sLineName",      buff);
    cJSON_AddStringToObject(elem, "INFO_TYPE",      "");                   //2024-11-15 思文:牡丹江要求加入
    cJSON_AddStringToObject(elem, "TASK_ID",        match_result->taskID); //2024-11-15 思文:牡丹江要求加入

    extra->fn_rule_id(extra->self, buff, MAX_CONTENT_SIZE);
    cJSON_AddStringToObject(elem, "RULE_ID",        buff);
    cJSON_AddStringToObject(elem, "SSYSFROM",       g_config.web_config.sys_from);
    cJSON_AddNumberToObject(elem, "NFILELENGTH",    pcap_status->pkt_bytes);
    cJSON_AddStringToObject(elem, "SFILEPATH",      pcap_status->pcap_name);
    cJSON_AddStringToObject(elem, "DATAFROM",       g_config.web_config.data_from);
    cJSON_AddStringToObject(elem, "SIGTYPE",        g_config.web_config.sig_type);
    cJSON_AddStringToObject(elem, "reserved",       "");

    int write_len = 0;
    buff_len = 0;
    if (cJSON_PrintPreallocated(elem, buff, MAX_CONTENT_SIZE -5, 0) > 0)
    {
        buff_len = strlen(buff);
        write_len += fwrite(buff, 1, buff_len, rec->fp);
        write_len += fwrite("\n", 1, 1, rec->fp);
    }
    else
    {
        DPI_LOG(DPI_LOG_WARNING, "error cjson print");
    }

    rec->write_bytes += write_len;
    cJSON_Delete(elem);
    dpi_free(buff);

    //磁盘已满?
    if(write_len != (buff_len + 1))
    {
        DPI_LOG(DPI_LOG_ERROR, "文件无法写入 %s", rec->filename_writing);
    }
    else
    {
        fflush(rec->fp);
    }

    return 0;
}


static void SdxRecIPRule_rotate(SdxRecIPRule *rec)
{
    if (!rec || !rec->fp)
        return;

    uint32_t json_max_size = g_config.web_config.json_size ? g_config.web_config.json_size : 1 * 1024 * 1024;     // 默认 1M
    uint32_t json_max_time = g_config.web_config.json_truncation ? g_config.web_config.json_truncation : 60 * 60; // 默认1小时

    if (rec->write_bytes >= json_max_size
      || (g_config.g_now_time - rec->create_time) >= json_max_time)
    {
        SdxRecIPRule_uninit(rec);
    }
}


/** 共用方法 **/
static
int _sdt_create_pcap(struct stu_rule_pcap *pcap_status, SdtMatchResult *match_result, int thread_index, const uint8_t line_no[16])
{
    /* --目录规则  /单位/任务号/规则组名称/日期（天）/线路名称_日期（秒）_IP地址_线程号_序号.pcap  
        例如：/57302/CC01#CMCX###202411070027/14094323_bjk-FTP-202411/20241125/[JXC-01]YT-3-119-W-R-U2#4F$3@1-64VC4C_20241125221317_109.8.150.110_1_018.pcap
    */
    char pcap_out_dir[COMMON_FILE_PATH]={0};
    char pcap_tmp_name[COMMON_FILE_PATH]={0};
    snprintf(pcap_out_dir, COMMON_FILE_PATH,
                "%s/%s/%s/%s_%s/%.8s",
                g_config.sdt_out_pcap_dir,
                match_result->unitID,
                match_result->taskID,
                match_result->groupID,
                match_result->groupName,
                time_to_datetime(g_config.g_now_time));
    mkdirs(pcap_out_dir);

    // 设置文件名
    if(line_no){
        uint32_t HW[4] = {0,0,0,0};
        char     buff_LINENAME1[512] = {0};
        sdx_convert_linename(line_no, buff_LINENAME1, HW);

        snprintf(pcap_status->pcap_name, COMMON_FILE_PATH,
                "%s/%s-%s-%s-%d-%03u.pcap",
                pcap_out_dir,
                buff_LINENAME1,
                time_to_datetime(g_config.g_now_time),
                g_config.sdx_config.sdx_ip_str,
                thread_index+1,
                dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL));
    } else {
        snprintf(pcap_status->pcap_name, COMMON_FILE_PATH,
                "%s/%s_%s_%s_%u_%u.pcap",
                pcap_out_dir,
                g_config.sdt_pcap_identification,
                time_to_datetime(g_config.g_now_time),
                g_config.sdx_config.sdx_ip_str,
                thread_index+1, //标书中要求 默认从1 开始
                dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL));
    }

    snprintf(pcap_tmp_name, COMMON_FILE_PATH, "%s.tmp", pcap_status->pcap_name);

    // 创建文件
    pcap_status->pcap_fp = fopen(pcap_tmp_name, "w");
    if(NULL == pcap_status->pcap_fp){
        DPI_LOG(DPI_LOG_ERROR, "创建 [%s] 失败", pcap_tmp_name);
        perror(pcap_tmp_name);
        return -1;
    }

    // 生成pcap文件的头部
    struct pcap_file_header fh;
    memset(&fh, 0, sizeof(struct pcap_file_header));
    fh.magic         = 0xA1B2C3D4;
    fh.version_major = PCAP_VERSION_MAJOR;
    fh.version_minor = PCAP_VERSION_MINOR;
    fh.snaplen       = 0XFFFFFFFF;
    fh.linktype      = DLT_EN10MB;

    /* 写入pcap头部 */
    fwrite(&fh, sizeof(struct pcap_file_header), 1, pcap_status->pcap_fp);
    pcap_status->pcap_time = (uint32_t)g_config.g_now_time;
    pcap_status->pkt_bytes = sizeof(struct pcap_file_header);
    return 0;
}

static
void _sdt_fragment_pcap(SdxOutThreadCtx *sdx_out_ctx, struct stu_rule_pcap *pcap_status, struct TaskPropertyExtract *extra)
{
    fclose(pcap_status->pcap_fp);
    pcap_status->pcap_fp=NULL;

    char writing_name[COMMON_FILE_PATH]={0};
    snprintf(writing_name, COMMON_FILE_PATH,"%s.tmp", pcap_status->pcap_name);
    rename(writing_name, pcap_status->pcap_name);

    // 生成pcap索引文件
    SdxRecIPRule_write_one_record(&sdx_out_ctx->sdx_rec_rule, pcap_status, extra);

    pcap_status->pkt_bytes = 0;
    pcap_status->pcap_time = (uint32_t)g_config.g_now_time;
    memset(pcap_status->pcap_name, 0, sizeof(pcap_status->pcap_name));

    g_sdx_output_thread_status[sdx_out_ctx->ring_id].pcap_num++;
}


/** 以 规则组 为单位输出 pcap 函数实现
 * 1) pcap文件句柄的所有权归 规则组对象(group_node)
 * 2) 同一规则组内的所有 规则 引用规则组的pcap句柄
 * 3) 
 **/
#if 1
struct rte_hash *g_sdt_group_hash_db = NULL;

struct sdt_out_status_in_group*
sdt_group_hash_lookup_or_create(const char *uintid, const char *taskid, const char *groupid)
{
    struct sdt_out_status_in_group  *group_node;
    char group_key[SDT_RULE_KEY_SIZE] = {0};

    snprintf(group_key, SDT_RULE_KEY_SIZE, "%s_%s_%s", uintid, taskid, groupid);
    int retval = rte_hash_lookup_data(g_sdt_group_hash_db, (const void*)group_key, (void**)&group_node);

    if (retval >= 0) {
        return group_node;
    }

    group_node = dpi_malloc(sizeof(*group_node));
    memset(group_node, 0, sizeof(*group_node));

    retval = rte_hash_add_key_data(g_sdt_group_hash_db, (const void *)group_key, (void *)group_node);
    if (retval < 0)
    {
        DPI_LOG(DPI_LOG_ERROR, "group:%s insert hash db failed! hash table numbers:%d",
                                groupid,
                                rte_hash_count(g_sdt_group_hash_db));
        free(group_node);
        return NULL;
    }

    group_node->rule_nodes = g_ptr_array_new();
    group_node->pcap_status.max_pcap_size = g_config.web_config.pcap_size;
    group_node->pcap_status.max_time      = g_config.web_config.pcap_truncation;

    return group_node;
}

static
void sdt_group_hash_db_clean_(void)
{
    struct sdt_out_status_in_group *group;

    char            key[SDT_RULE_KEY_SIZE];
    uint32_t        iter = 0;
    int             retval=0;

    if (g_sdt_group_hash_db == NULL)
        return;

    while (rte_hash_iterate(g_sdt_group_hash_db, (void *) key,
            (void *) &group, &iter) >= 0)
    {
        retval = rte_hash_del_key(g_sdt_hash_db, (const void *)key);
        if (retval < 0 || group == NULL)
        {
            continue;
        }
        // 解除 group_node 与 每个 rule_node 的关联
        for (guint index=0; index < group->rule_nodes->len; index++)
        {
            sdt_out_status *rule_node = (sdt_out_status*)g_ptr_array_index(group->rule_nodes, index);
            rule_node->group_node = NULL;
            rule_node->pcap_status_ptr = NULL;
        }
        g_ptr_array_remove_range(group->rule_nodes, 0, group->rule_nodes->len);
        g_ptr_array_free(group->rule_nodes, FALSE);
        free(group);
    }
}

static
SdtMatchResult* _extract_match_result_from_group(struct sdt_out_status_in_group *group)
{
    sdt_out_status *rule_node = (sdt_out_status *)g_ptr_array_index(group->rule_nodes, 0);

    return rule_node->match_result;
}

static
void _extract_rule_id_from_group(struct sdt_out_status_in_group *group, char *buff, int max_len)
{
    sdt_out_status *rule_node;

    int buff_len = 0;    
    buff[0]  = '\0';

    for (guint index=0; index < group->rule_nodes->len && buff_len < max_len; index++)
    {
        rule_node = (sdt_out_status *)g_ptr_array_index(group->rule_nodes, index);
        if (index > 0)
            buff_len += snprintf(buff+buff_len, max_len-buff_len, ",%u", rule_node->match_result->ruleId);
        else
            buff_len = snprintf(buff, max_len, "%u", rule_node->match_result->ruleId);
    }
}

static
struct mac_packet_header* _extract_mac_hdr_from_group(struct sdt_out_status_in_group *group)
{
    return &group->first_mac_hdr;
}

static
int sdt_create_file_in_group(sdt_out_status  *rule_node, struct packet_stream  *pkt_elm, int thread_index)
{
    int ret = 0;
    struct sdt_out_status_in_group  *group_node;
    SdtMatchResult *match_result = rule_node->match_result;

    if (rule_node->group_node == NULL)
    {
        group_node = sdt_group_hash_lookup_or_create(match_result->unitID, match_result->taskID, match_result->groupID);
        if (group_node == NULL)
        {
            return -1;
        }

        // rule_node 与 group_node 互相关联
        // group->rule_nodes 持有所有的 rule 节点
        g_ptr_array_add(group_node->rule_nodes, rule_node);

        // rule->group_node 指向所属的 group 节点
        rule_node->group_node       = group_node;
        rule_node->pcap_status_ptr  = &group_node->pcap_status;
    }
    else
    {
        group_node = rule_node->group_node;
    }

    if (group_node->pcap_status.pcap_fp == NULL)
    {
        if (1 == g_config.sdx_config.sdx_mac_packet_header_flag)
        {
            memcpy(&group_node->first_mac_hdr, pkt_elm->pkt_data, sizeof(struct mac_packet_header));
            ret = _sdt_create_pcap(&group_node->pcap_status, match_result, thread_index,
                        group_node->first_mac_hdr.Datasrc.Global_LineNO);
        }
        else
        {
            ret = _sdt_create_pcap(&group_node->pcap_status, match_result, thread_index, NULL);
        }
    }

    return ret;
}

static
void sdt_out_pcap_fragment_in_group(SdxOutThreadCtx *sdx_out_ctx, sdt_out_status  *rule_node)
{
    struct sdt_out_status_in_group *group = rule_node->group_node;

    if (group == NULL || group->pcap_status.pcap_fp == NULL) {
        return;
    }

    struct TaskPropertyExtract extra = {
        .self = group,
        .fn_mac_hdr = (struct mac_packet_header* (*)(void *))_extract_mac_hdr_from_group,
        .fn_match_result = (SdtMatchResult* (*)(void *))_extract_match_result_from_group,
        .fn_rule_id = (void (*)(void *, char *, int))_extract_rule_id_from_group
    };

    _sdt_fragment_pcap(sdx_out_ctx, &group->pcap_status, &extra);

    // 解除 group_node 与 每个 rule_node 的关联
    for (guint index=0; index < group->rule_nodes->len; index++)
    {
        sdt_out_status *rule_node = (sdt_out_status*)g_ptr_array_index(group->rule_nodes, index);
        rule_node->group_node = NULL;
        rule_node->pcap_status_ptr = NULL;
    }
    g_ptr_array_remove_range(group->rule_nodes, 0, group->rule_nodes->len);
}

#endif


/** 以 规则 为单位输出 pcap 函数实现
 **/
#if 1
static
void _extract_rule_id_from_rule(sdt_out_status *rule_node, char *buff, int max_len)
{
    SdtMatchResult *match_result = rule_node->match_result;

    snprintf(buff, max_len, "%u", match_result->ruleId);
}

static
struct mac_packet_header* _extract_mac_hdr_from_rule(sdt_out_status *rule_node)
{
    return &rule_node->first_mac_hdr;
}

static
SdtMatchResult* _extract_match_result_from_rule(sdt_out_status *rule_node)
{
    return rule_node->match_result;
}

static
int sdt_create_file_in_rule(sdt_out_status  *rule_node, struct packet_stream  *pkt_elm, int thread_index)
{
    int ret = 0;
    SdtMatchResult *match_result = rule_node->match_result;

    if (1 == g_config.sdx_config.sdx_mac_packet_header_flag)
    {
        memcpy(&rule_node->first_mac_hdr, pkt_elm->pkt_data, sizeof(struct mac_packet_header));
        ret = _sdt_create_pcap(&rule_node->pcap_status, match_result, thread_index,
                    rule_node->first_mac_hdr.Datasrc.Global_LineNO);
    }
    else
    {
        ret = _sdt_create_pcap(&rule_node->pcap_status, match_result, thread_index, NULL);
    }

    if (ret == 0)
        rule_node->pcap_status_ptr = &rule_node->pcap_status;

    return ret;
}

static
void sdt_out_pcap_fragment_in_rule(SdxOutThreadCtx *sdx_out_ctx, sdt_out_status  *rule_node)
{
    if (rule_node->pcap_status_ptr == NULL || rule_node->pcap_status_ptr->pcap_fp == NULL){
        return;
    }

    struct TaskPropertyExtract extra = {
        .self = rule_node,
        .fn_mac_hdr = (struct mac_packet_header* (*)(void *))_extract_mac_hdr_from_rule,
        .fn_match_result = (SdtMatchResult* (*)(void *))_extract_match_result_from_rule,
        .fn_rule_id = (void (*)(void *, char *, int))_extract_rule_id_from_rule
    };

    _sdt_fragment_pcap(sdx_out_ctx, &rule_node->pcap_status, &extra);
    rule_node->pcap_status_ptr = NULL;
}
#endif



static int
sdt_out_init(void)
{
    int i;
    struct rte_hash_parameters sdt_rules_hash_para = {
        .name      = "sdt_rules_status",
        .entries   = g_config.sdt_rule_max_num,
        .key_len   = SDT_RULE_KEY_SIZE,
        .hash_func = rte_jhash,
        .hash_func_init_val = 0,
        .socket_id = g_config.socketid,
    };

    g_sdt_hash_db = rte_hash_create(&sdt_rules_hash_para);
    if (g_sdt_hash_db == NULL){
        DPI_LOG(DPI_LOG_ERROR, "create g_sdt_hash_db failed!");
        return -1;
    }

    if (g_config.sdx_create_pcapfile_style == 0)
    {
        sdt_create_pcap_file = sdt_create_file_in_rule;
        sdt_fragment_pcap_file = sdt_out_pcap_fragment_in_rule;
    }
    else
    {
        // 参数复用
        sdt_rules_hash_para.name = "sdt_group_status";
        sdt_rules_hash_para.entries = 1000000;
        g_sdt_group_hash_db = rte_hash_create(&sdt_rules_hash_para);
        if (g_sdt_group_hash_db == NULL){
            DPI_LOG(DPI_LOG_ERROR, "create g_sdt_group_hash_db failed!");
            return -1;
        }
        sdt_create_pcap_file = sdt_create_file_in_group;
        sdt_fragment_pcap_file = sdt_out_pcap_fragment_in_group;
    }

    for (i = 0; i < g_config.sdt_out_thead_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "sdtout_ring_%d_%d",
                                                g_config.socketid,
                                                i);
        sdt_out_ring[i] = rte_ring_create(ring_name,
                                          g_config.sdt_out_ring_size,
                                          g_config.socketid,
                                          RING_F_SC_DEQ);
        if (sdt_out_ring[i] == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "error while create tbl ring\n");
            return -1;
        }
        if (rte_ring_lookup(ring_name) != sdt_out_ring[i]) {
            DPI_LOG(DPI_LOG_ERROR, "SDT OUT Cannot lookup ring from its name[%s]\n",
                                                                         ring_name);
            return -1;
        }
    }

    return 0;
}


static int
sdt_out_event_fragment(sdt_out_status           *out_elem)
{
    if(NULL==out_elem->event_status.event_fp){
        return 0;
    }

    fclose(out_elem->event_status.event_fp);
    out_elem->event_status.event_fp=NULL;

    char fininsh_name[COMMON_FILE_PATH]={0};
    strncpy(fininsh_name,
            out_elem->event_status.event_name,
            strlen(out_elem->event_status.event_name)-strlen(".tmp"));

    rename(out_elem->event_status.event_name, fininsh_name);
    memset(out_elem->event_status.event_name,0,sizeof(out_elem->event_status.event_name));

    return 1;
}


static int
sdt_out_syslog_fragment(sdt_out_status           *out_elem)
{
    if(NULL==out_elem->syslog_status.syslog_fp){
        return 0;
    }

    fclose(out_elem->syslog_status.syslog_fp);
    out_elem->syslog_status.syslog_fp=NULL;

    char finish_name[COMMON_FILE_PATH]={0};
    strncpy(finish_name,
            out_elem->syslog_status.syslog_name,
            strlen(out_elem->syslog_status.syslog_name)-strlen(".tmp"));

    rename(out_elem->syslog_status.syslog_name, finish_name);
    memset(out_elem->syslog_status.syslog_name,0,sizeof(out_elem->syslog_status.syslog_name));

    return 1;
}


static int
sdt_out_pcap(SdxOutThreadCtx *sdx_out_ctx, sdt_out_status  *out_elem, struct packet_stream  *pkt_elm)
{
    struct stu_rule_pcap  *pcap_status;

    if(NULL == out_elem->pcap_status_ptr || NULL == out_elem->pcap_status_ptr->pcap_fp) /* pcap 文件还没创建，创建pcap文件并写入头*/
    {
        if (sdt_create_pcap_file(out_elem, pkt_elm, pkt_elm->work_on_ringId))
        {
            DPI_LOG(DPI_LOG_ERROR, "PCAP创建失败");
            return -1;
        }
    }

    pcap_status = out_elem->pcap_status_ptr;

    int ret=0;
    struct pcappkt_hdr    pckt_header;

    int offset = 0;
    if(1==g_config.sdx_config.sdx_mac_packet_header_flag && 0==g_config.sdx_config.dump_pcap_with_sdx_mac)
    {
        offset = 51;
    }

    uint32_t caplen     =  DPI_MIN(pkt_elm->pkt_data_len, TCP_PAYLOAD_MAX_LEN);
    pckt_header.caplen  =  caplen - offset;
    pckt_header.len     =  pkt_elm->pkt_data_len - offset;

    if(READ_FROM_PCAP == g_config.data_source){
        pckt_header.tv_sec  = (uint32_t)(pkt_elm->timestamp/1e6);
        pckt_header.tv_usec = (uint32_t)(pkt_elm->timestamp - pckt_header.tv_sec*1000000);
    }else{
        pckt_header.tv_sec  = (uint32_t)g_config.g_now_time;
        pckt_header.tv_usec = (uint32_t)(g_config.g_now_time_usec-g_config.g_now_time* 1000000);
    }

    /* 写每个报文的的头 */
    /*写入报文帧*/
    ret = fwrite(&pckt_header, 1, sizeof(struct pcappkt_hdr), pcap_status->pcap_fp);
    ret+= fwrite(pkt_elm->pkt_data+offset, 1, caplen-offset, pcap_status->pcap_fp);

    //写入量判断
    if(ret == (typeof(ret))(sizeof(struct pcappkt_hdr) + caplen - offset))
    {
        uint32_t per_pkt_len = sizeof(struct pcappkt_hdr) + caplen - offset;
        pcap_status->pkt_bytes += per_pkt_len;
        fflush(pcap_status->pcap_fp); //及时输出
        g_sdx_output_thread_status[pkt_elm->work_on_ringId].packets_in_pcap ++;
        g_sdx_output_thread_status[pkt_elm->work_on_ringId].bytes_in_pcaps += per_pkt_len;
    }else{
        log_warn("报文帧无法写入");
        return -1;
    }

    if(pcap_status->max_pcap_size > 0 && pcap_status->pkt_bytes>=pcap_status->max_pcap_size)
    { /* 配置每个pcap大小，达到该大小则分包 */
        sdt_fragment_pcap_file(sdx_out_ctx, out_elem);
        // char pcap_tmp_filename[COMMON_FILE_PATH]={0};
        // snprintf(pcap_tmp_filename, COMMON_FILE_PATH, "%s", out_elem->pcap_status->pcap_name);
        //发送“数据采集文件”消息到kafka
        // forward_collect_send_kafka(out_elem, pkt_elm, pcap_tmp_filename);
        // out_elem->pcap_status->pkt_bytes=0;
    }
    else if(pcap_status->max_time > 0 && pcap_status->pcap_time+pcap_status->max_time < g_config.g_now_time)
    { /* 配置了每个pcap写时间，则达到该时间分包*/
        sdt_fragment_pcap_file(sdx_out_ctx, out_elem);
        // char pcap_tmp_filename[COMMON_FILE_PATH]={0};
        // snprintf(pcap_tmp_filename, COMMON_FILE_PATH, "%s", out_elem->pcap_status->pcap_name);
        // //发送“数据采集文件”消息到kafka
        // forward_collect_send_kafka(out_elem, pkt_elm, pcap_tmp_filename);
    }

    return 0;
}

static int
sdt_out_event(sdt_out_status  *out_elem, struct packet_stream  *pkt_elem)
{
    if(0==isUTF8((const char*)pkt_elem->pkt_data, strlen((char *)pkt_elem->pkt_data))){
        return -1;
    }
    int ret=0;

    if(NULL==out_elem->event_status.event_fp){

        char event_rule_dir[COMMON_FILE_PATH]={0};
        snprintf(event_rule_dir,COMMON_FILE_PATH,"%s/%s/%s_%s/event/%.8s",
                                        g_config.sdt_out_event_dir,
                                        out_elem->match_result->taskID,
                                        out_elem->match_result->groupID,
                                        out_elem->match_result->groupName,
                                        time_to_datetime(g_config.g_now_time));
        mkdirs(event_rule_dir);
        // 设置event文件名
        snprintf(out_elem->event_status.event_name, COMMON_FILE_PATH,
                                      "%s/%s_%u_%s_%s_%u_%u.%s.tmp",
                                      event_rule_dir,
                                      g_config.sdt_out_produce_data_dev_name,
                                      pkt_elem->rule_id,        //把 ruleID 带在文件名上
                                      time_to_datetime(g_config.g_now_time),
                                      g_config.sdx_config.sdx_ip_str,
                                      gettid(),
                                      dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL),
                                      g_config.yv_data_suffix);
        printf("sdt_event name:%s\n",out_elem->event_status.event_name);
        // 创建文件
        out_elem->event_status.event_fp = fopen(out_elem->event_status.event_name, "w");
        if(NULL==out_elem->event_status.event_fp){
            perror(out_elem->event_status.event_name);
            return 0;
        }
        out_elem->event_status.event_cnt=0;
    }

    ret+=fwrite(pkt_elem->pkt_data, 1, pkt_elem->pkt_data_len, out_elem->event_status.event_fp);
    fwrite("\n", 1, 1, out_elem->event_status.event_fp);
    fflush(out_elem->event_status.event_fp);
    if(ret!=pkt_elem->pkt_data_len){
        perror(__func__);
        return -1;
    }

    out_elem->event_status.event_time=g_config.g_now_time;
    out_elem->event_status.event_cnt++;

    /* 大小和时间互斥，1.有大小没时间，2.没大小有时间*/
    if(out_elem->event_status.max_event_num>0){/* 配置每个pcap大小，达到该大小则分包 */
        if(out_elem->event_status.event_cnt>out_elem->event_status.max_event_num){
            sdt_out_event_fragment(out_elem);
            out_elem->event_status.event_cnt=0;
        }
    }
    return 0;
}

static int
sdt_out_syslog(sdt_out_status  *out_elem, struct packet_stream  *pkt_elem)
{
    int ret=0;

    if(NULL==out_elem->syslog_status.syslog_fp){
        char syslog_rule_dir[COMMON_FILE_PATH]={0};
        char syslog_tmp_name[COMMON_FILE_PATH]={0};
        snprintf(syslog_rule_dir,COMMON_FILE_PATH,"%s/%s/%s_%s/alarm/%.8s",
                                        g_config.sdt_out_syslog_dir,
                                        out_elem->match_result->taskID,
                                        out_elem->match_result->groupID,
                                        out_elem->match_result->groupName,
                                        time_to_datetime(g_config.g_now_time));
        mkdirs(syslog_rule_dir);
        // 设置alarm文件名
        snprintf(out_elem->syslog_status.syslog_name, COMMON_FILE_PATH,
                                      "%s/%s_%u_%s_%s_%u_%u.%s.tmp",
                                      syslog_rule_dir,
                                      g_config.sdt_out_produce_data_dev_name,
                                      pkt_elem->rule_id,        //把 ruleID 带在文件名上
                                      time_to_datetime(g_config.g_now_time),
                                      g_config.sdx_config.sdx_ip_str,
                                      gettid(),
									  dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL),
									  g_config.yv_data_suffix);

        log_trace("sdt_syslog name:%s\n",out_elem->syslog_status.syslog_name);
        // 创建文件
        out_elem->syslog_status.syslog_fp = fopen(out_elem->syslog_status.syslog_name, "w");
        if(NULL==out_elem->syslog_status.syslog_fp){
            perror(out_elem->syslog_status.syslog_name);
            return -1;
        }

        fwrite(pkt_elem->pkt_data, pkt_elem->pkt_data_len, 1, out_elem->syslog_status.syslog_fp);
        fflush(out_elem->syslog_status.syslog_fp);
        out_elem->syslog_status.syslog_time=g_config.g_now_time;
        out_elem->syslog_status.syslog_cnt=0;
    }else{
        fwrite(pkt_elem->pkt_data, pkt_elem->pkt_data_len, 1, out_elem->syslog_status.syslog_fp);
        fflush(out_elem->syslog_status.syslog_fp);
    }
    out_elem->syslog_status.syslog_cnt++;

    /* 大小和时间互斥，1.有大小没时间，2.没大小有时间*/
    if(g_config.log_max_num>0){
        if(out_elem->syslog_status.syslog_cnt>g_config.log_max_num){
            sdt_out_syslog_fragment(out_elem);
            out_elem->syslog_status.syslog_cnt=0;
        }
    }

    return 0;
}



static int
sdt_out_process(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream  *pkt_elem)
{
    sdt_out_status *rule_status=NULL;
    rule_status=sdt_rule_hash_lookup_key(pkt_elem->unitID, pkt_elem->taskID, pkt_elem->groupID, pkt_elem->rule_id);
    if(NULL==rule_status)
    {
        DPI_LOG(DPI_LOG_ERROR, "sdt_rule_hash_lookup_key [%s][%s][%s][%u]", pkt_elem->unitID, pkt_elem->taskID, pkt_elem->groupID, pkt_elem->rule_id);
        return -1;
    }
    rte_atomic64_inc(&sdt_pcap_out_pkts);

    //互斥量 - 先 FETCH 再ADD  -- 一次只允许单个线程操作(一个线程在删除/其他线程在查询)
    sdt_atomic_lock(&rule_status->in_used);
    //互斥量 需要配对使用, 下面代码不可以直接 return

    int ret = -1;

    for(int chunli = 0; chunli < 8 *(int)sizeof(pkt_elem->action_type); chunli++)
    {
        uint32_t action = 1 << chunli;
        if(pkt_elem->action_type & action)
        {
            switch(action)
            {
                case SAE_packetDump:
                    ret = sdt_out_pcap(sdx_out_ctx, rule_status, pkt_elem);
                    break;

                case SAE_event:
                    ret = sdt_out_event(rule_status, pkt_elem);
                    break;

                default:
                    break;
            }
        }
    }

    if(SDT_OUT_ACTUAL_DATA_SYSLOG==pkt_elem->data_type)
    {
        sdt_out_syslog(rule_status, pkt_elem);
    }

    //更改互斥量
    sdt_atomic_unlock(&rule_status->in_used);
    return ret;
}

//SDX 项目 带51字节头
int sdt_out_forward_mac(struct packet_stream *pkt_stream)
{
    if(0 == (SAE_packetDump & pkt_stream->action_type))
    {
        return -1;
    }

    uint8_t     dst_mac[6];
    uint64_t rss = pkt_stream->flow_id;

    uint8_t mac_src[6];
    uint8_t mac_dst[6];
    struct rte_ether_addr ports_eth_addr;


	// 1. 配置 MAC
	int ret;
	int ethdr_size = sizeof(struct mac_packet_header);
	int pkt_size = 14 + pkt_stream->pkt_data_len + sizeof(Direct_IP_Custom_Tail); //另外再加14字节头
	uint8_t *p_new_pkt = (uint8_t*)malloc(pkt_size + 1);
	if (!p_new_pkt) {
		return -1;
	}
	memset(p_new_pkt, 0, pkt_size + 1);

	struct mac_packet_header *sdx_ethdr = (struct mac_packet_header *)pkt_stream->pkt_data;

    //xml 文件中具有最高优先级
    if(pkt_stream->mode_param_num > 0)
    {
        //基于 RuleID 同源同宿
        uint8_t index = rss % pkt_stream->mode_param_num;
        memcpy(mac_dst, pkt_stream->mode_param[index], 6);

        if(index < 10)
        {
            index +=0;//第1层
            if(0 == mac_forward_cnt[index].cnt)
            {
                memcpy(mac_forward_cnt[index].mac, mac_dst, 6);
            }
            ATOMIC_FETCH_ADD(&mac_forward_cnt[index].cnt);
        }
    }
    else
    //默认的 MAC池来自 config.ini 中的配置
    if(g_config.sdx_mac_num > 0)
    {
        //基于 RuleID 同源同宿
        uint8_t index = rss % g_config.sdx_mac_num;
        memcpy(mac_dst, g_config.sdx_mac[index], 6);

        if(index < 10)
        {
            index +=10;//第2层
            if(0 == mac_forward_cnt[index].cnt)
            {
                memcpy(mac_forward_cnt[index].mac, mac_dst, 6);
            }
            ATOMIC_FETCH_ADD(&mac_forward_cnt[index].cnt);
        }
    }
    else
    {
        printf  ("WARN: 帧转发 mode_param_num %u\n", pkt_stream->mode_param_num);
        log_warn("WARN: 帧转发 mode_param_num %u", pkt_stream->mode_param_num);
        return -1;
    }

    //获取网卡MAC
    struct rte_ether_addr dev_mac_src;
    int portid = rss % g_config.sdx_config.sdx_tx_port_num;
    rte_eth_macaddr_get(portid, &ports_eth_addr);
    rte_ether_addr_copy(&ports_eth_addr, &dev_mac_src);

    //SMAC 填充 IP
    //填充 task_sub_type 到 SMAC的倒数第2个字节
    memcpy(mac_src, dev_mac_src.addr_bytes, 6);
    mac_src[5] = (0x000000ff & g_config.sdx_config.sdx_ip_number);
    mac_src[4] = pkt_stream->task_sub_type;

    int offset = 0;
    memcpy(p_new_pkt+offset, mac_dst, 6);   offset+=6;
    memcpy(p_new_pkt+offset, mac_src, 6);   offset+=6;
    memcpy(p_new_pkt+offset, "\x08\x00", 2);offset+=2;
    memcpy(p_new_pkt+offset, pkt_stream->pkt_data, pkt_stream->pkt_data_len);

    // 2. 增加自定义trailer
    Direct_IP_Custom_Tail tail;
    memset(&tail, 0, sizeof(Direct_IP_Custom_Tail));

    memcpy(tail.Lineno, sdx_ethdr->Datasrc.Global_LineNO, sizeof(sdx_ethdr->Datasrc.Global_LineNO));
    tail.TimeSpan  = ((uint32_t)sdx_ethdr->TimeStamp[0]) << 24;
    tail.TimeSpan |= ((uint32_t)sdx_ethdr->TimeStamp[1]) << 16;
    tail.TimeSpan |= ((uint32_t)sdx_ethdr->TimeStamp[2]) << 8;
    tail.TimeSpan |= ((uint32_t)sdx_ethdr->TimeStamp[3]);
    tail.Eth_Len  = sdx_ethdr->bLinkLen;
    tail.Eth_Type = sdx_ethdr->bLinkType;
    tail.Group_ID = atoi(pkt_stream->groupID);
    memcpy(tail.TaskID, pkt_stream->taskID, TASK_ID_LENGTH);

    //14字节头+51字节头+报文帧+98字节Direct_IP_Custom_Tail  // 2024.12.05 牡丹江现场对接-改正
    memcpy(p_new_pkt + pkt_stream->pkt_data_len+offset , &tail, sizeof(Direct_IP_Custom_Tail));

    // 3. 进行转发操作
    int ok_num = 0;
    int fail_num = 0;
    ret = do_forward(portid, p_new_pkt, pkt_size, &ok_num, &fail_num);

    free((void*)p_new_pkt);
    p_new_pkt = NULL;

    rte_atomic64_add(&sdt_pcap_out_forward_ok_pkts, ok_num);
    rte_atomic64_add(&sdt_pcap_out_forward_fail_pkts, fail_num);
    rte_atomic64_add(&sdt_pcap_out_forward_pkts, ok_num + fail_num);

    g_sdx_output_thread_status[pkt_stream->work_on_ringId].packets_in_mac ++;
    g_sdx_output_thread_status[pkt_stream->work_on_ringId].bytes_in_mac += pkt_size;

    return ret;
}

int sdt_clean_rule_data_status(void)
{
    sdt_out_status  *next_hop;
    uint32_t        *rule_key;
    uint32_t        iter = 0;
    int             retval=0;

    SdxOutThreadCtx *ctx_tmp = SdxOutThreadCtx_new();

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        if(0==next_hop->flag){
            continue;
        }

        sdt_atomic_lock(&next_hop->in_used);

        sdt_fragment_pcap_file(ctx_tmp, next_hop);

        sdt_out_event_fragment(next_hop);

        sdt_atomic_unlock(&next_hop->in_used);
    }

    SdxOutThreadCtx_free(ctx_tmp);

    return 1;
}


int sdt_in_pcap(struct flow_info *flow, SdtMatchResult  *pAction, sdt_out_status  *flow_rule_status, const struct pkt_info  *pkt, bool first_packet)
{
    if(NULL==pkt || NULL==pAction){
        return 0;
    }

    uint32_t free_space;
    int ret=0;
    struct packet_stream *elem = malloc(sizeof(struct packet_stream));
    memset(elem, 0, sizeof(struct packet_stream));

    if(pkt->pkt_len<TCP_PAYLOAD_MAX_LEN){

        //解决 forward 没有 eth.type 的问题
        if (g_config.sdt_mac_forward_flag) {
            elem->mac_flag = 1;
            memcpy(elem->mac_hdr, pkt->raw_pkt, sizeof(elem->mac_hdr));
        }

        memcpy(elem->pkt_data, pkt->ethhdr, pkt->pkt_len);
        strncpy(elem->unitID, pAction->unitID, sizeof(elem->unitID));
        strncpy(elem->taskID, pAction->taskID, sizeof(elem->taskID));
        strncpy(elem->groupID,pAction->groupID,sizeof(elem->groupID));
        strncpy(elem->method, pAction->method, sizeof(elem->method));
        strncpy(elem->topicName,pAction->topicName,sizeof(elem->topicName));

        elem->rule_mode       = pAction->rule_mode;
        elem->task_mode       = pAction->task_mode;
        elem->task_sub_type   = pAction->task_sub_type;
        elem->mode_param_num  = pAction->mode_param_num;
        memcpy(elem->mode_param, pAction->mode_param, sizeof(pAction->mode_param));

        elem->action_type     = SAE_packetDump;
        elem->rule_id         = pAction->ruleId;
        elem->data_type       = SDT_OUT_ACTUAL_DATA_DUMP_PCAP;
        elem->thread_id       = flow->thread_id;
        elem->flow_id         = flow->flow_id;
        elem->timestamp       = flow->timestamp;
        elem->flow_cycle      = flow->flow_cycle;
        elem->first_packet    = first_packet;
        elem->pkt_data_len    = pkt->pkt_len;

        memcpy(elem->pkt_data, pkt->raw_pkt, pkt->pkt_len);

        int group_sum = 0;
        for (uint32_t i=0; i<strlen(pAction->groupID); i++) {
            group_sum += pAction->groupID[i];
        }

        // int ring_id = rte_atomic64_read(&sdt_syslog_fail_pkts) % g_config.sdt_out_thead_num;
        int ring_id = group_sum % g_config.sdt_out_thead_num;
        ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&elem, 1, &free_space);
        if (ret < 1) {
            free(elem);
            rte_atomic64_inc(&sdt_pcap_fail_pkts);
            return 0;
        }else{
            rte_atomic64_inc(&sdt_pcap_success_pkts);
        }
    }else{
        DPI_LOG(DPI_LOG_WARNING, "sdt_in_pcap pkt_len %u", pkt->pkt_len);
        free(elem);
    }

    return 1;
}

int sdt_in_event(struct flow_info *flow, SdtMatchResult  *sdt_act, int direction)
{
    uint32_t free_space;
    int ret=0;
    struct packet_stream *elem = malloc(sizeof(struct packet_stream));
    memset(elem, 0, sizeof(struct packet_stream));

    strncpy(elem->unitID, sdt_act->unitID, sizeof(elem->unitID));
    strncpy(elem->taskID, sdt_act->taskID, sizeof(elem->taskID));
    strncpy(elem->groupID,sdt_act->groupID,sizeof(elem->groupID));
    strncpy(elem->method, sdt_act->method, sizeof(elem->method));
    strncpy(elem->topicName,sdt_act->topicName,sizeof(elem->topicName));

    elem->rule_mode       = sdt_act->rule_mode;
    elem->task_mode       = sdt_act->task_mode;
    elem->task_sub_type   = sdt_act->task_sub_type;
    elem->mode_param_num  = sdt_act->mode_param_num;
    memcpy(elem->mode_param, sdt_act->mode_param, sizeof(sdt_act->mode_param));

    elem->data_type       = SDT_OUT_ACTUAL_DATA_EVENT;
    elem->action_type     = SAE_event;
    elem->rule_id         = sdt_act->ruleId;
    elem->thread_id       = flow->thread_id;
    elem->flow_id         = flow->flow_id;
    elem->timestamp       = flow->timestamp;
    elem->flow_cycle      = flow->flow_cycle;

    int idx=0;
    memset(elem->pkt_data, 0, sizeof(elem->pkt_data));

    precord_t *record;
    dpi_precord_new_record(record, NULL, NULL);
    if (record)
    {
        write_shared_header(record, TCP_PAYLOAD_MAX_LEN, flow, direction);
        int len = record_2_string(record, (char*)elem->pkt_data, TCP_PAYLOAD_MAX_LEN);
        elem->pkt_data_len = len;
        //立即释放
        sdt_precord_destroy(record);
    }

    int ring_id= rte_atomic64_read(&sdt_syslog_fail_pkts) % g_config.sdt_out_thead_num;
    ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&elem, 1, &free_space);
    if (ret < 1) {
        free(elem);
        rte_atomic64_inc(&sdt_event_fail_pkts);
        return 0;
    }else{
        rte_atomic64_inc(&sdt_event_success_pkts);
    }

    return 1;
}

int record_write_json(precord_t *record, cJSON *json);

int sdt_in_syslog(struct flow_info *flow, SdtMatchResult  *sdt_act)
{
    uint32_t free_space;
    int ret=0;
    struct packet_stream *elem = malloc(sizeof(struct packet_stream));
    memset(elem, 0, sizeof(struct packet_stream));
    strncpy(elem->unitID, sdt_act->unitID, sizeof(elem->unitID));
    strncpy(elem->taskID, sdt_act->taskID, sizeof(elem->taskID));
    strncpy(elem->groupID,sdt_act->groupID,sizeof(elem->groupID));
    strncpy(elem->method, sdt_act->method, sizeof(elem->method));
    strncpy(elem->topicName,sdt_act->topicName,sizeof(elem->topicName));

    elem->rule_mode       = sdt_act->rule_mode;
    elem->task_mode       = sdt_act->task_mode;
    elem->task_sub_type   = sdt_act->task_sub_type;
    elem->mode_param_num  = sdt_act->mode_param_num;
    memcpy(elem->mode_param, sdt_act->mode_param, sizeof(sdt_act->mode_param));

    elem->data_type       = SDT_OUT_ACTUAL_DATA_SYSLOG;
    elem->action_type     = SAE_alert;
    elem->rule_id         = sdt_act->ruleId;
    elem->thread_id       = flow->thread_id;
    elem->flow_id         = flow->flow_id;
    elem->timestamp       = flow->timestamp;
    elem->flow_cycle      = flow->flow_cycle;

    int idx=0;
    memset(elem->pkt_data, 0, sizeof(elem->pkt_data));

    precord_t *precord = write_sdt_syslog(sdt_act);

    //SYSLOG 存储为 JSON 格式
    cJSON * obj = cJSON_CreateObject();
    record_write_json(precord, obj);
    char *field_val = cJSON_PrintUnformatted(obj);
    uint32_t json_len = strlen(field_val);
    if(json_len < sizeof(elem->pkt_data))
    {
        memcpy(elem->pkt_data, field_val, json_len);
        elem->pkt_data_len = json_len;
    }
    else
    {
        printf("syslog 内容异常, 无法输出\n");
    }
    cJSON_Delete(obj);
    free(field_val);

    int ring_id= rte_atomic64_read(&sdt_syslog_fail_pkts)  % g_config.sdt_out_thead_num;
    ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&elem, 1, &free_space);
    if (ret < 1) {
        free(elem);
        rte_atomic64_inc(&sdt_syslog_fail_pkts);
        return 0;
    }else{
        rte_atomic64_inc(&sdt_syslog_success_pkts);
    }

    return 1;
}

/* event 上报依赖于上报统计和命中统计 */
static int
sdt_collect_report_match_cnt(sdt_out_status *rule_elem, uint64_t *report_cnt,uint64_t *report_mn_cnt, uint64_t *match_hits)
{
    if(NULL==rule_elem){
        return 0;
    }
    int j=0;
    uint64_t   local_report_cnt    = 0;
    uint64_t   local_report_mn_cnt = 0;
    uint64_t   local_match_hits    = 0;
    for(j=0;j<(int)g_config.dissector_thread_num;j++){
        /* 规则命中event上报次数计数 */
        local_report_cnt+=  rule_elem->thread_statistics[j].rule_report_cnt;

        local_report_mn_cnt+=rule_elem->thread_statistics[j].rule_report_mn_cnt;

        /* 规则命中次数计数 */
        local_match_hits+= rule_elem->thread_statistics[j].rule_match_hits;

    }
    *report_cnt=local_report_cnt;
    *match_hits=local_match_hits;
    *report_mn_cnt=local_report_mn_cnt;

    return 1;
}

int sdt_event_handle(struct flow_info *flow, SdtMatchResult *actionMatched, sdt_out_status *get_rule_status,int direction)
{
    if(NULL==actionMatched){
        return -1;
    }

    if(! (SAE_event & actionMatched->action)) {    //只处理event功能
        return -1;
    }

    if(NULL==get_rule_status){
        get_rule_status=sdt_rule_hash_db_lookup(actionMatched);
        if(NULL==get_rule_status){
            return 0;
        }
    }

    int approve = sdt_action_options_approve(actionMatched);
    if(ACTION_APPROVE == approve){
        //printf("ACTION_APPROVE %s %zu %zu  %zu\n", __func__, actionMatched->hitOnCnt, actionMatched->hitOnByte, actionMatched->hitOnTimeStamp);
        get_rule_status->thread_statistics[flow->thread_id].rule_report_cnt++;
        sdt_in_event(flow, actionMatched, direction);
    }

    return 1;
}





/***************************************************SDT 统计相关功能函数 *************************************************/
/*
* 单包模式命中统计
* 单包模式命中统计命中的单包数和大小
*
* 流模式命中统计
* 流模式命中统计要统计流中所有包数和包大小
*
*/


int SdxOutThreadStatus_rate(uint32_t *inc_bytes)
{
    int i;
    uint32_t local_inc_bytes = 0;
    SdxOutThreadStatus *status;

    for (i = 0; i < g_config.sdt_out_thead_num; i++)
    {
        status = g_sdx_output_thread_status + i;

        local_inc_bytes += (status->bytes_in_mac   - status->stats_pre->bytes_in_mac);
        local_inc_bytes += (status->bytes_in_pcaps - status->stats_pre->bytes_in_pcaps);

        status->stats_pre->bytes_in_mac   = status->bytes_in_mac;
        status->stats_pre->bytes_in_pcaps = status->bytes_in_pcaps;
    }

    *inc_bytes = local_inc_bytes;
    return 0;
}


/* 对于单包模式每次命中一次统计一次，适用流模式第一次统计 */
int
sdt_rule_perthread_pkt_statistics(struct flow_info *flow,
                                  SdtMatchResult   *match_result,
                                  sdt_out_status   *flow_rule_status ,
                                  int thread_id, uint8_t flag)
{
    if(NULL ==  match_result){
        return 0;
    }
    if(NULL==flow_rule_status){
        flow_rule_status=sdt_rule_hash_db_lookup(match_result);
        if(NULL==flow_rule_status){
            // 输出错误日志
            return -1;
        }
    }

/*  // 这一段做的事与 sdt_init_rules_to_hash_db 重复
    for(int chunli = 0; chunli < 8 *(int)sizeof(match_result->action); chunli++)
    {
        uint32_t action = 1 << chunli;
        if(match_result->action & action)
        {
            switch(action)
            {
                case SAE_packetDump:
                    {
                        if(match_result->packetDump_args.size>0){
                            flow_rule_status->pcap_status.max_pcap_size=
                                (uint64_t)match_result->packetDump_args.size*1024*1024;
                        }

                        if(match_result->packetDump_args.minute>0){
                            flow_rule_status->pcap_status.max_time =
                                match_result->packetDump_args.minute*60;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }
*/

    if(flow_rule_status->thread_statistics[thread_id].rule_first_match_time<=0){
        flow_rule_status->thread_statistics[thread_id].rule_first_match_time=g_config.g_now_time;
    }
    flow_rule_status->thread_statistics[thread_id].rule_last_match_time=g_config.g_now_time;

    if(flag & 0x01){
        flow_rule_status->thread_statistics[thread_id].rule_match_flows++;
    }
    // 命中次数 +1
    if (flag & 0x02)
        flow_rule_status->thread_statistics[thread_id].hit_number++;

    flow_rule_status->thread_statistics[thread_id].rule_match_hits++;

    //match_result->session_flag 规则是单包模式还是流模式
    if(match_result->match_mode){  /* 命中规则是流模式 link */
        flow_rule_status->thread_statistics[flow->thread_id].rule_match_pkts =
                                    flow->dst2src_packets+flow->src2dst_packets;
        flow_rule_status->thread_statistics[flow->thread_id].rule_match_bytes=
                                    flow->dst2src_bytes+flow->dst2src_bytes;
        // LINK 模式只在首次命中加1
    }else{  /* 命中规则是单包模式 ipff */
        flow_rule_status->thread_statistics[thread_id].rule_match_pkts++;
        flow_rule_status->thread_statistics[thread_id].rule_match_bytes += flow->packet_len;
    }

    return 1;
}


/* 对于非包模式匹配的需要统计流中其他报文数及字节数 */
int
sdt_rule_perthread_flow_statistics( SdtMatchResult   *   sdt_action, sdt_out_status  *flow_rule_status, int thread_id,uint16_t pkt_len)
{
    if(NULL==flow_rule_status){
        flow_rule_status=sdt_rule_hash_db_lookup(sdt_action);
        if(NULL==flow_rule_status){
            // 输出错误日志
            return -1;
        }
    }

    flow_rule_status->thread_statistics[thread_id].rule_match_pkts++;
    flow_rule_status->thread_statistics[thread_id].rule_match_bytes+=pkt_len;

    return 1;
}


struct stu_rule_statistics  g_rule_statistic;

/* 定时汇总统计总量 */
int
sdt_rule_collect_statistics(void)
{
    int i=0,j=0;

    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;

    uint64_t   total_match_pcaps = 0;

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        if(0==next_hop->flag){
            continue;
        }
        if(next_hop->report_mn_sec>0){
            next_hop->time_count++;
        }

        uint8_t     clear_time_count=0;
        uint64_t   local_report_cnt  = 0;
        uint64_t   local_match_hits  = 0;
        uint64_t   local_match_pkts  = 0;
        uint64_t   local_match_bytes = 0;
        uint64_t   local_match_flows = 0;
        uint64_t   local_match_pcaps = 0;
        uint32_t   local_hit_number  = 0;
        uint32_t   local_store_dump_number = 0;
        uint32_t   local_store_meta_number = 0;
        for(j=0;j<(int)g_config.dissector_thread_num;j++){
            /* 规则命中event上报次数计数 */
            local_report_cnt+=  next_hop->thread_statistics[j].rule_report_cnt;
            /* 规则命中次数计数 */
            local_match_hits+= next_hop->thread_statistics[j].rule_match_hits;
            /* 规则命中报文数据计数 */
            local_match_pkts+= next_hop->thread_statistics[j].rule_match_pkts;
            /* 规则命中字节数计数 */
            local_match_bytes+= next_hop->thread_statistics[j].rule_match_bytes;
            /* 规则命中流计数 */
            local_match_flows+= next_hop->thread_statistics[j].rule_match_flows;
            /* 规则命中后 输出的pcap文件*/
            local_match_pcaps += next_hop->thread_statistics[j].rule_match_pcaps;

            local_hit_number += next_hop->thread_statistics[j].hit_number;
            local_store_dump_number += next_hop->thread_statistics[j].store_dump_number;
            local_store_meta_number += next_hop->thread_statistics[j].store_meta_number;

            if(0==next_hop->statistics_current.rule_first_match_time &&
              (0!=next_hop->thread_statistics[j].rule_first_match_time))
            {
                next_hop->statistics_current.rule_first_match_time=
                               next_hop->thread_statistics[j].rule_first_match_time;
            }

            if(0!=next_hop->thread_statistics[j].rule_first_match_time &&
              (next_hop->statistics_current.rule_first_match_time>
               next_hop->thread_statistics[j].rule_first_match_time))
            {
                next_hop->statistics_current.rule_first_match_time=
                               next_hop->thread_statistics[j].rule_first_match_time;
            }

            if(next_hop->statistics_current.rule_last_match_time<
               next_hop->thread_statistics[j].rule_last_match_time)
            {
                next_hop->statistics_current.rule_last_match_time=
                               next_hop->thread_statistics[j].rule_last_match_time;
            }

            if(next_hop->report_mn_sec &&
               next_hop->time_count>next_hop->report_mn_sec){
                next_hop->thread_statistics[j].rule_report_mn_cnt=0;
                clear_time_count=1;
            }
        }

        if(1==clear_time_count){
            next_hop->time_count=0;
        }

        if(0==next_hop->statistics_before.rule_last_match_time){
            next_hop->statistics_before.rule_last_match_time=
                               next_hop->statistics_current.rule_last_match_time;
        }

        next_hop->statistics_current.rule_report_cnt  = local_report_cnt;
        next_hop->statistics_current.rule_match_hits  = local_match_hits;
        next_hop->statistics_current.rule_match_pkts  = local_match_pkts;
        next_hop->statistics_current.rule_match_bytes = local_match_bytes;
        next_hop->statistics_current.rule_match_pcaps = local_match_pcaps;
        next_hop->statistics_current.rule_match_flows = local_match_flows;
        next_hop->statistics_current.hit_number       = local_hit_number;
        next_hop->statistics_current.store_dump_number= local_store_dump_number;
        next_hop->statistics_current.store_meta_number= local_store_meta_number;

        total_match_pcaps += local_match_pcaps;

    }

    g_rule_statistic.rule_match_pcaps = total_match_pcaps;

    return 1;
}


int sdt_statistics_keep_before(void)
{
    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        if(0==next_hop->flag){
            continue;
        }

        next_hop->statistics_before.rule_match_hits  =
                           next_hop->statistics_current.rule_match_hits;

        next_hop->statistics_before.rule_match_pkts  =
                           next_hop->statistics_current.rule_match_pkts;
        next_hop->statistics_before.rule_match_bytes =
                           next_hop->statistics_current.rule_match_bytes;
        next_hop->statistics_before.rule_match_flows =
                           next_hop->statistics_current.rule_match_flows;

        if(0==next_hop->statistics_before.rule_first_match_time)
        {
            next_hop->statistics_before.rule_first_match_time =
                           next_hop->statistics_current.rule_first_match_time;
        }

        next_hop->statistics_before.rule_last_match_time =
                           next_hop->statistics_current.rule_last_match_time;

    }

    return 1;
}

static int sdt_out_PCAP(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream *node)
{
    return sdt_out_process(sdx_out_ctx, node);
}

static int sdt_out_FRAME(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream *node)
{
    return sdt_out_forward_mac(node);
}

static int sdt_out_one(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream *node)
{
    struct {
        const char *method;
        int rule_mode;
        int (*sdt_out_fun)(SdxOutThreadCtx *, struct packet_stream *node);
    } list[]={
        {"KS-ZC",  0, sdt_out_PCAP},      //method 的有效形式有多种
        {"KS-ZC",  1, sdt_out_FRAME},     //根据 rule_mode 形式转发
        {"ML-MAC", 0, sdt_out_FRAME},
        {"KS-QF",  0, sdt_out_FRAME},     //将数据帧发给清分设备
        {NULL, 0, NULL},
    };

    int ret = -1;
    int i = 0;
    for(i = 0; list[i].method; i++)
    {
        if(0 == strcmp(list[i].method, node->method) && node->rule_mode==list[i].rule_mode)
        {
            ret = list[i].sdt_out_fun(sdx_out_ctx, node);
        }
    }

    sdt_out_status *rule_status;
    if (ret == 0 &&
        (rule_status=sdt_rule_hash_lookup_key(node->unitID, node->taskID, node->groupID, node->rule_id)))
    {
        // 输出成功, 加入统计
        // IPFF 模式每帧都+1
        if (rule_status->match_result->is_ipff)
        {
            rule_status->thread_statistics[sdx_out_ctx->ring_id].store_dump_number++;
        }
        // LINK 模式只在第一帧+1(每个flow只加一次)
        else if (node->first_packet)
        {
            rule_status->thread_statistics[sdx_out_ctx->ring_id].store_dump_number++;
        }
    }

    // DPI_LOG(DPI_LOG_ERROR, "ERROR: %s %s RuleID:%u 未知类型 method:%s", node->taskID, node->groupID, node->rule_id, node->method);
    return ret;
}

static uint8_t sdt_out_signal = 0;
static pthread_t sdt_out_ths[TBL_RING_MAX_NUM] = {(pthread_t)0};
static pthread_t sdt_statistics_th = (pthread_t)0;

void *sdt_statistics_thread(void * arg)
{
    memset(&g_rule_statistic, 0, sizeof(g_rule_statistic));
    while(1)
    {
        if (unlikely(sdt_out_signal == 1))
            break;
        sleep(1);
        sdt_rule_collect_statistics();
    }
    return NULL;
}

int sdt_out_thfunc_timeout(SdxOutThreadCtx *sdx_out_ctx)
{
    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key, (void *) &next_hop, &iter) >= 0)
    {

        //互斥量 - 先 FETCH 再ADD
        sdt_atomic_lock(&next_hop->in_used);
        //互斥量 需要配对使用, 下面代码不可以直接 return

        /* pcap file timeout */
        if(next_hop->pcap_status_ptr &&
           next_hop->pcap_status_ptr->pcap_fp!=NULL  &&
           next_hop->pcap_status_ptr->max_time>0     &&
           next_hop->pcap_status_ptr->pcap_time + next_hop->pcap_status_ptr->max_time < g_config.g_now_time)
        {
            sdt_fragment_pcap_file(sdx_out_ctx, next_hop);
        }

        /* event log timeout */
        if( next_hop->event_status.event_fp!=NULL &&
            next_hop->event_status.event_time + g_config.write_tbl_maxtime < g_config.g_now_time)
        {
            sdt_out_event_fragment(next_hop);   // event文件分割
            next_hop->event_status.event_time=g_config.g_now_time;
        }

        /* syslog timeout */
        if( next_hop->syslog_status.syslog_fp!=NULL &&
            next_hop->syslog_status.syslog_time + g_config.write_tbl_maxtime < g_config.g_now_time)
        {
            sdt_out_syslog_fragment(next_hop);   // syslog文件分割
            next_hop->syslog_status.syslog_time=g_config.g_now_time;
        }

        //只允许 单个成员访问
        sdt_atomic_unlock(&next_hop->in_used);
    }
    return 0;
}

void *sdt_out_thfunc(void * arg)
{
    SdxOutThreadCtx *sdx_out_ctx = (SdxOutThreadCtx*)arg;

    int i, j;
    int deq_num=0;
    long ring_id = sdx_out_ctx->ring_id;
    struct packet_stream *elem;
    struct packet_stream *elem_burst[TBL_MAX_BURST]={0};

    int            retval=0;
    int core_affinity = g_config.sdt_out_core[ring_id];

    //对本线程实例计数
    ATOMIC_ADD_FETCH(&sdt_out_thfunc_cnt);

    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(core_affinity, &cpuset);

    if(pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0){
        DPI_LOG(DPI_LOG_WARNING, "Error while binding log thread to core %d",
                                                              core_affinity);
    }
    DPI_LOG(DPI_LOG_DEBUG, "Running log thread on core %d", core_affinity);

    while (1) {
        if(unlikely(1==sdt_out_signal && rte_ring_empty(sdt_out_ring[ring_id])))
        {
            SdxOutThreadCtx_free(sdx_out_ctx);
            sdx_out_ctx = NULL;
            ATOMIC_SUB_FETCH(&sdt_out_thfunc_cnt);
            pthread_exit(0);
        }

        //HTTP线程在清理 HASH_DB
        //两个日志线程在查 HASH DB
        //在这里等着 -- 直到清理完成
        if(g_sdt_hash_db_clear_flag)
        {
            ATOMIC_ADD_FETCH(&sdt_out_thfunc_signal);
            while(g_sdt_hash_db_clear_flag)
            {
                usleep(1000*100);
            }
            ATOMIC_SUB_FETCH(&sdt_out_thfunc_signal);
        }

        deq_num = rte_ring_sc_dequeue_burst(sdt_out_ring[ring_id], (void **)elem_burst, TBL_MAX_BURST, NULL);
        for (j = 0; j < deq_num; j++)
        {
            elem = elem_burst[j];
            elem->work_on_ringId = ring_id;

            sdt_out_one(sdx_out_ctx, elem);
            free(elem);
        }

        if (deq_num <= 0)
        {
            usleep(1000*300); //300MS
            //////////// 唯一检测 多线程超时 Design By chunli ///////////////////
            //////////// 唯一检测 多线程超时 Design By chunli ///////////////////
            //////////// 唯一检测 多线程超时 Design By chunli ///////////////////
            static int sdt_out_thfunc_timeout_pv = 0;
            int pv = ATOMIC_FETCH_ADD(&sdt_out_thfunc_timeout_pv);
            if(pv % g_config.sdt_out_thead_num == g_config.timeout_index % g_config.sdt_out_thead_num)
            {
                sdt_out_thfunc_timeout(sdx_out_ctx);
            }
            ATOMIC_FETCH_SUB(&sdt_out_thfunc_timeout_pv);
        }
        SdxRecIPRule_rotate(&sdx_out_ctx->sdx_rec_rule);
    }
}


static void
sdt_out_thread(void)
{
    int status;
    long i;
    pthread_t out_th;
    SdxOutThreadStatus *stats_pre;
    SdxOutThreadCtx  *sdx_out_ctx;

    g_sdx_output_thread_status = (SdxOutThreadStatus*)calloc(sizeof(SdxOutThreadStatus), g_config.sdt_out_thead_num);

    for(i=0; i < g_config.sdt_out_thead_num; i++)
    {
        sdx_out_ctx = SdxOutThreadCtx_new();
        sdx_out_ctx->ring_id = i;

        stats_pre = (SdxOutThreadStatus*)calloc(sizeof(SdxOutThreadStatus), 1);
        stats_pre->stats_pre = NULL;

        g_sdx_output_thread_status[i].stats_pre = (struct SdxOutThreadStatus_*)stats_pre;

        status = pthread_create(&out_th, NULL, sdt_out_thfunc, (void*)sdx_out_ctx);
        if(status != 0) {
            DPI_SYS_LOG(DPI_LOG_ERROR, "error on create sdt_out_thfunc thread");
            exit(-1);
        }

        sdt_out_ths[i] = out_th;
    }

    status = pthread_create(&sdt_statistics_th, NULL, sdt_statistics_thread, NULL);
    if(status != 0) {
        DPI_SYS_LOG(DPI_LOG_ERROR, "error on sdt_statistics_thread");
        exit(-1);
    }


}


int sdt_io_init(void)
{

    int ret=0;
    ret=sdt_out_init();
    if(ret<0){
        DPI_SYS_LOG(DPI_LOG_ERROR, "sdt_out_init failed");
    }

    sdt_out_thread();

    return 1;
}

void sdt_out_threads_stop(void)
{
    int i;

    sdt_out_signal = 1;

    for(i=0; i < g_config.sdt_out_thead_num; i++)
    {
        if (!pthread_equal(sdt_out_ths[i], (pthread_t)0))
            pthread_join(sdt_out_ths[i], NULL);
    }

    if (!pthread_equal(sdt_statistics_th, (pthread_t)0))
        pthread_join(sdt_statistics_th, NULL);
}


SdxOutThreadCtx* SdxOutThreadCtx_new()
{
    SdxOutThreadCtx *ctx = dpi_malloc(sizeof(SdxOutThreadCtx));

    memset(ctx, 0, sizeof(SdxOutThreadCtx));
    return ctx;
}

void SdxOutThreadCtx_free(SdxOutThreadCtx* ctx)
{
    if (ctx)
    {
        SdxRecIPRule_uninit(&ctx->sdx_rec_rule);
        dpi_free(ctx);
    }
}


/* 文件序号操作 */
#define GET_WHOLE_DAYS_BY_SEC(t)    (((t)+8*60*60) / (24*60*60))

static uint32_t     global_file_seq = 1;
static uint32_t     s_pre_days = 0;
static GMutex       file_seq_mtx;

static uint32_t dpi_safe_get_global_filename_seq(void)
{
    uint32_t cur_days = GET_WHOLE_DAYS_BY_SEC(g_config.g_now_time);

    if (cur_days == s_pre_days)
    {
        return ATOMIC_FETCH_ADD(&global_file_seq);
    }

    g_mutex_lock(&file_seq_mtx);
    if (cur_days != s_pre_days)
    {
        s_pre_days      = cur_days;
        global_file_seq = 1;
    }
    g_mutex_unlock(&file_seq_mtx);

    return ATOMIC_FETCH_ADD(&global_file_seq);
}

uint32_t dpi_safe_get_filename_seq(int file_seq_type)
{
    uint32_t seq = 0;

    switch (file_seq_type)
    {
    case FILE_SEQ_TYPE_GLOBAL:
        seq = dpi_safe_get_global_filename_seq();
        break;
    default:
        DPI_LOG(DPI_LOG_ERROR, "Unknown file_seq_type[%d]", file_seq_type);
        abort();
        break;
    }

    return seq;
}

int
sdt_check_rule_exist_report(struct flow_info *flow)
{
    uint32_t i;
    for(i=0;i<flow->sdt_flow.sdt_rule_cnt;i++)
    {
        if(!flow->sdt_flow.sdt_rules_status[i])
        {
            continue;
        }
        if(SAE_report & flow->sdt_flow.sdt_rules_status[i]->match_result->action)
        {
            return 1;
        }
    }
    return 0;
}

