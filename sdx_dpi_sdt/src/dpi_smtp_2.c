/****************************************************************************************
 * 文 件 名 : dpi_smtp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy          2018/07/06
编码: wangy            2018/07/06
修改: xuxn          2019/03/07
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include "dpi_email.h"

#include <rte_mempool.h>
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"
#include "dpi_smtp.h"


extern char *email_heads[];
extern const char *email_cmds[];

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

uint16_t get_smtp_field(const uint8_t *payload, uint32_t payload_len, const uint8_t** field);
void dissect_attachments_info(const uint8_t *payload, uint32_t payload_len, struct smtp_session *session, uint32_t offset);
static dpi_field_table  smtp_field_array[] = {


    //extend
    DPI_FIELD_D(EM_SMTP_AUTH_NAME,                                   EM_F_TYPE_STRING,             "Auth_Name"),
    DPI_FIELD_D(EM_SMTP_AUTH_PASSWD,                                 EM_F_TYPE_STRING,             "Auth_Passwd"),
    DPI_FIELD_D(EM_SMTP_MAIL_FILENAME,                               EM_F_TYPE_STRING,             "Mail_File_Name"),
    DPI_FIELD_D(EM_SMTP_MAIL_SERVER_NAME,                            EM_F_TYPE_STRING,             "Mail_Server_Name"),
    DPI_FIELD_D(EM_SMTP_MAIL_SERVER_SOFT,                            EM_F_TYPE_STRING,             "Mail_Server_Soft"),
    DPI_FIELD_D(EM_SMTP_SEND_HOST_IP,                                EM_F_TYPE_STRING,             "Mail_Send_Host_Ip"),
    DPI_FIELD_D(EM_SMTP_RECV_HOST_IP,                                EM_F_TYPE_STRING,             "Mail_Recv_Host_Ip"),

    DPI_FIELD_D(EM_SMTP_DATE,                                        EM_F_TYPE_STRING,             "Date"),
    DPI_FIELD_D(EM_SMTP_FROM,                                        EM_F_TYPE_STRING,             "From"),
    DPI_FIELD_D(EM_SMTP_SENDER,                                      EM_F_TYPE_STRING,             "Sender_Name"),
    DPI_FIELD_D(EM_SMTP_SENDER_DOMAIN,                               EM_F_TYPE_STRING,             "SenderDomain"),
    DPI_FIELD_D(EM_SMTP_REPLY_TO,                                    EM_F_TYPE_STRING,             "Reply-To"),
    DPI_FIELD_D(EM_SMTP_TO,                                          EM_F_TYPE_STRING,             "To"),
    DPI_FIELD_D(EM_SMTP_RECV,                                        EM_F_TYPE_STRING,             "Receiver_Name"),
    DPI_FIELD_D(EM_SMTP_CC,                                          EM_F_TYPE_STRING,             "Cc"),
    DPI_FIELD_D(EM_SMTP_BCC,                                         EM_F_TYPE_STRING,             "Bcc"),
    DPI_FIELD_D(EM_SMTP_REFERENCES,                                  EM_F_TYPE_STRING,             "References"),
    DPI_FIELD_D(EM_SMTP_SUBJECT,                                     EM_F_TYPE_STRING,             "Subject"),
    DPI_FIELD_D(EM_SMTP_X_PRIORITY,                                  EM_F_TYPE_STRING,             "X_Priority"),
    DPI_FIELD_D(EM_SMTP_X_GUID,                                      EM_F_TYPE_STRING,             "X_Guid"),
    DPI_FIELD_D(EM_SMTP_X_HAS_ATTACH,                                EM_F_TYPE_STRING,             "X_Has_Attach"),
    DPI_FIELD_D(EM_SMTP_X_MAILER,                                    EM_F_TYPE_STRING,             "X_Mailer"),
    DPI_FIELD_D(EM_SMTP_MIME_VERSION,                                EM_F_TYPE_STRING,             "MIME-Version"),
    DPI_FIELD_D(EM_SMTP_MESSAGE_ID,                                  EM_F_TYPE_STRING,             "Message-ID"),
    DPI_FIELD_D(EM_SMTP_CONTENT_TYPE,                                EM_F_TYPE_STRING,             "Content-Type"),
    DPI_FIELD_D(EM_SMTP_CONTENT_ID,                                  EM_F_TYPE_STRING,             "Content-ID"),
    DPI_FIELD_D(EM_SMTP_USER_AGENT,                                  EM_F_TYPE_STRING,             "User-Agent"),
    DPI_FIELD_D(EM_SMTP_COMMENTS,                                    EM_F_TYPE_STRING,            "Comments"),
    DPI_FIELD_D(EM_SMTP_KEYWORDS,                                    EM_F_TYPE_STRING,            "Keywords"),
    DPI_FIELD_D(EM_SMTP_RESENT_DATE,                                 EM_F_TYPE_STRING,             "Resent-Date"),
    DPI_FIELD_D(EM_SMTP_RESENT_FROM,                                 EM_F_TYPE_STRING,             "Resent-From"),
    DPI_FIELD_D(EM_SMTP_RESENT_SENDER,                               EM_F_TYPE_STRING,             "Resent-Sender"),
    DPI_FIELD_D(EM_SMTP_RESENT_TO,                                   EM_F_TYPE_STRING,             "Resent-To"),
    DPI_FIELD_D(EM_SMTP_RESENT_CC,                                   EM_F_TYPE_STRING,             "Resent-Cc"),
    DPI_FIELD_D(EM_SMTP_RESENT_BCC,                                  EM_F_TYPE_STRING,             "Resent-Bcc"),
    DPI_FIELD_D(EM_SMTP_RESENT_MESSAGE_ID,                           EM_F_TYPE_STRING,             "Resent-Message_ID"),
    DPI_FIELD_D(EM_SMTP_RETURN_PATH,                                 EM_F_TYPE_STRING,             "Return-Path"),
    DPI_FIELD_D(EM_SMTP_RECEIVED,                                    EM_F_TYPE_STRING,             "Received"),
    DPI_FIELD_D(EM_SMTP_IN_REPLY_TO,                                 EM_F_TYPE_STRING,            "In-Reply-To"),
    DPI_FIELD_D(EM_SMTP_CONTENT_TRANSFER_ENCODING,                   EM_F_TYPE_STRING,             "Content-Transfer-Encoding"),
    DPI_FIELD_D(EM_SMTP_DELIVERY_DATE,                               EM_F_TYPE_STRING,             "Delivery-Date"),
    DPI_FIELD_D(EM_SMTP_LATEST_DELIVERY_TIME,                        EM_F_TYPE_STRING,             "Latest-Delivery-Time"),
    DPI_FIELD_D(EM_SMTP_CONTENT_DESCRIPTION,                         EM_F_TYPE_STRING,            "Content-Description"),

    DPI_FIELD_D(EM_SMTP_AUTOFORWARDED,                               EM_F_TYPE_STRING,             "Autoforwarded"),
    DPI_FIELD_D(EM_SMTP_AUTOSUBMITTED,                               EM_F_TYPE_STRING,             "Autosubmitted"),
    DPI_FIELD_D(EM_SMTP_X400_CONTENT_IDENTIFIER,                     EM_F_TYPE_STRING,             "X400-Content-Identifier"),
    DPI_FIELD_D(EM_SMTP_CONTENT_LANGUAGE,                            EM_F_TYPE_STRING,             "Content-Language"),
    DPI_FIELD_D(EM_SMTP_CONVERSION,                                  EM_F_TYPE_STRING,             "Conversion"),
    DPI_FIELD_D(EM_SMTP_CONVERSION_WITH_LOSS,                        EM_F_TYPE_STRING,             "Conversion-With-Loss"),
    DPI_FIELD_D(EM_SMTP_DISCARDED_X400_IPMS_EXTENSIONS,              EM_F_TYPE_STRING,             "Discarded-X400-IPMS-Extensions"),
    DPI_FIELD_D(EM_SMTP_DISCARDED_X400_MTS_EXTENSIONS,               EM_F_TYPE_STRING,             "Discarded-X400-MTS-Extensions"),
    DPI_FIELD_D(EM_SMTP_DL_EXPANSION_HISTORY,                        EM_F_TYPE_STRING,             "DL-Expansion-History"),
    DPI_FIELD_D(EM_SMTP_DEFERRED_DELIVERY,                           EM_F_TYPE_STRING,             "Deferred-Delivery"),
    DPI_FIELD_D(EM_SMTP_EXPIRES,                                     EM_F_TYPE_STRING,             "Expires"),
    DPI_FIELD_D(EM_SMTP_IMPORTANCE,                                  EM_F_TYPE_STRING,             "Importance"),
    DPI_FIELD_D(EM_SMTP_INCOMPLETE_COPY,                             EM_F_TYPE_STRING,             "Incomplete-Copy"),
    DPI_FIELD_D(EM_SMTP_MESSAGE_TYPE,                                EM_F_TYPE_STRING,             "Message-Type"),
    DPI_FIELD_D(EM_SMTP_ORIGINAL_ENCODED_INFORMATION_TYPES,          EM_F_TYPE_STRING,             "Original-Encoded-Information-Types"),
    DPI_FIELD_D(EM_SMTP_ORIGINATOR_RETURN_ADDRESS,                   EM_F_TYPE_STRING,             "Originator-Return-Address"),
    DPI_FIELD_D(EM_SMTP_PRIORITY,                                    EM_F_TYPE_STRING,             "Priority"),
    DPI_FIELD_D(EM_SMTP_REPLY_BY,                                    EM_F_TYPE_STRING,             "Reply-By"),
    DPI_FIELD_D(EM_SMTP_SENSITIVITY,                                 EM_F_TYPE_STRING,             "Sensitivity"),
    DPI_FIELD_D(EM_SMTP_SUPERSEDES,                                  EM_F_TYPE_STRING,             "Supersedes"),
    DPI_FIELD_D(EM_SMTP_X400_CONTENT_TYPE,                           EM_F_TYPE_STRING,             "X400-Content-Type"),
    DPI_FIELD_D(EM_SMTP_X400_MTS_IDENTIFIER,                         EM_F_TYPE_STRING,             "X400-MTS-Identifier"),
    DPI_FIELD_D(EM_SMTP_X400_ORIGINATOR,                             EM_F_TYPE_STRING,             "X400-Originator"),
    DPI_FIELD_D(EM_SMTP_X400_RECEIVED,                               EM_F_TYPE_STRING,             "X400-Received"),
    DPI_FIELD_D(EM_SMTP_X400_RECIPIENTS,                             EM_F_TYPE_STRING,             "X400-Recipients"),
    DPI_FIELD_D(EM_SMTP_DELIVERED_TO,                                EM_F_TYPE_STRING,             "Delivered-To"),
    DPI_FIELD_D(EM_SMTP_THREAD_INDEX,                                EM_F_TYPE_STRING,             "Thread-Index"),
    DPI_FIELD_D(EM_SMTP_X_MIMEOLE,                                   EM_F_TYPE_STRING,             "X-Mimeole"),
    DPI_FIELD_D(EM_SMTP_EXPIRY_DATE,                                 EM_F_TYPE_STRING,             "Expiry-Date"),
    DPI_FIELD_D(EM_SMTP_X_MS_TNEF_CORRELATOR,                        EM_F_TYPE_STRING,             "X-Ms-Tnef-Correlator"),
    DPI_FIELD_D(EM_SMTP_X_UIDL,                                      EM_F_TYPE_STRING,             "X-Uidl"),
    DPI_FIELD_D(EM_SMTP_X_AUTHENTICATION_WARNING,                    EM_F_TYPE_STRING,             "X-Authentication-Warning"),
    DPI_FIELD_D(EM_SMTP_X_VIRUS_SCANNED,                             EM_F_TYPE_STRING,             "X-Virus-Scanned"),
    DPI_FIELD_D(EM_SMTP_SIO_LABEL,                                   EM_F_TYPE_STRING,             "Sio-Label"),
    DPI_FIELD_D(EM_SMTP_HELO,                                        EM_F_TYPE_STRING,             "Helo"),

    DPI_FIELD_D(EM_SMTP_RECV_HOST,                                   EM_F_TYPE_EMPTY,              "Recv_Host"),
    DPI_FIELD_D(EM_SMTP_SEND_SOFT,                                   EM_F_TYPE_STRING,             "Send_Soft"),
    DPI_FIELD_D(EM_SMTP_PROXY_RESEND,                                EM_F_TYPE_EMPTY,              "Proxy_Resend"),
    //DPI_FIELD_D(EM_SMTP_PROXY_USER,                                EM_F_TYPE_NULL,               "Proxy_User"),
    DPI_FIELD_D(EM_SMTP_USER_OP_TYPE,                                EM_F_TYPE_EMPTY,              "User_Op_Type"),
    DPI_FIELD_D(EM_SMTP_USER_OP_TIME,                                EM_F_TYPE_EMPTY,              "User_Op_Time"),
    DPI_FIELD_D(EM_SMTP_USER_OP_RES,                                 EM_F_TYPE_EMPTY,              "User_Op_Res"),
    DPI_FIELD_D(EM_SMTP_RECEIVER_TYPE,                               EM_F_TYPE_EMPTY,              "Receiver_Type"),
    DPI_FIELD_D(EM_SMTP_SEND_DURATION,                               EM_F_TYPE_EMPTY,              "Send_Duration"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME0,                       EM_F_TYPE_STRING,             "Attachment_Filename0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE0,                   EM_F_TYPE_STRING,             "Attachment_Content_Type0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING0,                       EM_F_TYPE_STRING,             "Attachment_Encoding0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION0,                    EM_F_TYPE_STRING,             "Attachment_Disposition0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN0,                            EM_F_TYPE_UINT64,             "Attachment_len0"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME1,                       EM_F_TYPE_STRING,             "Attachment_Filename1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE1,                   EM_F_TYPE_STRING,             "Attachment_Content_Type1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING1,                       EM_F_TYPE_STRING,             "Attachment_Encoding1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION1,                    EM_F_TYPE_STRING,             "Attachment_Disposition1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN1,                            EM_F_TYPE_UINT64,             "Attachment_len1"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME2,                       EM_F_TYPE_STRING,             "Attachment_Filename2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE2,                   EM_F_TYPE_STRING,             "Attachment_Content_Type2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING2,                       EM_F_TYPE_STRING,             "Attachment_Encoding2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION2,                    EM_F_TYPE_STRING,             "Attachment_Disposition2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN2,                            EM_F_TYPE_UINT64,             "Attachment_len2"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME3,                       EM_F_TYPE_STRING,             "Attachment_Filename3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE3,                   EM_F_TYPE_STRING,             "Attachment_Content_Type3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING3,                       EM_F_TYPE_STRING,             "Attachment_Encoding3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION3,                    EM_F_TYPE_STRING,             "Attachment_Disposition3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN3,                            EM_F_TYPE_UINT64,             "Attachment_len3"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME4,                       EM_F_TYPE_STRING,             "Attachment_Filename4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE4,                   EM_F_TYPE_STRING,             "Attachment_Content_Type4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING4,                       EM_F_TYPE_STRING,             "Attachment_Encoding4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION4,                    EM_F_TYPE_STRING,             "Attachment_Disposition4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN4,                            EM_F_TYPE_UINT64,             "Attachment_len4"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME5,                       EM_F_TYPE_STRING,             "Attachment_Filename5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE5,                   EM_F_TYPE_STRING,             "Attachment_Content_Type5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING5,                       EM_F_TYPE_STRING,             "Attachment_Encoding5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION5,                    EM_F_TYPE_STRING,             "Attachment_Disposition5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN5,                            EM_F_TYPE_UINT64,             "Attachment_len5"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME6,                       EM_F_TYPE_STRING,             "Attachment_Filename6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE6,                   EM_F_TYPE_STRING,             "Attachment_Content_Type6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING6,                       EM_F_TYPE_STRING,             "Attachment_Encoding6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION6,                    EM_F_TYPE_STRING,             "Attachment_Disposition6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN6,                            EM_F_TYPE_UINT64,             "Attachment_len6"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME7,                       EM_F_TYPE_STRING,             "Attachment_Filename7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE7,                   EM_F_TYPE_STRING,             "Attachment_Content_Type7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING7,                       EM_F_TYPE_STRING,             "Attachment_Encoding7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION7,                    EM_F_TYPE_STRING,             "Attachment_Disposition7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN7,                            EM_F_TYPE_UINT64,            "Attachment_len7"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME8,                       EM_F_TYPE_STRING,             "Attachment_Filename8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE8,                   EM_F_TYPE_STRING,             "Attachment_Content_Type8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING8,                       EM_F_TYPE_STRING,             "Attachment_Encoding8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION8,                    EM_F_TYPE_STRING,             "Attachment_Disposition8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN8,                            EM_F_TYPE_UINT64,             "Attachment_len8"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME9,                       EM_F_TYPE_STRING,             "Attachment_Filename9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE9,                   EM_F_TYPE_STRING,             "Attachment_Content_Type9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING9,                       EM_F_TYPE_STRING,             "Attachment_Encoding9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION9,                    EM_F_TYPE_STRING,             "Attachment_Disposition9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN9,                            EM_F_TYPE_UINT64,             "Attachment_len9"),


    /* new fields add by liugh*/
    DPI_FIELD_D(EM_SMTP_MAIL_CODE_FORMAT,                           EM_F_TYPE_STRING,             "MailContentCodeFormat"),
    DPI_FIELD_D(EM_SMTP_MAIL_CODE_BASE,                             EM_F_TYPE_STRING,             "MailContentCodeBase"),
    DPI_FIELD_D(EM_SMTP_MAIL_CONTENT_DATA,                          EM_F_TYPE_STRING,             "MailContentData"),

    DPI_FIELD_D(EM_SMTP_PROTO_TYPE,                                 EM_F_TYPE_STRING,             "Proto_Type"),
    DPI_FIELD_D(EM_SMTP_EML_FILESIZE,                               EM_F_TYPE_UINT32,             "eml_filesize"),
    DPI_FIELD_D(EM_SMTP_LOGIN_STATUS,                               EM_F_TYPE_STRING,              "LoginStatus")
};

static dpi_field_table  smtp_field_array_sdt[] = {
    //extend
    DPI_FIELD_D(EM_SMTP_AUTH_NAME,                                   YV_FT_BYTES,             "Auth_Name"),
    DPI_FIELD_D(EM_SMTP_AUTH_PASSWD,                                 YV_FT_BYTES,             "Auth_Passwd"),
    DPI_FIELD_D(EM_SMTP_MAIL_FILENAME,                               YV_FT_BYTES,             "Mail_File_Name"),
    DPI_FIELD_D(EM_SMTP_MAIL_SERVER_NAME,                            YV_FT_BYTES,             "Mail_Server_Name"),
    DPI_FIELD_D(EM_SMTP_MAIL_SERVER_SOFT,                            YV_FT_BYTES,             "Mail_Server_Soft"),
    DPI_FIELD_D(EM_SMTP_SEND_HOST_IP,                                YV_FT_BYTES,             "Mail_Send_Host_Ip"),
    DPI_FIELD_D(EM_SMTP_RECV_HOST_IP,                                YV_FT_BYTES,             "Mail_Recv_Host_Ip"),

    DPI_FIELD_D(EM_SMTP_DATE,                                        YV_FT_BYTES,             "Date"),
    DPI_FIELD_D(EM_SMTP_FROM,                                        YV_FT_BYTES,             "From"),
    DPI_FIELD_D(EM_SMTP_SENDER,                                      YV_FT_BYTES,             "Sender_Name"),
    DPI_FIELD_D(EM_SMTP_SENDER_DOMAIN,                               YV_FT_BYTES,             "SenderDomain"),
    DPI_FIELD_D(EM_SMTP_REPLY_TO,                                    YV_FT_BYTES,             "Reply-To"),
    DPI_FIELD_D(EM_SMTP_TO,                                          YV_FT_BYTES,             "To"),
    DPI_FIELD_D(EM_SMTP_RECV,                                        YV_FT_BYTES,             "Receiver_Name"),
    DPI_FIELD_D(EM_SMTP_CC,                                          YV_FT_BYTES,             "Cc"),
    DPI_FIELD_D(EM_SMTP_BCC,                                         YV_FT_BYTES,             "Bcc"),
    DPI_FIELD_D(EM_SMTP_REFERENCES,                                  YV_FT_BYTES,             "References"),
    DPI_FIELD_D(EM_SMTP_SUBJECT,                                     YV_FT_BYTES,             "Subject"),
    DPI_FIELD_D(EM_SMTP_X_PRIORITY,                                  YV_FT_BYTES,             "X_Priority"),
    DPI_FIELD_D(EM_SMTP_X_GUID,                                      YV_FT_BYTES,             "X_Guid"),
    DPI_FIELD_D(EM_SMTP_X_HAS_ATTACH,                                YV_FT_BYTES,             "X_Has_Attach"),
    DPI_FIELD_D(EM_SMTP_X_MAILER,                                    YV_FT_BYTES,             "X_Mailer"),
    DPI_FIELD_D(EM_SMTP_MIME_VERSION,                                YV_FT_BYTES,             "MIME-Version"),
    DPI_FIELD_D(EM_SMTP_MESSAGE_ID,                                  YV_FT_BYTES,             "Message-ID"),
    DPI_FIELD_D(EM_SMTP_CONTENT_TYPE,                                YV_FT_BYTES,             "Content-Type"),
    DPI_FIELD_D(EM_SMTP_CONTENT_ID,                                  YV_FT_BYTES,             "Content-ID"),
    DPI_FIELD_D(EM_SMTP_USER_AGENT,                                  YV_FT_BYTES,             "User-Agent"),
    DPI_FIELD_D(EM_SMTP_COMMENTS,                                    YV_FT_BYTES,            "Comments"),
    DPI_FIELD_D(EM_SMTP_KEYWORDS,                                    YV_FT_BYTES,            "Keywords"),
    DPI_FIELD_D(EM_SMTP_RESENT_DATE,                                 YV_FT_BYTES,             "Resent-Date"),
    DPI_FIELD_D(EM_SMTP_RESENT_FROM,                                 YV_FT_BYTES,             "Resent-From"),
    DPI_FIELD_D(EM_SMTP_RESENT_SENDER,                               YV_FT_BYTES,             "Resent-Sender"),
    DPI_FIELD_D(EM_SMTP_RESENT_TO,                                   YV_FT_BYTES,             "Resent-To"),
    DPI_FIELD_D(EM_SMTP_RESENT_CC,                                   YV_FT_BYTES,             "Resent-Cc"),
    DPI_FIELD_D(EM_SMTP_RESENT_BCC,                                  YV_FT_BYTES,             "Resent-Bcc"),
    DPI_FIELD_D(EM_SMTP_RESENT_MESSAGE_ID,                           YV_FT_BYTES,             "Resent-Message_ID"),
    DPI_FIELD_D(EM_SMTP_RETURN_PATH,                                 YV_FT_BYTES,             "Return-Path"),
    DPI_FIELD_D(EM_SMTP_RECEIVED,                                    YV_FT_BYTES,             "Received"),
    DPI_FIELD_D(EM_SMTP_IN_REPLY_TO,                                 YV_FT_BYTES,            "In-Reply-To"),
    DPI_FIELD_D(EM_SMTP_CONTENT_TRANSFER_ENCODING,                   YV_FT_BYTES,             "Content-Transfer-Encoding"),
    DPI_FIELD_D(EM_SMTP_DELIVERY_DATE,                               YV_FT_BYTES,             "Delivery-Date"),
    DPI_FIELD_D(EM_SMTP_LATEST_DELIVERY_TIME,                        YV_FT_BYTES,             "Latest-Delivery-Time"),
    DPI_FIELD_D(EM_SMTP_CONTENT_DESCRIPTION,                         YV_FT_BYTES,            "Content-Description"),

    DPI_FIELD_D(EM_SMTP_AUTOFORWARDED,                               YV_FT_BYTES,             "Autoforwarded"),
    DPI_FIELD_D(EM_SMTP_AUTOSUBMITTED,                               YV_FT_BYTES,             "Autosubmitted"),
    DPI_FIELD_D(EM_SMTP_X400_CONTENT_IDENTIFIER,                     YV_FT_BYTES,             "X400-Content-Identifier"),
    DPI_FIELD_D(EM_SMTP_CONTENT_LANGUAGE,                            YV_FT_BYTES,             "Content-Language"),
    DPI_FIELD_D(EM_SMTP_CONVERSION,                                  YV_FT_BYTES,             "Conversion"),
    DPI_FIELD_D(EM_SMTP_CONVERSION_WITH_LOSS,                        YV_FT_BYTES,             "Conversion-With-Loss"),
    DPI_FIELD_D(EM_SMTP_DISCARDED_X400_IPMS_EXTENSIONS,              YV_FT_BYTES,             "Discarded-X400-IPMS-Extensions"),
    DPI_FIELD_D(EM_SMTP_DISCARDED_X400_MTS_EXTENSIONS,               YV_FT_BYTES,             "Discarded-X400-MTS-Extensions"),
    DPI_FIELD_D(EM_SMTP_DL_EXPANSION_HISTORY,                        YV_FT_BYTES,             "DL-Expansion-History"),
    DPI_FIELD_D(EM_SMTP_DEFERRED_DELIVERY,                           YV_FT_BYTES,             "Deferred-Delivery"),
    DPI_FIELD_D(EM_SMTP_EXPIRES,                                     YV_FT_BYTES,             "Expires"),
    DPI_FIELD_D(EM_SMTP_IMPORTANCE,                                  YV_FT_BYTES,             "Importance"),
    DPI_FIELD_D(EM_SMTP_INCOMPLETE_COPY,                             YV_FT_BYTES,             "Incomplete-Copy"),
    DPI_FIELD_D(EM_SMTP_MESSAGE_TYPE,                                YV_FT_BYTES,             "Message-Type"),
    DPI_FIELD_D(EM_SMTP_ORIGINAL_ENCODED_INFORMATION_TYPES,          YV_FT_BYTES,             "Original-Encoded-Information-Types"),
    DPI_FIELD_D(EM_SMTP_ORIGINATOR_RETURN_ADDRESS,                   YV_FT_BYTES,             "Originator-Return-Address"),
    DPI_FIELD_D(EM_SMTP_PRIORITY,                                    YV_FT_BYTES,             "Priority"),
    DPI_FIELD_D(EM_SMTP_REPLY_BY,                                    YV_FT_BYTES,             "Reply-By"),
    DPI_FIELD_D(EM_SMTP_SENSITIVITY,                                 YV_FT_BYTES,             "Sensitivity"),
    DPI_FIELD_D(EM_SMTP_SUPERSEDES,                                  YV_FT_BYTES,             "Supersedes"),
    DPI_FIELD_D(EM_SMTP_X400_CONTENT_TYPE,                           YV_FT_BYTES,             "X400-Content-Type"),
    DPI_FIELD_D(EM_SMTP_X400_MTS_IDENTIFIER,                         YV_FT_BYTES,             "X400-MTS-Identifier"),
    DPI_FIELD_D(EM_SMTP_X400_ORIGINATOR,                             YV_FT_BYTES,             "X400-Originator"),
    DPI_FIELD_D(EM_SMTP_X400_RECEIVED,                               YV_FT_BYTES,             "X400-Received"),
    DPI_FIELD_D(EM_SMTP_X400_RECIPIENTS,                             YV_FT_BYTES,             "X400-Recipients"),
    DPI_FIELD_D(EM_SMTP_DELIVERED_TO,                                YV_FT_BYTES,             "Delivered-To"),
    DPI_FIELD_D(EM_SMTP_THREAD_INDEX,                                YV_FT_BYTES,             "Thread-Index"),
    DPI_FIELD_D(EM_SMTP_X_MIMEOLE,                                   YV_FT_BYTES,             "X-Mimeole"),
    DPI_FIELD_D(EM_SMTP_EXPIRY_DATE,                                 YV_FT_BYTES,             "Expiry-Date"),
    DPI_FIELD_D(EM_SMTP_X_MS_TNEF_CORRELATOR,                        YV_FT_BYTES,             "X-Ms-Tnef-Correlator"),
    DPI_FIELD_D(EM_SMTP_X_UIDL,                                      YV_FT_BYTES,             "X-Uidl"),
    DPI_FIELD_D(EM_SMTP_X_AUTHENTICATION_WARNING,                    YV_FT_BYTES,             "X-Authentication-Warning"),
    DPI_FIELD_D(EM_SMTP_X_VIRUS_SCANNED,                             YV_FT_BYTES,             "X-Virus-Scanned"),
    DPI_FIELD_D(EM_SMTP_SIO_LABEL,                                   YV_FT_BYTES,             "Sio-Label"),
    DPI_FIELD_D(EM_SMTP_HELO,                                        YV_FT_BYTES,             "Helo"),

    DPI_FIELD_D(EM_SMTP_RECV_HOST,                                   YV_FT_NIL,              "Recv_Host"),
    DPI_FIELD_D(EM_SMTP_SEND_SOFT,                                   YV_FT_BYTES,             "Send_Soft"),
    DPI_FIELD_D(EM_SMTP_PROXY_RESEND,                                YV_FT_NIL,              "Proxy_Resend"),
    //DPI_FIELD_D(EM_SMTP_PROXY_USER,                                YV_FT_NIL,               "Proxy_User"),
    DPI_FIELD_D(EM_SMTP_USER_OP_TYPE,                                YV_FT_NIL,              "User_Op_Type"),
    DPI_FIELD_D(EM_SMTP_USER_OP_TIME,                                YV_FT_NIL,              "User_Op_Time"),
    DPI_FIELD_D(EM_SMTP_USER_OP_RES,                                 YV_FT_NIL,              "User_Op_Res"),
    DPI_FIELD_D(EM_SMTP_RECEIVER_TYPE,                               YV_FT_NIL,              "Receiver_Type"),
    DPI_FIELD_D(EM_SMTP_SEND_DURATION,                               YV_FT_NIL,              "Send_Duration"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME0,                       YV_FT_BYTES,             "Attachment_Filename0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE0,                   YV_FT_BYTES,             "Attachment_Content_Type0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING0,                       YV_FT_BYTES,             "Attachment_Encoding0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION0,                    YV_FT_BYTES,             "Attachment_Disposition0"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN0,                            YV_FT_UINT64,             "Attachment_len0"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME1,                       YV_FT_BYTES,             "Attachment_Filename1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE1,                   YV_FT_BYTES,             "Attachment_Content_Type1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING1,                       YV_FT_BYTES,             "Attachment_Encoding1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION1,                    YV_FT_BYTES,             "Attachment_Disposition1"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN1,                            YV_FT_UINT64,             "Attachment_len1"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME2,                       YV_FT_BYTES,             "Attachment_Filename2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE2,                   YV_FT_BYTES,             "Attachment_Content_Type2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING2,                       YV_FT_BYTES,             "Attachment_Encoding2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION2,                    YV_FT_BYTES,             "Attachment_Disposition2"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN2,                            YV_FT_UINT64,             "Attachment_len2"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME3,                       YV_FT_BYTES,             "Attachment_Filename3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE3,                   YV_FT_BYTES,             "Attachment_Content_Type3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING3,                       YV_FT_BYTES,             "Attachment_Encoding3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION3,                    YV_FT_BYTES,             "Attachment_Disposition3"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN3,                            YV_FT_UINT64,             "Attachment_len3"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME4,                       YV_FT_BYTES,             "Attachment_Filename4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE4,                   YV_FT_BYTES,             "Attachment_Content_Type4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING4,                       YV_FT_BYTES,             "Attachment_Encoding4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION4,                    YV_FT_BYTES,             "Attachment_Disposition4"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN4,                            YV_FT_UINT64,             "Attachment_len4"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME5,                       YV_FT_BYTES,             "Attachment_Filename5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE5,                   YV_FT_BYTES,             "Attachment_Content_Type5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING5,                       YV_FT_BYTES,             "Attachment_Encoding5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION5,                    YV_FT_BYTES,             "Attachment_Disposition5"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN5,                            YV_FT_UINT64,             "Attachment_len5"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME6,                       YV_FT_BYTES,             "Attachment_Filename6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE6,                   YV_FT_BYTES,             "Attachment_Content_Type6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING6,                       YV_FT_BYTES,             "Attachment_Encoding6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION6,                    YV_FT_BYTES,             "Attachment_Disposition6"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN6,                            YV_FT_UINT64,             "Attachment_len6"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME7,                       YV_FT_BYTES,             "Attachment_Filename7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE7,                   YV_FT_BYTES,             "Attachment_Content_Type7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING7,                       YV_FT_BYTES,             "Attachment_Encoding7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION7,                    YV_FT_BYTES,             "Attachment_Disposition7"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN7,                            YV_FT_UINT64,            "Attachment_len7"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME8,                       YV_FT_BYTES,             "Attachment_Filename8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE8,                   YV_FT_BYTES,             "Attachment_Content_Type8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING8,                       YV_FT_BYTES,             "Attachment_Encoding8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION8,                    YV_FT_BYTES,             "Attachment_Disposition8"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN8,                            YV_FT_UINT64,             "Attachment_len8"),

    DPI_FIELD_D(EM_SMTP_ATTACHMENT_FILENAME9,                       YV_FT_BYTES,             "Attachment_Filename9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_CONTENT_TYPE9,                   YV_FT_BYTES,             "Attachment_Content_Type9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_ENCODING9,                       YV_FT_BYTES,             "Attachment_Encoding9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_DISPOSITION9,                    YV_FT_BYTES,             "Attachment_Disposition9"),
    DPI_FIELD_D(EM_SMTP_ATTACHMENT_LEN9,                            YV_FT_UINT64,             "Attachment_len9"),


    /* new fields add by liugh*/
    DPI_FIELD_D(EM_SMTP_MAIL_CODE_FORMAT,                           YV_FT_BYTES,             "MailContentCodeFormat"),
    DPI_FIELD_D(EM_SMTP_MAIL_CODE_BASE,                             YV_FT_BYTES,             "MailContentCodeBase"),
    DPI_FIELD_D(EM_SMTP_MAIL_CONTENT_DATA,                          YV_FT_BYTES,             "MailContentData"),

    DPI_FIELD_D(EM_SMTP_PROTO_TYPE,                                 YV_FT_BYTES,             "Proto_Type"),
    DPI_FIELD_D(EM_SMTP_EML_FILESIZE,                               YV_FT_UINT32,             "eml_filesize"),
    DPI_FIELD_D(EM_SMTP_LOGIN_STATUS,                               YV_FT_BYTES,              "LoginStatus")
};



/*
*smtp的邮件的内容用一个单独的eml文件保存，通过tbl日志中的一个字段关联
*/
static int get_smtp_filename(session_type_t session_type, uint8_t thread_id,char *name, int len)
{
    int proto = session_type == SESSION_TYPE_ESMTP ? TBL_LOG_MAIL_ESMTP : TBL_LOG_MAIL_SMTP;
    snprintf(name, len, "%s/%s/email_%llu_%ld_%03d.eml",
            g_config.tbl_out_dir, tbl_log_array[TBL_LOG_EMAIL].protoname, (unsigned long long)g_config.g_now_time_usec, random(),thread_id);

    return 0;
}

static void reset_session(struct smtp_session* session)
{
    if (session) {
        memset(session, 0, sizeof(struct smtp_session));
        session->state = SMTP_STATE_READING_CMDS;
        // 默认esmtp
        session->has_ensure_type = 0;
        session->session_type = SESSION_TYPE_ESMTP;
        session->cur_cmd = SMTP_CMD_AUTH_BEFORE;
    }
}

static int smtp_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, int *idx, int i)
{
    //int local_idx=*idx;
    struct smtp_session * session = (struct smtp_session *)flow->app_session;
    if (!session)
        return -1;
    switch(i){

        case EM_SMTP_AUTH_NAME:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,(const uint8_t *)session->auth_name, strlen(session->auth_name));
            break;
        case EM_SMTP_AUTH_PASSWD:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,(const uint8_t *)session->auth_passwd, strlen(session->auth_passwd));
            break;
        case EM_SMTP_MAIL_FILENAME:
            if(strlen(session->mail_filename)>0){
                char filename[128]={0};
                if(get_filename(session->mail_filename, filename)){
                    write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, (const uint8_t *)filename, strlen(filename));
                    break;
                }
            }
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,(const uint8_t *)session->mail_filename, strlen(session->mail_filename));
            break;
        case EM_SMTP_MAIL_SERVER_NAME:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,(const uint8_t *)session->mail_server_name_ptr, strlen(session->mail_server_name_ptr));
            break;
        case EM_SMTP_MAIL_SERVER_SOFT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,(const uint8_t *)session->mail_server_soft_ptr, strlen(session->mail_server_soft_ptr));
            break;
        case EM_SMTP_SEND_HOST_IP:
            if(4 == flow->ip_version)
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->send_host_ip4, 64);
            else
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->send_host_ip6, 64);
            break;
        case EM_SMTP_RECV_HOST_IP:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,(const uint8_t *)session->mail_recv_host_ip_ptr, strlen(session->mail_recv_host_ip_ptr));
            break;
        case EM_SMTP_DATE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_date_ptr, session->mail_date_len);
            break;
        case EM_SMTP_FROM:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_from_ptr, session->mail_from_len);
            break;
        //发送人
        case EM_SMTP_SENDER:
            if(session->mail_sender_name_ptr)
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_sender_name_ptr, session->mail_sender_name_len);
            else
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_from_ptr, session->mail_from_len);
            break;
        //发件域
        case EM_SMTP_SENDER_DOMAIN:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_sender_domain_ptr, session->mail_sender_domain_len);
            break;
        case EM_SMTP_REPLY_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_reply_to_ptr, session->mail_reply_to_len);
            break;

        case EM_SMTP_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_to_ptr, session->mail_to_len);
            break;
        //收件人
        case EM_SMTP_RECV:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_to_ptr, session->mail_to_len);
            break;
        case EM_SMTP_CC:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_cc_ptr, session->mail_cc_len);
            break;
        case EM_SMTP_BCC:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_bcc_ptr, session->mail_bcc_len);
            break;
        case EM_SMTP_REFERENCES:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_references_ptr, session->mail_references_len);
            break;
        case EM_SMTP_SUBJECT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_subject_ptr, session->mail_subject_len);
            break;
        case EM_SMTP_X_PRIORITY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->mail_x_priority_ptr, session->mail_x_priority_len);
            break;
        case EM_SMTP_X_GUID:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_guid_ptr, session->mail_x_guid_len);
            break;
        case EM_SMTP_X_HAS_ATTACH:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_has_attach_ptr, session->mail_x_has_attach_len);
            break;
        case EM_SMTP_X_MAILER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_mailer_ptr, session->mail_x_mailer_len);
            break;
        case EM_SMTP_MIME_VERSION:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_mime_version_ptr, session->mail_mime_version_len);
            break;
        case EM_SMTP_MESSAGE_ID:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_message_id_ptr, session->mail_message_id_len);
            break;
        case EM_SMTP_CONTENT_TYPE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_content_type_ptr, session->mail_content_type_len);
            break;
        case EM_SMTP_CONTENT_ID:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_content_id_ptr, session->mail_content_id_len);
            break;
        case EM_SMTP_USER_AGENT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_user_agent_ptr, session->mail_user_agent_len);
            break;
        case EM_SMTP_SEND_SOFT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_mailer_ptr, session->mail_x_mailer_len);
            break;
        case EM_SMTP_COMMENTS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_comments_ptr, session->mail_comments_len);
            break;
        case EM_SMTP_KEYWORDS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_keywords_ptr, session->mail_keywords_len);
            break;
        case EM_SMTP_RESENT_DATE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_resent_date_ptr, session->mail_resent_date_len);
            break;
        case EM_SMTP_RESENT_FROM:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_resent_from_ptr, session->mail_resent_from_len);
            break;
        case EM_SMTP_RESENT_SENDER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_resent_sender_ptr, session->mail_resent_sender_len);
            break;
        case EM_SMTP_RESENT_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_resent_to_ptr, session->mail_resent_to_len);
            break;
        case EM_SMTP_RESENT_CC:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_resent_cc_ptr, session->mail_resent_cc_len);
            break;
        case EM_SMTP_RESENT_BCC:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_resent_bcc_ptr, session->mail_resent_bcc_len);
            break;
        case EM_SMTP_RESENT_MESSAGE_ID:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_resent_message_id_ptr, session->mail_resent_message_id_len);
            break;
        case EM_SMTP_RETURN_PATH:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_return_path_ptr, session->mail_return_path_len);
            break;
        case EM_SMTP_RECEIVED:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_received_ptr, session->mail_received_len);
            break;
        case EM_SMTP_IN_REPLY_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_in_reply_to_ptr, session->mail_in_reply_to_len);
            break;
        case EM_SMTP_CONTENT_TRANSFER_ENCODING:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_content_transfer_encoding_ptr, session->mail_content_transfer_encoding_len);
            break;
        case EM_SMTP_DELIVERY_DATE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_delivery_date_ptr, session->mail_delivery_date_len);
            break;
        case EM_SMTP_LATEST_DELIVERY_TIME:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_latest_delivery_time_ptr, session->mail_latest_delivery_time_len);
            break;
        case EM_SMTP_CONTENT_DESCRIPTION:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_content_description_ptr, session->mail_content_description_len);
            break;
        case EM_SMTP_AUTOFORWARDED:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_autoforwarded_ptr, session->mail_autoforwarded_len);
            break;
        case EM_SMTP_AUTOSUBMITTED:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_autosubmitted_ptr, session->mail_autosubmitted_len);
            break;
        case EM_SMTP_X400_CONTENT_IDENTIFIER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x400_content_identifier_ptr, session->mail_x400_content_identifier_len);
            break;
        case EM_SMTP_CONTENT_LANGUAGE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_content_language_ptr, session->mail_content_language_len);
            break;
        case EM_SMTP_CONVERSION:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_conversion_ptr, session->mail_conversion_len);
            break;
        case EM_SMTP_CONVERSION_WITH_LOSS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_conversion_with_loss_ptr, session->mail_conversion_with_loss_len);
            break;
        case EM_SMTP_DISCARDED_X400_IPMS_EXTENSIONS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_discarded_x400_ipms_extensions_ptr, session->mail_discarded_x400_ipms_extensions_len);
            break;
        case EM_SMTP_DISCARDED_X400_MTS_EXTENSIONS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_discarded_x400_mts_extensions_ptr, session->mail_discarded_x400_mts_extensions_len);
            break;
        case EM_SMTP_DL_EXPANSION_HISTORY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_dl_expansion_history_ptr, session->mail_dl_expansion_history_len);
            break;
        case EM_SMTP_DEFERRED_DELIVERY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_deferred_delivery_ptr, session->mail_deferred_delivery_len);
            break;
        case EM_SMTP_EXPIRES:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_expires_ptr, session->mail_expires_len);
            break;
        case EM_SMTP_IMPORTANCE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_importance_ptr, session->mail_importance_len);
            break;
        case EM_SMTP_INCOMPLETE_COPY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_incomplete_copy_ptr, session->mail_incomplete_copy_len);
            break;
        case EM_SMTP_MESSAGE_TYPE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_message_type_ptr, session->mail_message_type_len);
            break;
        case EM_SMTP_ORIGINAL_ENCODED_INFORMATION_TYPES:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_original_encoded_information_types_ptr, session->mail_original_encoded_information_types_len);
            break;
        case EM_SMTP_ORIGINATOR_RETURN_ADDRESS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_originator_return_address_ptr, session->mail_originator_return_address_len);
            break;
        case EM_SMTP_PRIORITY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_priority_ptr, session->mail_priority_len);
            break;
        case EM_SMTP_REPLY_BY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_reply_by_ptr, session->mail_reply_by_len);
            break;
        case EM_SMTP_SENSITIVITY:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_sensitivity_ptr, session->mail_sensitivity_len);
            break;
        case EM_SMTP_SUPERSEDES:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_supersedes_ptr, session->mail_supersedes_len);
            break;
        case EM_SMTP_X400_CONTENT_TYPE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x400_content_type_ptr, session->mail_x400_content_type_len);
            break;
        case EM_SMTP_X400_MTS_IDENTIFIER:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x400_mts_identifier_ptr, session->mail_x400_mts_identifier_len);
            break;
        case EM_SMTP_X400_ORIGINATOR:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x400_originator_ptr, session->mail_x400_originator_len);
            break;
        case EM_SMTP_X400_RECEIVED:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x400_received_ptr, session->mail_x400_received_len);
            break;
        case EM_SMTP_X400_RECIPIENTS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x400_recipients_ptr, session->mail_x400_recipients_len);
            break;
        case EM_SMTP_DELIVERED_TO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_delivered_to_ptr, session->mail_delivered_to_len);
            break;
        case EM_SMTP_THREAD_INDEX:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_thread_index_ptr, session->mail_thread_index_len);
            break;
        case EM_SMTP_X_MIMEOLE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_mimeole_ptr, session->mail_x_mimeole_len);
            break;
        case EM_SMTP_EXPIRY_DATE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_expiry_date_ptr, session->mail_expiry_date_len);
            break;
        case EM_SMTP_X_MS_TNEF_CORRELATOR:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_ms_tnef_correlator_ptr, session->mail_x_ms_tnef_correlator_len);
            break;
        case EM_SMTP_X_UIDL:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_uidl_ptr, session->mail_x_uidl_len);
            break;
        case EM_SMTP_X_AUTHENTICATION_WARNING:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_authentication_warning_ptr, session->mail_x_authentication_warning_len);
            break;
        case EM_SMTP_X_VIRUS_SCANNED:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_x_virus_scanned_ptr, session->mail_x_virus_scanned_len);
            break;
        case EM_SMTP_SIO_LABEL:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->mail_sio_label_ptr, session->mail_sio_label_len);
            break;
        case EM_SMTP_HELO:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,(const uint8_t*)session->mail_helo_ptr, strlen(session->mail_helo_ptr));
            break;

        case EM_SMTP_ATTACHMENT_FILENAME0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[0].attachment_filename, strlen((const char*)session->attachment_list[0].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[0].attachment_content_type, strlen((const char*)session->attachment_list[0].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[0].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[0].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[0].attachment_content_disposition, strlen((const char*)session->attachment_list[0].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN0:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[0].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[1].attachment_filename, strlen((const char*)session->attachment_list[1].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[1].attachment_content_type, strlen((const char*)session->attachment_list[1].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[1].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[1].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[1].attachment_content_disposition, strlen((const char*)session->attachment_list[1].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN1:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[1].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[2].attachment_filename, strlen((const char*)session->attachment_list[2].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[2].attachment_content_type, strlen((const char*)session->attachment_list[2].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[2].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[2].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[2].attachment_content_disposition, strlen((const char*)session->attachment_list[2].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN2:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[2].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[3].attachment_filename, strlen((const char*)session->attachment_list[3].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[3].attachment_content_type, strlen((const char*)session->attachment_list[3].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[3].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[3].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[3].attachment_content_disposition, strlen((const char*)session->attachment_list[3].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN3:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[3].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[4].attachment_filename, strlen((const char*)session->attachment_list[4].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[4].attachment_content_type, strlen((const char*)session->attachment_list[4].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[4].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[4].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[4].attachment_content_disposition, strlen((const char*)session->attachment_list[4].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN4:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,NULL, session->attachment_list[4].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[5].attachment_filename, strlen((const char*)session->attachment_list[5].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[5].attachment_content_type, strlen((const char*)session->attachment_list[5].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[5].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[5].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[5].attachment_content_disposition, strlen((const char*)session->attachment_list[5].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN5:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[5].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[6].attachment_filename, strlen((const char*)session->attachment_list[6].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[6].attachment_content_type, strlen((const char*)session->attachment_list[6].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[6].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[6].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[6].attachment_content_disposition, strlen((const char*)session->attachment_list[6].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN6:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[6].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[7].attachment_filename, strlen((const char*)session->attachment_list[7].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[7].attachment_content_type, strlen((const char*)session->attachment_list[7].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[7].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[7].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[7].attachment_content_disposition, strlen((const char*)session->attachment_list[7].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN7:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[7].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[8].attachment_filename, strlen((const char*)session->attachment_list[8].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[8].attachment_content_type, strlen((const char*)session->attachment_list[8].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[8].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[8].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[8].attachment_content_disposition, strlen((const char*)session->attachment_list[8].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN8:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[8].attachment_len);
            break;

        case EM_SMTP_ATTACHMENT_FILENAME9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[9].attachment_filename, strlen((const char*)session->attachment_list[9].attachment_filename));
            break;
        case EM_SMTP_ATTACHMENT_CONTENT_TYPE9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[9].attachment_content_type, strlen((const char*)session->attachment_list[9].attachment_content_type));
            break;
        case EM_SMTP_ATTACHMENT_ENCODING9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[9].attachment_content_transfer_encoding, strlen((const char*)session->attachment_list[9].attachment_content_transfer_encoding));
            break;
        case EM_SMTP_ATTACHMENT_DISPOSITION9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type,session->attachment_list[9].attachment_content_disposition, strlen((const char*)session->attachment_list[9].attachment_content_disposition));
            break;
        case EM_SMTP_ATTACHMENT_LEN9:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->attachment_list[9].attachment_len);
            break;

        /* 添加主题内容提取 */
        case EM_SMTP_MAIL_CODE_FORMAT:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->main_content_print, strlen((const char*)session->main_content_print));
            break;
        case EM_SMTP_MAIL_CODE_BASE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->main_content_code, strlen((const char*)session->main_content_code));
            break;
        case EM_SMTP_MAIL_CONTENT_DATA:
            if(session->main_content_len>MAX_CONTENT_SIZE){
                session->main_content_len=0;
                session->main_content_data=NULL;
            }
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, session->main_content_data, session->main_content_len);
            break;
        case EM_SMTP_PROTO_TYPE:
            if (session->session_type == SESSION_TYPE_SMTP) {
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, (const uint8_t *)"SMTP", 4);
            }
            else {
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, (const uint8_t *)"ESMTP", 5);
            }
            break;
        case EM_SMTP_EML_FILESIZE:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, NULL, session->eml_filesize);
            break;
        case EM_SMTP_LOGIN_STATUS:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, smtp_field_array[i].type, (const uint8_t *)session->login_status, strlen(session->login_status));
            break;

        default:
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
    }

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = smtp_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif


    return 0;
}



static int write_smtp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;
    struct smtp_session * session = (struct smtp_session *)flow->app_session;
    if (!session)
        return 0;
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return 0;
    }

    if (session->session_type == SESSION_TYPE_ESMTP) {
        log_ptr->log_type = TBL_LOG_MAIL_ESMTP;
    }
    else{
        log_ptr->log_type = TBL_LOG_MAIL_SMTP;
    }

    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "smtp");

    for(i=0;i<map_fields_get_num(PROTOCOL_MAIL_SMTP);i++){
        smtp_field_element(log_ptr, flow, direction, &idx,i);
    }

    log_ptr->thread_id= flow->thread_id;
    log_ptr->log_type = TBL_LOG_MAIL_SMTP;
    log_ptr->log_len = idx;        //最后一个字段后面有 "|"
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}

#define CUT_OUT_FIELD(ptr, ptr_len, head_len)   do{        \
    (ptr) = line + (head_len);                            \
    (ptr_len) = line_len - (head_len);                    \
}while(0)



#if 0
static uint16_t get_mail_main_content(const char *data_p, uint32_t len, struct smtp_session *session)
{
    const char *pp0=NULL;
    const char *pp1=NULL;
    uint32_t tmp_len=0;
    uint32_t index=0;

    memset(session->main_content_print,0,sizeof(session->main_content_print));
    memset(session->main_content_code,0,sizeof(session->main_content_code));
    session->main_content_len=0;
    session->main_content_data=NULL;

    pp0=mail_memstr(data_p+index, "charset=", len);
    if(pp0!=NULL){
        tmp_len=pp0-data_p;
        index+=tmp_len;
        pp1=mail_memstr(pp0, "\r\n",len-index);
        if(pp1!=NULL){
            tmp_len=pp1-pp0;
            snprintf((char *)session->main_content_print,tmp_len+1,"%s",pp0);
            //printf("[####SMTP]printlen:%d data:%s\n",tmp_len,session->main_content_print);
            index+=tmp_len;
            index+=2;
            if(index>=len){return PKT_DROP;}

        }
    }

    const char *cp0=NULL;
    const char *cp1=NULL;
    if(index>=len){return PKT_DROP;}
    if(pp1!=NULL){
        cp0=mail_memstr(pp1,"Content-Transfer-Encoding: ",len-index);
        tmp_len=cp0-pp1;
        index+=tmp_len;
        if(index>=len){return PKT_DROP;}
        if(cp0!=NULL){
            cp1=mail_memstr(cp0, "\r\n",len-index);
            if(cp1!=NULL){
                //strncpy((char*)session->main_content_code,cp0+tmp_len,cp1-cp0);
                tmp_len=cp1-cp0;
                index+=tmp_len;
                if(index>=len){return PKT_DROP;}

                if(tmp_len>50){tmp_len=50;}
                snprintf((char*)session->main_content_code,tmp_len+1,"%s", cp0);
                //printf("[SMTP]code:%s\n",session->main_content_code);
                if(index+2<len ){
                if(strncmp(cp1+2,"\r\n",2)==0){
                    goto content_data;
                }else{
                    index+=2;
                }
                }
            }
        }
    }else{
        cp0=mail_memstr(data_p,"Content-Transfer-Encoding: ",len-index);
        tmp_len=cp0-data_p;
        index+=tmp_len;
        if(index>=len){return PKT_DROP;}
        if(cp0!=NULL){
            cp1=mail_memstr(cp0, "\r\n",len-index);
            if(cp1!=NULL){
                //strncpy((char*)session->main_content_code,cp0+tmp_len,cp1-cp0);
                tmp_len=cp1-cp0;
                index+=tmp_len;
                if(index>=len){return PKT_DROP;}

                if(tmp_len>50){tmp_len=50;}
                snprintf((char*)session->main_content_code,tmp_len,"%s", cp0);
                //printf("[LIUGUANGHAI]code:%s\n",session->main_content_code);
                if(index+2<len ){
                if(strncmp(cp1+2,"\r\n",2)==0){
                    goto content_data;
                }else{
                    index+=2;
                }
                }
            }
        }
    }

    const char *p_tmp=NULL;
    p_tmp = mail_memstr(data_p+index,"\r\n\r\n",len-index);
    if(p_tmp!=NULL){
        index=p_tmp-data_p;
        if(index>=len){return PKT_DROP;}
    }

content_data:
    index+=4;
    if(index>=len){return PKT_DROP;}
    session->main_content_data=(const uint8_t *)data_p+index;

    const char *dp0=NULL;
    dp0=mail_memstr(data_p+index,".\r\n",len-index)    ;
    if(cp1!=NULL && dp0!=NULL){
        tmp_len=dp0-data_p;
        if(tmp_len<index){return PKT_DROP;}
        session->main_content_len=tmp_len-index;
        if(session->main_content_len>MAX_CONTENT_SIZE){
            session->main_content_len=MAX_CONTENT_SIZE;
        }
    }else {
        session->main_content_len=0;
        session->main_content_data=NULL;
    }

    #if 0
    if(session->main_content_len>0){
        char tts[1024]={0};
        if(session->main_content_len>1024){
            session->main_content_len=1024;
        }
        snprintf(tts,session->main_content_len,"%s",session->main_content_data);
        printf("\n===============================SMTP==================================\n");
        printf("%s\n",tts);
        printf("====================================================================\n\n");
    }
    #endif


    return PKT_OK;
}
#else
static uint16_t get_mail_main_content(const char *data_p, uint32_t len, struct smtp_session *session)
{
    const char *cp0 = NULL;
    const char *cp1 = NULL;
    const char *pp0 = NULL;
    const char *pp1 = NULL;
    uint32_t tmp_len = 0;
    uint32_t index = 0;

    session->main_content_len = 0;
    session->main_content_data = NULL;

    pp0 = mail_memstr(data_p + index, "charset=", len);
    if (pp0 != NULL) {
        tmp_len = pp0 - data_p;
        index += tmp_len;
        pp1 = mail_memstr(pp0, "\r\n", len - index);
        if (pp1 != NULL) {
            tmp_len = pp1 - pp0;
            strncat(session->text_charsets, (const char *)(pp0 + strlen("charset=") + 1), tmp_len - (strlen("charset=") + 1));
            session->text_charsets_num++;
            index += tmp_len;
            index += 2;

            //if (!strncasecmp((const char *)data_p, "text/html", 9))
            {
                session->content_charset_ptr = (const uint8_t *)(pp0 + strlen("charset=") + 1);
                session->content_charset_len = tmp_len - 1 - (strlen("charset=") + 1);
            }

            if (index >= len) { return PKT_DROP; }

        }
    }

    if (index >= len) { return PKT_DROP; }
    if (pp1 != NULL) {
        cp0 = mail_memstr(pp1, "Content-Transfer-Encoding: ", len - index);
        tmp_len = cp0 - pp1;
        index += tmp_len;
        if (index >= len) { return PKT_DROP; }
        if (cp0 != NULL) {
            cp1 = mail_memstr(cp0, "\r\n", len - index);
            if (cp1 != NULL) {
                tmp_len = cp1 - cp0;
                index += tmp_len;
                if (index >= len) { return PKT_DROP; }

                if (tmp_len > 50) { tmp_len = 50; }
                snprintf((char*)session->main_content_code, tmp_len + 1, "%s", cp0);
                strncat(session->body_tra_encs, cp0 + 27, cp1 - (cp0 + 27));
                strcat(session->body_tra_encs, ",");
                session->body_tra_encs_num++;

                if (index + 2 < len) {
                    if (strncmp(cp1 + 2, "\r\n", 2) == 0) {
                        goto content_data;
                    }
                    else {
                        index += 2;
                    }
                }
            }
        }
    }
    else {
        cp0 = mail_memstr(data_p, "Content-Transfer-Encoding: ", len - index);
        tmp_len = cp0 - data_p;
        index += tmp_len;
        if (index >= len) { return PKT_DROP; }
        if (cp0 != NULL) {
            cp1 = mail_memstr(cp0, "\r\n", len - index);
            if (cp1 != NULL) {
                tmp_len = cp1 - cp0;
                index += tmp_len;
                if (index >= len) { return PKT_DROP; }

                if (tmp_len > 50) { tmp_len = 50; }
                snprintf((char*)session->main_content_code, tmp_len, "%s", cp0);
                strncat(session->body_tra_encs, cp0 + 27, cp1 - (cp0 + 27));
                strcat(session->body_tra_encs, ",");
                session->body_tra_encs_num++;

                if (index + 2 < len) {
                    if (strncmp(cp1 + 2, "\r\n", 2) == 0) {
                        goto content_data;
                    }
                    else {
                        index += 2;
                    }
                }
            }
        }
    }

    const char *p_tmp = NULL;
    p_tmp = mail_memstr(data_p + index, "\r\n\r\n", len - index);
    if (p_tmp != NULL) {
        index = p_tmp - data_p;
        if (index >= len) { return PKT_DROP; }
    }

content_data:
    index += 4;
    if (index >= len) { return PKT_DROP; }
    session->main_content_data = (const uint8_t *)data_p + index;

    const char *dp0 = NULL;
    dp0 = mail_memstr(data_p + index, ".\r\n", len - index);
    if (cp1 != NULL && dp0 != NULL) {
        tmp_len = dp0 - data_p;
        if (tmp_len < index) { return PKT_DROP; }
        session->main_content_len = tmp_len - index;
        if (session->main_content_len > MAX_CONTENT_SIZE) {
            session->main_content_len = MAX_CONTENT_SIZE;
        }
    }
    else {
        session->main_content_len = 0;
        session->main_content_data = NULL;
    }

    /* html标签的正文 */
    if (!strncasecmp((const char *)data_p, "text/html", 9)) {
        session->content_has_html_ptr = session->main_content_data;
        session->content_has_html_len = session->main_content_len;
    }

    return PKT_OK;
}
#endif

//解决某个字段值有多行的情况(每行都具有明确的 "\r\n"): 确定该字段值的长度
//适用条件: 该字段完整值之后(包括"\r\n")一定是含有 ": " 或 "\r\n\r\n" 的一整行
//          该字段到这一行首个元素的地址偏移量上所有字符(可再减2去掉最后的一个"\r\n"),
//            均作为该字段的值，本函数的返回值即是这个偏移量,代表该字段值的长度
uint16_t get_smtp_field(const uint8_t *payload, uint32_t payload_len, const uint8_t** field)
{
    uint16_t field_len = 0;
    const uint8_t* tmp = *field;
    uint16_t check_len=MAX_CHECK_LEN;
    if(tmp && tmp - payload < payload_len)
    {
        const uint8_t* p0 = (uint8_t*)strstr((const char*)tmp,"\r\n");
        const uint8_t* p1 = (uint8_t*)strstr((const char*)tmp,": ");
        const uint8_t* p2 = (uint8_t*)strstr((const char*)tmp,"\r\n\r\n");
        if(p1 && p2 && p1 > p2)        //最后一个标准字段
        {
            field_len = p2 - tmp;
            return field_len < payload_len ? field_len : 0;
        }
        if(p0 && p1 && p2 && p0 < p1 && p1 < p2)
        {
            const uint8_t* p2 = (uint8_t*)strstr((const char*)p1, "\r\n");
            if(p2)
            {
                field_len = p0 - tmp + 2;
                int len = tmp - payload;
                while(p0 && (unsigned)(len + field_len) < payload_len && p0 < p1)
                {
                    if(check_len>(len-field_len)){
                        check_len=len-field_len;
                    }
                    p0 = (const uint8_t*)mail_memstr((const char*)tmp + field_len,"\r\n",check_len);
                    if(!p0) break;
                    if(p0 >= p2)
                    {
                        field_len -= 2;
                        break;
                    }
                    field_len = p0 - tmp + 2;
                    if(field_len<=0 || field_len>=payload_len){
                        break;
                    }
                    p0 = tmp + field_len;
                    check_len=MAX_CHECK_LEN;
                }
                return field_len < payload_len ? field_len : 0;
            }
        }
    }
    *field = NULL;
    return 0;
}

//解析附件信息,目前最多解析10个附件
void dissect_attachments_info(const uint8_t *payload, uint32_t payload_len, struct smtp_session *session, uint32_t offset)
{
    if(!session->mail_content_type_ptr) return;
    const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_content_type_ptr,"-=_");
//    const uint8_t* p2 = (uint8_t*)strstr((const char*)session->mail_content_type_ptr, "_=-");
    const uint8_t* p2 = NULL;
    if (p1) {
        p2 = (uint8_t*)strstr((const char*)p1, "\r\n");
    }
    if(p1 && p2 && p1 < p2 && payload)
    {
        while (p2 && !isalnum(*p2)) p2--;

        int fLen = 0;
        int bLen = 0;
//        fLen = p2 - p1 - 3;
        fLen = p2 + 1 - p1 - 3;
        if(fLen > 0 && fLen < 128)
        {
            //获取附件boundary标志
            char boundary[fLen+1];
            memset(boundary, 0, fLen+1);
            memcpy(boundary, (const void*)(p1+3), fLen);   //boundary == "001_NextPart815368584288"
            bLen = strlen(boundary);        //标志成功提取
            if(bLen)
            {
                int index = 0;
                //过滤掉第一个boundary
                const uint8_t* tmp = (uint8_t*)strstr((const char*)payload + offset, (const char*)boundary);
                tmp = (NULL == tmp ? NULL : tmp + bLen);
                const uint8_t* p_tmp = (uint8_t*)strstr((const char*)tmp, (const char*)boundary);
                if (p_tmp) {
                    uint32_t now_len = p_tmp + 2 - payload;
                    uint32_t left_len = payload_len - now_len;
                    if (left_len > 10 && (strncasecmp((const char *)tmp + 2 + 14, "text/plain", 10) == 0 || strncasecmp((const char *)tmp + 2 + 14, "text/html", 9) == 0)) {
                        if (session->main_content_len == 0 && left_len > 0 && left_len < payload_len)
                            get_mail_main_content((const char *)(const char *)tmp + 2 + 14, left_len, session);
                    }
                }
                //解析附件从第二个boundary开始,目前支持最多10个附件
                for(index=0; tmp && index < SMTP_ATTACHMENT_NUM_MAX; index++)
                {
                    int tLen = 0;
                    int flags = 0;
                    const uint8_t* tmp0 = NULL, *tmp1 = NULL, *tmp2 = NULL;
                    const uint8_t* tmptmp = (uint8_t*)strstr((const char*)tmp, (const char*)boundary);
                    if(tmptmp)
                    {
                        if(index != 0)
                            session->attachment_list[index-1].attachment_len = tmptmp - tmp -10;
                        tmp = tmptmp;
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-type:");
                        if(tmp0)
                        {
                            #if 1  //提取主体内容 add by liugh
                            uint32_t now_len=tmp0-payload+14;
                            uint32_t left_len=payload_len-now_len;
                            if(left_len >10 && (strncasecmp((const char *)tmp0+14,"text/plain",10) == 0 ||  strncasecmp((const char *)tmp0+14,"text/html",9) == 0)){
                            //if(left_len >10 && (strncasecmp((const char *)tmp0+14,"text/plain",10) == 0 )){
                                if(session->main_content_len==0 && left_len>0 && left_len<payload_len)
                                    get_mail_main_content((const char *)(const char *)tmp0+14, left_len, session);
                            }
                            #endif
                            int cLen = strlen("content-type:");
                            tmp1 = (uint8_t*)strstr((const char*)tmp0, ";");
                            if(!tmp1)
                            {
                                tmp2 = (uint8_t*)strstr((const char*)tmp0,"\r\n");
                                if(tmp2)
                                {
                                    tLen = tmp2 - tmp0 - cLen -1;
                                    if (tLen > 0) {
                                        memcpy((void*)session->attachment_list[index].attachment_content_type, (const void*)(tmp0 + cLen), tLen < CONTENT_TYPE_LEN ? tLen : CONTENT_TYPE_LEN - 1);
                                        strncat(session->body_type, (const char *)(tmp0 + cLen), tLen);
                                        strcat(session->body_type, ",");
                                        session->body_type_num++;
                                    }
                                }
                                //-1 说明此处未获取到filename
                                flags = -1;
                            }
                            else
                            {
                                tLen = tmp1 - tmp0 - cLen -1;
                                if (tLen > 0) {
                                    memcpy((void*)session->attachment_list[index].attachment_content_type, (const void*)(tmp0 + cLen + 1), tLen < CONTENT_TYPE_LEN ? tLen : CONTENT_TYPE_LEN - 1);
                                    strncat(session->body_type, (const char *)(tmp0 + cLen + 1), tLen);
                                    strcat(session->body_type, ",");
                                    session->body_type_num++;

                                    session->attachment_conttype_num++;
                                }
                                tmp1 = (uint8_t*)strstr((const char*)tmp1,"name=\"");
                                if(tmp1)
                                {
                                    tmp2 = (uint8_t*)strstr((const char*)tmp1,"\r\n");
                                    if(tmp2)
                                    {
                                        int len = strlen("name=\"");
                                        tLen = tmp2 - tmp1 - len -1;
                                        if (tLen > 0) {
                                            session->attachment_filename_num++;
                                            memcpy((void*)session->attachment_list[index].attachment_filename, (const void*)(tmp1 + len), tLen < FILE_NAME_LEN ? tLen : FILE_NAME_LEN - 1);
                                        }
                                    }
                                }
                            }
                        }
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-md5:");
                        if (tmp0)
                        {
                            tmp2 = (uint8_t*)strstr((const char*)tmp0, "\r\n");
                            if (tmp2)
                            {
                                int cLen = strlen("content-md5:");
                                int len = tmp2 - tmp0 - cLen - 1;
                                if (len > 0) {
                                    session->attachent_md5_num++;
                                    memcpy((void*)session->attachment_list[index].attachment_content_md5, (const void*)(tmp0 + cLen + 1), len < CONTENT_MD5_LEN ? len : CONTENT_MD5_LEN - 1);
                                }
                            }
                        }
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-transfer-encoding:");
                        if(tmp0)
                        {
                            tmp2 = (uint8_t*)strstr((const char*)tmp0,"\r\n");
                            if(tmp2)
                            {
                                int cLen = strlen("content-transfer-encoding:");
                                int len = tmp2 - tmp0 - cLen -1;
                                if(len > 0)
                                    memcpy((void*)session->attachment_list[index].attachment_content_transfer_encoding, (const void*)(tmp0+cLen+1), len<ENCODING_LEN ? len:ENCODING_LEN-1);
                            }
                        }
                        tmp0 = (uint8_t*)strcasestr((const char*)tmp, "content-disposition");
                        if(tmp0)
                        {
                            int cLen = strlen("content-disposition:");
                            tmp1 = (uint8_t*)strstr((const char*)tmp0, ";");
                            if(!tmp1)
                            {
                                tmp2 = (uint8_t*)strstr((const char*)tmp0,"\r\n");
                                if(tmp2)
                                {
                                    tLen = tmp2 - tmp0 - cLen -1;
                                    if(tLen > 0)
                                        memcpy((void*)session->attachment_list[index].attachment_content_disposition, (const void*)(tmp0+cLen), tLen<DISPOSITION_LEN ? tLen:DISPOSITION_LEN-1);
                                }
                                //-1 说明此处未获取到filename
                                flags = -1;
                            }
                            else
                            {
                                tmp2 = (uint8_t*)strstr((const char*)tmp1+4,"\r\n");
                                if(tmp2)
                                {
                                    tLen = tmp2 - tmp0 - cLen -1;
                                    if(tLen > 0)
                                        memcpy((void*)session->attachment_list[index].attachment_content_disposition, (const void*)(tmp0+cLen), tLen<DISPOSITION_LEN ? tLen:DISPOSITION_LEN-1);
                                    if(flags == -1)
                                    {
                                        tmp1 = (uint8_t*)strcasestr((const char*)tmp0, "filename=\"");
                                        if(tmp1)
                                        {
                                            int len = strlen("filename=\"");
                                            tLen = tmp2 - tmp1 - len -1;
                                            if (tLen > 0) {
                                                session->attachment_filename_num++;
                                                memcpy((void*)session->attachment_list[index].attachment_filename, (const void*)(tmp1 + len), tLen < FILE_NAME_LEN ? tLen : FILE_NAME_LEN - 1);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if(tmp2)
                        {
                            tmp = tmp2 + 4;
                            continue;
#if 0
                            //优化:此处的匹配如果与进循环第一次匹配合并是较好的做法
                            const uint8_t *ppppp = strstr(tmp, boundary);
                            if(ppppp)
                            {
                                session->attachment_list[index].attachment_len = ppppp - tmp -10;
                                continue;
                            }
#endif
                        }
                    }
                    break;
                }
            }
        }
    }
}

//获取字段值(单行或多行)的总长度,不含最后一组"\r\n"，
static int smtp_find_packet_line_end(const uint8_t *data, uint16_t len)
{
    if(data)
    {

        const uint8_t* p0 = (const uint8_t*)mail_memstr((const char*)data, "\r\n",len);
        if(p0)
        {
            int plen0=p0-data;
            const uint8_t* p1 = (const uint8_t*)mail_memstr((const char*)p0, ":",len-plen0);
            if(p1)
            {
                int i = p1 - data;
                if(i >= len) return len;
                for(; i > 0 && get_uint16_t(data, i) != ntohs(0x0d0a); i--);
                return i;
            }
            return p0 - data;
        }
    }
    return -1;
}


/*
* 解析Cc的地址与名字, 逗号分开
* ex: Cc: "'Antonio Pelaez'" <<EMAIL>>,
    "'Than Thi Chung'" <<EMAIL>>,
    "'Vu Thi Bich Hop'" <<EMAIL>>,
    "'Le Van Son'" <<EMAIL>>, <<EMAIL>>,
    <<EMAIL>>, "'Pham Thi Lan'" <<EMAIL>>,
    <<EMAIL>>,
    "'Pham Thuy Anh'" <<EMAIL>>,
    "'Nguyen Thi Thuy'" <<EMAIL>>
*/
static int extract_cc_infos(const uint8_t *ptr, uint16_t ptr_len, struct smtp_session *session) {

    if (!ptr || !session || !ptr_len)
        return -1;

    uint16_t i = 0, start = 0;
    uint16_t j, k;
    if (!strncasecmp((const char *)ptr, "cc: ", 4))
        i += 4;

    start = i;
    do {
        if (ptr[i] == '>') {
            while (start < i && ptr[start] != '\'' && ptr[start] != '<') start++;

            for (j = start; j <= i; j++) {
                if (ptr[j] == '<') {
                    // Cc address
                    strncat(session->cc_addrs, (char *)(ptr + j + 1), i - (j + 1));
                    strcat(session->cc_addrs, ",");
                    session->cc_addrs_num++;

                    // j = i时 无 alias
                    if (j > start && j < i) {
                        // Cc alias
                        strncat(session->cc_alias, (char *)(ptr + start), j - start);

                        while (1) {

                            char ch = session->cc_alias[strlen(session->cc_alias) - 1];
                            if (ch != '\'')
                                session->cc_alias[strlen(session->cc_alias) - 1] = '\0';
                            else
                                break;
                        }

                        strcat(session->cc_alias, ",");
                        session->cc_alias_num++;
                    }

                    start = i + 1;
                    break;
                }
            }
        }

    } while (i++ < ptr_len);


    return 0;
}

static void get_address_and_alias(const uint8_t *ptr, uint16_t ptr_len, struct smtp_session *session) {
    if (!ptr || !session)
        return;

    char alias[300];
    char tmp[2048 + 1];
    const char *token = "<";
    char *p_next = NULL;
    uint16_t i, k;

    memset(alias, 0, sizeof(alias));
    strncpy(alias, (const char *)ptr, ptr_len);
    p_next = strtok(alias, token);

    for (i = 0; i < strlen(alias); i++) {
        if (alias[i] == '<') {
            p_next = &alias[i];
            break;
        }
    }

    if (p_next) {

        if (i > 0) {
            memset(tmp, 0, sizeof(tmp));
            strcat(tmp, p_next);
            char *p0 = tmp;

            // 删除首尾
            while (!isalnum(*p0)) p0++;
            while (!isalnum(tmp[strlen(tmp) - 1])) tmp[strlen(tmp) - 1] = '\0';

            if ((int)strlen(p0) > 1 && strlen(p0) < ptr_len) {
                // 别名
                strcat(session->rcv_aliases, (const char *)p0);
                strcat(session->rcv_aliases, ",");
                session->rcv_aliases_num++;
            }

            // 地址
            strncat(session->rcv_mails, (const char *)(ptr + (int)strlen(p_next) + 1), ptr_len - ((int)strlen(p_next) + 1));
            strcat(session->rcv_mails, ",");
            session->rcv_mails_num++;

            for (k = (int)strlen(p_next) + 1; k < ptr_len; k++) {
                if (p_next[k] == '@') {
                    strncat(session->rcv_doms, (const char *)&p_next[k + 1], ptr_len - k - 1);
                    if (session->rcv_doms[strlen(session->rcv_doms) - 1] == '>')
                        session->rcv_doms[strlen(session->rcv_doms) - 1] = '\0';
                    strcat(session->rcv_doms, ",");

                    session->rcv_doms_num++;
                    break;
                }
            }
        }
        else {
            // 地址
            strncat(session->rcv_mails, (const char *)(ptr + 1), ptr_len - 1);
            strcat(session->rcv_mails, ",");
            session->rcv_mails_num++;

            for (k = 1; k < ptr_len; k++) {
                if (ptr[k] == '@') {
                    strncat(session->rcv_doms, (const char *)&ptr[k + 1], ptr_len - k - 1);
                    if (session->rcv_doms[strlen(session->rcv_doms) - 1] == '>')
                        session->rcv_doms[strlen(session->rcv_doms) - 1] = '\0';
                    strcat(session->rcv_doms, ",");

                    session->rcv_doms_num++;
                    break;
                }
            }
        }
    }
    else {
        memset(tmp, 0, sizeof(tmp));
        strcat(tmp, p_next);
        char *p0 = tmp;
        uint16_t len = ptr_len;

        while (!isalnum(*p0)) {
            p0++;
            len--;
        }
        while (!isalnum(p0[len - 1])) {
            p0[len - 1] = '\0';
            len--;
        }

        strncat(session->rcv_mails, (const char *)p0, len);
        strcat(session->rcv_mails, ",");
        session->rcv_mails_num++;

        for (k = 0; k < len; k++) {
            if (p0[k] == '@') {
                strncat(session->rcv_doms, (const char *)&p0[k + 1], len - k - 1);
                if (session->rcv_doms[strlen(session->rcv_doms) - 1] == '>')
                    session->rcv_doms[strlen(session->rcv_doms) - 1] = '\0';
                strcat(session->rcv_doms, ",");

                session->rcv_doms_num++;
                break;
            }
        }
    }
}

/*
* 提取To信息
* ex:
To: "'Diep Nguyen'" <<EMAIL>>,
    "'Nguyen thi kieu, Vien'" <<EMAIL>>,
    "'Pernille Friis'" <<EMAIL>>,
    "'Trine Glue Doan'" <<EMAIL>>, <<EMAIL>>
*/
static int extract_to_infos(const uint8_t *ptr, uint16_t ptr_len, struct smtp_session *session) {
    if (!ptr || !session || !ptr_len)
        return -1;

    int k = 0;
    int i = 0;
    char alias[300];
    char tmp[2048 + 1];
    char *p_next = NULL;
    char *p_start = NULL;

    strncpy(tmp, (const char *)ptr, ptr_len);
    p_start = tmp;
    p_next = tmp;
    while (i < ptr_len) {

        if (p_next[i] == '>') {
            get_address_and_alias((const uint8_t *)(p_start + 1), p_next + i - (p_start + 1), session);

            p_start = &tmp[i];
            if (p_start[0] != '\"') {

                // update next start
                k = 0;
                while (i + k < ptr_len && p_start[k] != '<' && p_start[k] != '\"') k++;
                    ;

                p_start += (k - 1);

                i += k;
                continue;
            }
        }

        i++;
    }

    // 只有1个邮箱
    if (0 == session->rcv_aliases_num) {
        get_address_and_alias(ptr, ptr_len, session);
    }

    return 0;
}

/*
*  根据IP查询ASN与国家
*@ip4_str ex:**************
*/
static int get_region_from_ip(ip2region_t st, const char *ip4_str, IPINFO *info, char *asn, int asn_len) {
    memset(asn, 0, asn_len);
    if (g_config.mmdb_switch == 0)
        return -1;

    if (!ip4_str || !info)
        return -1;

    int i = 0;
    uint8_t ip[4];
    char tmp[64];
    const char *token = ".";
    char *p_next = NULL;

    memset(ip, 0, sizeof(ip));
    snprintf(tmp, sizeof(tmp), "%s", ip4_str);
    p_next = strtok(tmp, token);
    while (p_next) {

        if (i >= 4)
            break;

        ip[i++] = (uint8_t)atoi(p_next);

        if (p_next[0] == '\0')
            break;

        p_next = strtok(NULL, token);
    }

    datablock_entry entry;
    char* s[6]; //国家 地区 省 市县 运营商
    if (ip2region_memory_search(st, get_uint32_ntohl(ip, 0), &entry)) {
        //printf("IP positin: %s\n", entry.region);
        if (entry.region[0] == '0') {
            strcpy(info->country, "中国");

            goto asn_extract;
        }
        int i, j = 0;
        s[j++] = entry.region;
        for (i = 0; entry.region[i]; ++i) {
            if (entry.region[i] == '|') {
                entry.region[i] = '\0';
                s[j++] = entry.region + i + 1;
            }
        }
        if (j < 4) return 0;
        strcpy(info->country, s[0]);
        strcpy(info->area,  s[1]);
        strcpy(info->state, s[2]);
        strcpy(info->city,  s[3]);
    }

asn_extract:
    get_asn_from_ip(ip4_str, asn, asn_len);

    return 0;
}

/*
* 解析Received字段中的 from domain&ip, 并统计数量
* ex: from helg05.formin.fi ([************]) by hels11.mfa.uhnet.fi with Microsoft SMTPSVC(6.0.3790.1830);
*/
static int extract_received_infos(const uint8_t *ptr, uint16_t ptr_len, struct smtp_session *session) {

    if (!ptr || !session || !ptr_len)
        return -1;

    struct keys_info {
        uint8_t idx;
        const char *name;
    } targets[] = {
        {1,  "from"},
        {2,  "by"},
        {0,  NULL},
    };

    uint8_t offset = 0;
    uint8_t type = 0;
    uint16_t i;
    uint16_t dom_start, ip_start;
    for (i = 0; i < ptr_len; i++) {
        for (type = 0; targets[type].idx && targets[type].name; type++) {
            if (!strncasecmp((const char *)(ptr + i), targets[type].name, strlen(targets[type].name))) {
                goto recv_start;
            }
        }
    }

recv_start:
    if (i == ptr_len)
        return -1;

    i += strlen(targets[type].name) + 1;
    dom_start = i;

    // from domain
    for (; i < ptr_len; i++) {
        if (ptr[i] == ' ') {
            uint16_t len = i - dom_start;
            const uint8_t *p0 = memstr(ptr + dom_start, ")", i - dom_start);
            if (p0) {
                len = p0 - (ptr + dom_start);
            }

            if (targets[type].idx == 1) {
                strncat(session->received_from_doms, (char *)(ptr + dom_start), len);
                strcat(session->received_from_doms, ",");
                session->received_from_doms_num++;
                ip_start = i;  // update here
                break;
            }
            else if (targets[type].idx == 2) {
                strncat(session->received_by_doms, (char *)(ptr + dom_start), len);
                strcat(session->received_by_doms, ",");
                session->received_by_doms_num++;
                ip_start = i;  // update here
                break;
            }
        }
    }

    // from ip
    for (; i < ptr_len; i++) {
        if (ptr[i] == '[') {
            ip_start = i;  // found here
        }
        else if (ptr[i] == ']') {
            if (ptr[ip_start] == '[') {
                // from ip
                if (targets[type].idx == 1) {
                    strncat(session->received_from_ips, (char *)(ptr + ip_start + 1), i - ip_start - 1);
                    strcat(session->received_from_ips, ",");
                    session->received_from_ips_num++;

                    IPINFO info;
                    char ip[64];
                    memset(ip, 0, sizeof(ip));
                    strncpy(ip, (char *)(ptr + ip_start + 1), i - ip_start - 1);

                    char asn[100];
                    get_region_from_ip(&g_config.entry, ip, &info, asn, sizeof(asn));
                    strcat(session->from_countries, info.country);
                    strcat(session->from_countries, ",");
                    session->from_countries_num++;

                    if (strlen(asn)) {
                        strcat(session->from_asns, asn);
                        strcat(session->from_asns, ",");
                        session->from_asns_num++;
                    }
                }
                // by ip
                else if (targets[type].idx == 2) {
                    strncat(session->received_by_ips, (char *)(ptr + ip_start + 1), i - ip_start - 1);
                    strcat(session->received_by_ips, ",");
                    session->received_by_ips_num++;
                }
            }
            break;
        }
    }


    return 0;
}

/*
*解析邮件内容，只解析头部，邮件具体内容保存在文件中
*/
static void dissect_imf(const uint8_t *payload, uint32_t payload_len, struct smtp_session *session)
{
    int line_len;
    int empty_line_index;
    uint32_t offset = 0;
    const uint8_t *line;
    const uint8_t* pto;

    // 正文前的一些命令解析
    int cmd_end;
    int cmd_line_len;
    const uint8_t *cmd_line;
    uint32_t cmd_off = 0;

    cmd_end = _find_empty_line(payload, payload_len);
    if (cmd_end < 0)
        cmd_end = payload_len;

    cmd_line = payload;
    while (cmd_off < (uint32_t)cmd_end)
    {
        cmd_line_len = smtp_find_packet_line_end(cmd_line, cmd_end - cmd_off);
        if (cmd_line_len <= 0)
            break;
        else
        {
            // login server
            if (session->login_svr_ptr) {
                pto = memstr(cmd_line, "Pop3 Server ", cmd_line_len);
                if (!pto) {
                    pto = memstr(cmd_line, "Imap Server ", cmd_line_len);
                }
                if (pto && pto + 12) {
                    pto = memstr(pto + 12, "(", cmd_line_len - 12);
                }
                if (pto)
                {
                    session->login_svr_ptr = pto;
                    session->login_svr_len = payload + cmd_line_len - pto;
                }
            }

            int i_set = 0;
            const char *p_set;
            while ((p_set = email_heads[i_set++])) {
                if (cmd_line_len >= (int)strlen(p_set) && strncasecmp((const char *)cmd_line, p_set, strlen(p_set)) == 0) {
                    strcat(session->head_sets, p_set);
                    strcat(session->head_sets, ",");
                    session->head_sets_num++;
                }
            }

            int i_cmd = 0;
            int i_line;
            const char *p_cmd;
            while ((p_cmd = email_cmds[i_cmd++])) {
                if (cmd_line_len >= (int)strlen(p_cmd)) {
                    for (i_line = 0; i_line < cmd_line_len; i_line++) {
                        if (strncasecmp((const char *)(cmd_line + i_line), p_cmd, strlen(p_cmd)) == 0) {
                            strcat(session->commands, p_cmd);
                            strcat(session->commands, ",");
                            session->commands_num++;

                            if (session->starttls_f == 0 && !strncasecmp("starttls", p_cmd, strlen(p_cmd)) == 0)
                                session->starttls_f = 1;

                            // HELO EHLO 后面紧跟服务器
                            if (!session->host_ptr) {
                                if (!strcmp((const char *)(cmd_line + i_line), "HELO ")
                                    || !strcmp((const char *)(cmd_line + i_line), "EHLO ")) {
                                    session->host_ptr = cmd_line + i_line + 5;
                                    session->host_len = payload + cmd_line_len - (cmd_line + i_line + 5);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (strncmp((const char *)cmd_line, "Content-Type: ", 14) == 0 && cmd_line + 14)
        {
            session->mail_content_type_ptr = cmd_line + 14;

            if ((pto = memstr(cmd_line, ";", cmd_line_len))) {
                session->mail_content_type_len = pto - session->mail_content_type_ptr;

                strncat(session->cont_types, (const char *)(cmd_line + 14), session->mail_content_type_len);
                strcat(session->cont_types, ",");
                session->cont_types_num++;
#if 1

                if (session->mail_content_type_ptr != NULL &&
                    (strncasecmp((const char *)session->mail_content_type_ptr, "text/plain", 10) == 0 ||
                        strncasecmp((const char *)session->mail_content_type_ptr, "text/html", 9) == 0)
                    ) {
                    /*
                   if(session->mail_content_type_ptr!=NULL &&
                       (strncasecmp((const char *)session->mail_content_type_ptr,"text/plain",10) == 0 )
                       ){*/

                    uint16_t main_len = session->mail_content_type_ptr - payload;
                    uint16_t mm_len = 0;
                    if (main_len < payload_len)
                        mm_len = payload_len - main_len;

                    if (session->main_content_len == 0 && mm_len > 0 && mm_len < payload_len)
                        get_mail_main_content((const char *)session->mail_content_type_ptr, mm_len, session);
                }
#endif
            }
        }

        cmd_off += cmd_line_len + 2;
        cmd_line = &payload[cmd_off];
    }

    /*
    const uint8_t* pto = (uint8_t*)strcasestr((const char*)payload, "To:");
    if(pto && pto + 4)
    {
        session->mail_to_ptr = pto + 4;
        const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_to_ptr,":");
        if(p1)
        {
            int len = p1 - session->mail_to_ptr - 1;
            len = (unsigned)len < payload_len ? len : 0;
            for(;len > 0 && session->mail_to_ptr[len] != '\r'; len--);
            session->mail_to_len = len;
        }
    }
    */
    pto = (uint8_t*)strcasestr((const char*)payload, "MAIL FROM:");
    if (pto && pto + 11)
    {
        session->mail_mail_from_ptr = pto + 11;
        const uint8_t* p0 = (uint8_t*)strstr((const char*)session->mail_mail_from_ptr, ">\r\n");
        if (!p0)
            p0 = (uint8_t*)strstr((const char*)session->mail_mail_from_ptr, "\r\n");
        const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_mail_from_ptr, "<");
        if (!p1)
            p1 = session->mail_mail_from_ptr;
        else
            session->mail_mail_from_ptr = p1 + 1;
        session->mail_mail_from_len = p0 - p1 - 1;
        p1 = (uint8_t*)strstr((const char*)session->mail_mail_from_ptr, "@");
        if (p0 && p1 && p0 > p1)
        {
            session->mail_mail_from_domain_ptr = p1 + 1;
            session->mail_mail_from_domain_len = p0 - p1 - 1;

            strncat(session->mail_from_doms, (const char *)session->mail_mail_from_domain_ptr, session->mail_mail_from_domain_len);
            strcat(session->mail_from_doms, ",");
            session->mail_from_doms_num++;
        }
    }
    pto = (uint8_t*)strcasestr((const char*)payload, "RCPT TO:");
    if (pto && pto + 8)
    {
        if (pto[8] == ' ')
            session->mail_rcpt_to_ptr = pto + 9;
        else
            session->mail_rcpt_to_ptr = pto + 8;

        const uint8_t* p0 = (uint8_t*)strstr((const char*)session->mail_rcpt_to_ptr, ">\r\n");
        if (!p0)
            p0 = (uint8_t*)strstr((const char*)session->mail_rcpt_to_ptr, "\r\n");
        const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_rcpt_to_ptr, "<");
        if (!p1)
            p1 = session->mail_rcpt_to_ptr;
        else
            session->mail_rcpt_to_ptr = p1 + 1;
        session->mail_rcpt_to_len = p0 - p1 - 1;
        p1 = (uint8_t*)strstr((const char*)session->mail_rcpt_to_ptr, "@");
        if (p0 && p1 && p0 > p1)
        {
            session->mail_rcpt_to_domain_ptr = p1 + 1;
            session->mail_rcpt_to_domain_len = p0 - p1 - 1;

            strncat(session->mail_rcpt_to_doms, (const char *)session->mail_rcpt_to_domain_ptr, session->mail_rcpt_to_domain_len);
            strcat(session->mail_rcpt_to_doms, ",");
            session->mail_rcpt_to_doms_num++;
        }
    }

    pto = (uint8_t*)strcasestr((const char*)payload, "user-agent:");
    if(pto && pto + 12)
    {
        session->mail_user_agent_ptr = pto + 12;
        const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_user_agent_ptr,":");
        if(p1)
        {
            int len = p1 - session->mail_user_agent_ptr - 1;
            len = (unsigned)len < payload_len ? len : 0;
            for(;len > 0 && session->mail_user_agent_ptr[len] != '\r'; len--);
            session->mail_user_agent_len = len;
        }
    }

    empty_line_index = _find_empty_line(payload, payload_len);
    if (empty_line_index < 0)
        empty_line_index = payload_len;

    if (empty_line_index < (int)payload_len)
        session->mail_num = 2;
    else
        session->mail_num = 1;

    line = payload;
    while (offset < (uint32_t)empty_line_index)
    {
        line_len = smtp_find_packet_line_end(line, empty_line_index - offset);
        if (line_len <= 0) break;
        else
        {
            if (line_len > 6 && strncasecmp((const char *)line, "Date: ", 6) == 0)
            {

                session->mail_date_ptr = line + 6;
                session->mail_date_len = line_len - 6;
                /*
                uint32_t temp_len=256;
                if(session->mail_date_len>0 && temp_len>session->mail_date_len){
                    temp_len=session->mail_date_len;
                }
                const char *p=mail_memstr(session->mail_date_ptr,"\r\n",temp_len);
                if(p!=NULL){

                }
                */
            }
            else if (line_len > 6 && strncasecmp((const char *)line, "From:", 5) == 0 )
            {
                session->mail_from_ptr = line + 6;
                session->mail_from_len = line_len - 6;
                if(session->mail_from_ptr)
                {
                    const uint8_t* p0 = (uint8_t*)strstr((const char*)session->mail_from_ptr,">\r\n");
                    if(!p0)
                        p0 = (uint8_t*)strstr((const char*)session->mail_from_ptr,"\r\n");
                    const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_from_ptr,"<");
                    if(p1 && p0 && p0 > p1)
                    {
                        session->sender_email_ptr = p1 + 1;
                        session->sender_email_len = p0 - p1 - 1;
                        const uint8_t *tmp1 = (uint8_t*)strstr((const char*)session->mail_from_ptr,"\"");
                        if(tmp1 && tmp1+1 && tmp1 < p1)
                        {
                            const uint8_t *tmp2 = (uint8_t*)strstr((const char*)tmp1+1,"\"");
                            if(tmp2 && tmp2 < p1)
                            {
                                session->mail_sender_name_ptr = tmp1+1;
                                session->mail_sender_name_len = tmp2 - tmp1 - 1;
                            }
                        }
                        p1 = (uint8_t*)strstr((const char*)p1,"@");
                    }
                    else
                        p1 = (uint8_t*)strstr((const char*)session->mail_from_ptr,"@");
                    if(p0 && p1 && p0 > p1)
                    {
                        session->mail_sender_domain_ptr = p1;
                        session->mail_sender_domain_len = p0 - p1;
                    }
                }
            }
            else if (line_len > 4 && strncasecmp((const char *)line, "To: ", 4) == 0 )
            {
                session->mail_to_ptr = line + 4;
                session->mail_to_len = line_len - 4;
                if(session->mail_to_ptr)
                {
                    // 获取收件人别名与地址
                    extract_to_infos(session->mail_to_ptr, session->mail_to_len, session);

                    const uint8_t* p0 = (uint8_t*)strstr((const char*)session->mail_to_ptr,">\r\n");
                    if(!p0)
                        p0 = (uint8_t*)strstr((const char*)session->mail_to_ptr,"\r\n");
                    const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_to_ptr,"<");
                    if(p1 && p0 && p0 > p1)
                    {
                        session->receiver_email_ptr = p1 + 1;
                        session->receiver_email_len = p0 - p1 - 1;

                        const uint8_t *tmp1 = (uint8_t*)strstr((const char*)session->mail_to_ptr," ");
                        if(tmp1 && *(tmp1+1)=='<' && tmp1 < p1)
                        {
                            session->mail_receiver_name_ptr = session->mail_to_ptr;
                            session->mail_receiver_name_len = tmp1 - session->mail_to_ptr;
                        }
                        p1 = (uint8_t*)strstr((const char*)p1,"@");
                    }
                    else
                        p1 = (uint8_t*)strstr((const char*)session->mail_to_ptr,"@");
                    if(p0 && p1 && p0 > p1)
                    {
                        session->mail_receiver_domain_ptr = p1 + 1;
                        session->mail_receiver_domain_len = p0 - p1 - 1;
                    }
                }
            }
            else if (line_len > 4 && strncasecmp((const char *)line, "Cc: ", 4) == 0)
            {
                session->mail_cc_ptr = line + 4;
                session->mail_cc_len = line_len - 4;

                /*
                session->mail_cc_ptr = line + 4;
                const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_cc_ptr,": ");
                if(p1)
                {
                    int len = p1 - session->mail_cc_ptr - 1;
                    len = (unsigned)len < payload_len ? len : 0;
                    for(;len > 0 && session->mail_cc_ptr[len] != '\r'; len--);
                    session->mail_cc_len = len;
                }else{
                    session->mail_cc_len = line_len - 4;
                }*/

                extract_cc_infos(line + 4, line_len - 4, session);

            }
            else if (line_len > 5 && strncasecmp((const char *)line, "Bcc: ", 5) == 0)
            {
                session->mail_bcc_ptr = line + 5;
                session->mail_bcc_len = line_len - 5;
            }
            else if (line_len > 9 && strncasecmp((const char *)line, "Subject: ", 9) == 0)
            {
                session->mail_subject_ptr = line + 9;
                session->mail_subject_len = line_len - 9;

                strncat(session->subjects, (const char *)(line + 9), line_len - 9);
                strcat(session->subjects, ",");
                session->subjects_num++;
            }
            else if(line_len > 12 && strncasecmp((const char *)line, "x-priority: ", 12) == 0)
            {
                session->mail_x_priority_ptr = line + 12;
                session->mail_x_priority_len = line_len - 12;
            }
            else if(line_len > 8 && strncasecmp((const char *)line, "x-guid: ", 8) == 0)
                CUT_OUT_FIELD(session->mail_x_guid_ptr, session->mail_x_guid_len, 8);
            else if(line_len > 14 && strncasecmp((const char *)line, "x-has-attach: ", 14) == 0)
                CUT_OUT_FIELD(session->mail_x_has_attach_ptr, session->mail_x_has_attach_len, 14);
            else if (line_len > 10 && strncasecmp((const char *)line, "x-mailer: ", 10) == 0) {
                CUT_OUT_FIELD(session->mail_x_mailer_ptr, session->mail_x_mailer_len, 10);

                strncat(session->x_mailers, (const char *)(line + 10), line_len - 10);
                strcat(session->x_mailers, ",");
                session->x_mailers_num++;
            }
            else if (line_len > 14 && strncasecmp((const char *)line, "Mime-Version: ", 14) == 0) {
                CUT_OUT_FIELD(session->mail_mime_version_ptr, session->mail_mime_version_len, 14);

                strncat(session->mime_vers, (const char *)(line + 14), line_len - 14);
                strcat(session->mime_vers, ",");
                session->mime_vers_num++;
            }
            else if (line_len > 12 && strncasecmp((const char *)line, "message-id: ", 12) == 0) {
                CUT_OUT_FIELD(session->mail_message_id_ptr, session->mail_message_id_len, 12);

                strncat(session->msg_ids, (const char *)(line + 12), line_len - 12);
                strcat(session->msg_ids, ",");
                session->msg_ids_num++;
            }
            else if(line_len > 12 && strncasecmp((const char *)line, "content-id: ", 12) == 0)
                CUT_OUT_FIELD(session->mail_content_id_ptr, session->mail_content_id_len, 12);
            else if(line_len > 10 && strncasecmp((const char *)line, "comments: ", 10) == 0)
                CUT_OUT_FIELD(session->mail_comments_ptr, session->mail_comments_len, 10);
            else if(line_len > 10 && strncasecmp((const char *)line, "keywords: ", 10) == 0)
                CUT_OUT_FIELD(session->mail_keywords_ptr, session->mail_keywords_len, 10);
            else if(line_len > 13 && strncasecmp((const char *)line, "resent-date: ", 13) == 0)
                CUT_OUT_FIELD(session->mail_resent_date_ptr, session->mail_resent_date_len, 13);
            else if(line_len > 13 && strncasecmp((const char *)line, "resent-from: ", 13) == 0)
                CUT_OUT_FIELD(session->mail_resent_from_ptr, session->mail_resent_from_len, 13);
            else if(line_len > 15 && strncasecmp((const char *)line, "resent-sender: ", 15) == 0)
                CUT_OUT_FIELD(session->mail_resent_sender_ptr, session->mail_resent_sender_len, 15);
            else if(line_len > 11 && strncasecmp((const char *)line, "resent-to: ", 11) == 0)
                CUT_OUT_FIELD(session->mail_resent_to_ptr, session->mail_resent_to_len, 11);
            else if(line_len > 11 && strncasecmp((const char *)line, "resent-cc: ", 11) == 0)
                CUT_OUT_FIELD(session->mail_resent_cc_ptr, session->mail_resent_cc_len, 11);
            else if(line_len > 12 && strncasecmp((const char *)line, "resent-bcc: ", 12) == 0)
                CUT_OUT_FIELD(session->mail_resent_bcc_ptr, session->mail_resent_bcc_len, 12);
            else if(line_len > 19 && strncasecmp((const char *)line, "resent-message-id: ", 19) == 0)
                CUT_OUT_FIELD(session->mail_resent_message_id_ptr, session->mail_resent_message_id_len, 19);
            else if(line_len > 13 && strncasecmp((const char *)line, "return-path: ", 13) == 0)
                CUT_OUT_FIELD(session->mail_return_path_ptr, session->mail_return_path_len, 13);
            else if(line_len > 10 && strncasecmp((const char *)line, "received: ", 10) == 0)
            {
                session->mail_received_ptr = line + 10;
                session->mail_received_len = get_smtp_field(payload, payload_len, &session->mail_received_ptr);
                //此处不能用下面的偏移量,只能在这里完成偏移
                offset += session->mail_received_len + 2;
                line = &payload[offset];

                strncat(session->receiveds, (const char *)session->mail_received_ptr, session->mail_received_len);
                strcat(session->receiveds, ",");
                session->receiveds_num++;

                extract_received_infos(session->mail_received_ptr, session->mail_received_len, session);

                continue;
            }
            else if(line_len > 13 && strncasecmp((const char *)line, "in-reply-to: ", 13) == 0)
                CUT_OUT_FIELD(session->mail_in_reply_to_ptr, session->mail_in_reply_to_len, 13);
            else if(line_len > 27 && strncasecmp((const char *)line, "content-transfer-encoding: ", 27) == 0)
                CUT_OUT_FIELD(session->mail_content_transfer_encoding_ptr, session->mail_content_transfer_encoding_len, 27);
            else if(line_len > 15 && strncasecmp((const char *)line, "delivery-date: ", 15) == 0)
                CUT_OUT_FIELD(session->mail_delivery_date_ptr, session->mail_delivery_date_len, 15);
            else if(line_len > 22 && strncasecmp((const char *)line, "latest-delivery-time: ", 22) == 0)
                CUT_OUT_FIELD(session->mail_latest_delivery_time_ptr, session->mail_latest_delivery_time_len, 22);
            else if(line_len > 21 && strncasecmp((const char *)line, "content-description: ", 21) == 0)
                CUT_OUT_FIELD(session->mail_content_description_ptr, session->mail_content_description_len, 21);
            else if(line_len > 10 && strncasecmp((const char *)line, "reply-to: ", 10) == 0)
                CUT_OUT_FIELD(session->mail_reply_to_ptr, session->mail_reply_to_len, 10);
            else if(line_len > 12 && strncasecmp((const char *)line, "references: ", 12) == 0)
                CUT_OUT_FIELD(session->mail_references_ptr, session->mail_references_len, 12);
            else if(line_len > 15 && strncasecmp((const char *)line, "autoforwarded: ", 15) == 0)
                CUT_OUT_FIELD(session->mail_autoforwarded_ptr, session->mail_autoforwarded_len, 15);
            else if(line_len > 15 && strncasecmp((const char *)line, "autosubmitted: ", 15) == 0)
                CUT_OUT_FIELD(session->mail_autosubmitted_ptr, session->mail_autosubmitted_len, 15);
            else if(line_len > 25 && strncasecmp((const char *)line, "x400-content-identifier: ", 25) == 0)
                CUT_OUT_FIELD(session->mail_x400_content_identifier_ptr, session->mail_x400_content_identifier_len, 25);
            else if(line_len > 18 && strncasecmp((const char *)line, "content-language: ", 18) == 0)
                CUT_OUT_FIELD(session->mail_content_language_ptr, session->mail_content_language_len, 18);
            else if(line_len > 12 && strncasecmp((const char *)line, "conversion: ", 12) == 0)
                CUT_OUT_FIELD(session->mail_conversion_ptr, session->mail_conversion_len, 12);
            else if(line_len > 22 && strncasecmp((const char *)line, "conversion-with-loss: ", 22) == 0)
                CUT_OUT_FIELD(session->mail_conversion_with_loss_ptr, session->mail_conversion_with_loss_len, 22);
            else if(line_len > 32 && strncasecmp((const char *)line, "discarded-x400-ipms-extensions: ", 32) == 0)
                CUT_OUT_FIELD(session->mail_discarded_x400_ipms_extensions_ptr, session->mail_discarded_x400_ipms_extensions_len, 32);
            else if(line_len > 31 && strncasecmp((const char *)line, "discarded-x400-mts-extensions: ", 31) == 0)
                CUT_OUT_FIELD(session->mail_discarded_x400_mts_extensions_ptr, session->mail_discarded_x400_mts_extensions_len, 31);
            else if(line_len > 22 && strncasecmp((const char *)line, "dl-expansion-history: ", 22) == 0)
                CUT_OUT_FIELD(session->mail_dl_expansion_history_ptr, session->mail_dl_expansion_history_len, 22);
            else if(line_len > 19 && strncasecmp((const char *)line, "deferred-delivery: ", 19) == 0)
                CUT_OUT_FIELD(session->mail_deferred_delivery_ptr, session->mail_deferred_delivery_len, 19);
            else if(line_len > 9 && strncasecmp((const char *)line, "expires: ", 9) == 0)
                CUT_OUT_FIELD(session->mail_expires_ptr, session->mail_expires_len, 9);
            else if(line_len > 12 && strncasecmp((const char *)line, "importance: ", 12) == 0)
                CUT_OUT_FIELD(session->mail_importance_ptr, session->mail_importance_len, 12);
            else if(line_len > 17 && strncasecmp((const char *)line, "incomplete-copy: ", 17) == 0)
                CUT_OUT_FIELD(session->mail_incomplete_copy_ptr, session->mail_incomplete_copy_len, 17);
            else if(line_len > 14 && strncasecmp((const char *)line, "message-type: ", 14) == 0)
                CUT_OUT_FIELD(session->mail_message_type_ptr, session->mail_message_type_len, 14);
            else if(line_len > 36 && strncasecmp((const char *)line, "original-encoded-information-types: ", 36) == 0)
                CUT_OUT_FIELD(session->mail_original_encoded_information_types_ptr, session->mail_original_encoded_information_types_len, 36);
            else if(line_len > 27 && strncasecmp((const char *)line, "originator-return-address: ", 27) == 0)
                CUT_OUT_FIELD(session->mail_originator_return_address_ptr, session->mail_originator_return_address_len, 27);
            else if(line_len > 10 && strncasecmp((const char *)line, "priority: ", 10) == 0)
                CUT_OUT_FIELD(session->mail_priority_ptr, session->mail_priority_len, 10);
            else if(line_len > 10 && strncasecmp((const char *)line, "reply-by: ", 10) == 0)
                CUT_OUT_FIELD(session->mail_reply_by_ptr, session->mail_reply_by_len, 10);
            else if(line_len > 13 && strncasecmp((const char *)line, "sensitivity: ", 13) == 0)
                CUT_OUT_FIELD(session->mail_sensitivity_ptr, session->mail_sensitivity_len, 13);
            else if(line_len > 12 && strncasecmp((const char *)line, "supersedes: ", 12) == 0)
                CUT_OUT_FIELD(session->mail_supersedes_ptr, session->mail_supersedes_len, 12);
            else if(line_len > 19 && strncasecmp((const char *)line, "x400-content-type: ", 19) == 0)
                CUT_OUT_FIELD(session->mail_x400_content_type_ptr, session->mail_x400_content_type_len, 19);
            else if(line_len > 21 && strncasecmp((const char *)line, "x400-mts-identifier: ", 21) == 0)
                CUT_OUT_FIELD(session->mail_x400_mts_identifier_ptr, session->mail_x400_mts_identifier_len, 21);
            else if(line_len > 17 && strncasecmp((const char *)line, "x400-originator: ", 17) == 0)
                CUT_OUT_FIELD(session->mail_x400_originator_ptr, session->mail_x400_originator_len, 17);
            else if(line_len > 15 && strncasecmp((const char *)line, "x400-received: ", 15) == 0)
                CUT_OUT_FIELD(session->mail_x400_received_ptr, session->mail_x400_received_len, 15);
            else if(line_len > 17 && strncasecmp((const char *)line, "x400-recipients: ", 17) == 0)
                CUT_OUT_FIELD(session->mail_x400_recipients_ptr, session->mail_x400_recipients_len, 17);
            else if(line_len > 14 && strncasecmp((const char *)line, "delivered-to: ", 14) == 0)
                CUT_OUT_FIELD(session->mail_delivered_to_ptr, session->mail_delivered_to_len, 14);
            else if(line_len > 14 && strncasecmp((const char *)line, "thread-index: ", 14) == 0)
                CUT_OUT_FIELD(session->mail_thread_index_ptr, session->mail_thread_index_len, 14);
            else if(line_len > 11 && strncasecmp((const char *)line, "x-mimeole: ", 11) == 0)
                CUT_OUT_FIELD(session->mail_x_mimeole_ptr, session->mail_x_mimeole_len, 11);
            else if(line_len > 13 && strncasecmp((const char *)line, "expiry-date: ", 13) == 0)
                CUT_OUT_FIELD(session->mail_expiry_date_ptr, session->mail_expiry_date_len, 13);
            else if(line_len > 22 && strncasecmp((const char *)line, "x-ms-tnef-correlator: ", 22) == 0)
                CUT_OUT_FIELD(session->mail_x_ms_tnef_correlator_ptr, session->mail_x_ms_tnef_correlator_len, 22);
            else if(line_len > 8 && strncasecmp((const char *)line, "x-uidl: ", 8) == 0)
                CUT_OUT_FIELD(session->mail_x_uidl_ptr, session->mail_x_uidl_len, 8);
            else if(line_len > 26 && strncasecmp((const char *)line, "x-authentication-warning: ", 26) == 0)
                CUT_OUT_FIELD(session->mail_x_authentication_warning_ptr, session->mail_x_authentication_warning_len, 26);
            else if(line_len > 17 && strncasecmp((const char *)line, "x-virus-scanned: ", 17) == 0)
                CUT_OUT_FIELD(session->mail_x_virus_scanned_ptr, session->mail_x_virus_scanned_len, 17);
            else if(line_len > 11 && strncasecmp((const char *)line, "sio-label: ", 11) == 0)
                CUT_OUT_FIELD(session->mail_sio_label_ptr, session->mail_sio_label_len, 11);
            else if (line_len > 18 && strncasecmp((const char *)line, "x-originating-ip: ", 18) == 0)
                CUT_OUT_FIELD(session->x_ori_ip_ptr, session->x_ori_ip_len, 18);
        }
        offset += line_len + 2;
        line = &payload[offset];
    }
    //解析附件信息
    if (session->mail_content_type_ptr)
    {
        if(0 == strncasecmp((const char*)session->mail_content_type_ptr,"multipart/mixed", strlen("multipart/mixed"))
            || 0 == strncasecmp((const char*)session->mail_content_type_ptr, "multipart/alternative", strlen("multipart/alternative")))
            dissect_attachments_info(payload, payload_len,session, offset);
    }
}


#if 0
static void identify_smtp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    int line_len;
    if (g_config.protocol_switch[PROTOCOL_MAIL_SMTP] == 0)
        return;
    if (payload_len > 2 /*&& get_uint16_ntohs(payload, payload_len - 2) == 0x0d0a*/) {
        if (flow->pkt_first_line.has_search == 0) {
            line_len = find_packet_line_end(payload, payload_len);
            flow->pkt_first_line.has_search = 1;
            flow->pkt_first_line.linelen = line_len;
        } else {
            line_len = flow->pkt_first_line.linelen;
        }
        if (line_len >= 3) {
            if (memcmp(payload, "220", 3) == 0
                    || memcmp(payload, "250", 3) == 0
                    || memcmp(payload, "235", 3) == 0
                    || memcmp(payload, "334", 3) == 0
                    || memcmp(payload, "354", 3) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            }
        }
        if (line_len >= 5) {
            if ((((payload[0] == 'H' || payload[0] == 'h') && (payload[1] == 'E' || payload[1] == 'e'))
                        || ((payload[0] == 'E' || payload[0] == 'e') && (payload[1] == 'H' || payload[1] == 'h')))
                    && (payload[2] == 'L' || payload[2] == 'l')
                    && (payload[3] == 'O' || payload[3] == 'o')
                    && payload[4] == ' ') {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            } else if (strncasecmp((const char *)payload, "MAIL ", 5) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            } else if (strncasecmp((const char *)payload, "RCPT ", 5) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            } else if (strncasecmp((const char *)payload, "AUTH ", 5) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            }
        }
        if (line_len == 8) {
            if (strncasecmp((const char *)payload, "STARTTLS", 8) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            }
        }

        if (line_len == 4) {
            if (strncasecmp((const char *)payload, "DATA", 4) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            } else if (strncasecmp((const char *)payload, "NOOP", 4) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            } else if (strncasecmp((const char *)payload, "REST", 4) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_SMTP;
                return;
            }
        }
    }
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_MAIL_SMTP);
    return;
}
#endif

static void identify_smtp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    int line_len;
    if (g_config.protocol_switch[PROTOCOL_MAIL_ESMTP] == 0)
        return;

    if (payload_len > 2 /*&& get_uint16_ntohs(payload, payload_len - 2) == 0x0d0a*/) {
        if (flow->pkt_first_line.has_search == 0) {
            line_len = find_packet_line_end(payload, payload_len);
            flow->pkt_first_line.has_search = 1;
            flow->pkt_first_line.linelen = line_len;
        } else {
            line_len = flow->pkt_first_line.linelen;
        }
        if (line_len >= 3) {
            if (memcmp(payload, "220", 3) == 0
                    || memcmp(payload, "250", 3) == 0
                    || memcmp(payload, "235", 3) == 0
                    || memcmp(payload, "334", 3) == 0
                    || memcmp(payload, "354", 3) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            }
        }
        if (line_len >= 5) {
            if ((((payload[0] == 'H' || payload[0] == 'h') && (payload[1] == 'E' || payload[1] == 'e'))
                        || ((payload[0] == 'E' || payload[0] == 'e') && (payload[1] == 'H' || payload[1] == 'h')))
                    && (payload[2] == 'L' || payload[2] == 'l')
                    && (payload[3] == 'O' || payload[3] == 'o')
                    && payload[4] == ' ') {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            } else if (strncasecmp((const char *)payload, "MAIL ", 5) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            } else if (strncasecmp((const char *)payload, "RCPT ", 5) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            } else if (strncasecmp((const char *)payload, "AUTH ", 5) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            }
        }
        if (line_len == 8) {
            if (strncasecmp((const char *)payload, "STARTTLS", 8) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            }
        }

        if (line_len == 4) {
            if (strncasecmp((const char *)payload, "DATA", 4) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            } else if (strncasecmp((const char *)payload, "NOOP", 4) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            } else if (strncasecmp((const char *)payload, "REST", 4) == 0) {
                flow->real_protocol_id = PROTOCOL_MAIL_ESMTP;
                return;
            }
        }
    }
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_MAIL_ESMTP);
    return;
}


static int write_smtp_eml_data(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len,struct smtp_session *_session)
{
  uint32_t index=0;
  uint32_t actual_len=0;
  uint32_t expect_len=0;
  struct   list_head *reassemble;
  uint32_t reassemble_result_len;
  uint8_t *reassemble_result = NULL;
  int ret = 0;
  EmailSession  *session = (EmailSession *)flow->app_session;

  ret = email_get_reassemble(flow, direction, &reassemble_result, &expect_len);
  if (ret != 0) {
    if (reassemble_result == NULL) {
      dpi_free(reassemble_result);
    }
    return -1;
  }
  // reassemble_result[expect_len + 1499] = '\0'; // 安全
  dpi_email_t *email = dpi_email_create();
  dpi_email_imf(email, NULL, (const char *)reassemble_result, expect_len);
//   dissect_imf(reassemble_result, expect_len, session);
  // if(session->mail_from_len>0 || session->mail_to_len>0){
  if (g_config.smtp_content) {
    get_smtp_filename(session->session_type, flow->thread_id, session->mail_filename, sizeof(session->mail_filename));
    FILE *fp = fopen(session->mail_filename, "w");
    if (fp) {
      fwrite(reassemble_result, expect_len, 1, fp);
      fclose(fp);
      session->eml_filesize = expect_len;
      if (expect_len >= actual_len) {
                float drop_percent =
                    ((expect_len - actual_len) / (expect_len * 1.0)) * 100;
                snprintf((char *)session->drop_data, 16, "%0.2f", drop_percent);
      }
      if (expect_len != 0) {
                float percent_data = (actual_len / (expect_len * 1.0)) * 100;
                // printf("reassemble_result_len:%d,expect:%d,
                // percent:%0.2f%%\n",reassemble_result_len,expect_len,percent_data);
      }
      strncpy(session->login_status, "YES", COMMON_STATUS_LEN);
    } else {
      session->mail_filename[0] = '0';
    }
  }

  strcpy(session->email_type, "SMTP");
  write_email_log(flow, direction, email);
  dpi_free(reassemble_result);
  dpi_email_destory(email);

    if (direction == FLOW_DIR_SRC2DST)
        tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
    else
        tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);

    return 0;
}

/*
* smtp协议的解析函数
* 主要是根据每个数据包的命令得到smtp的当前状态，并缓存一些信息在会话的app_session中
*/
static int dissect_smtp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
  const uint8_t *line;
  uint8_t        is_request = 0;
  int            line_len;
  EmailSession  *session;
  uint16_t       copy_len;
  // struct smtp_session *session;
  if (flow->app_session == NULL) {
    flow->app_session = dpi_malloc(sizeof(EmailSession));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }

    session = (EmailSession *)flow->app_session;
    memset(session, 0, sizeof(EmailSession));
    session->state = EMAIL_STATE_READING_CMDS;
    }

  session    = (EmailSession *)flow->app_session;
  is_request = (direction == FLOW_DIR_SRC2DST) ? 1 : 0;


  if (flag == DISSECT_PKT_FIANL) {
    if (flow->reassemble_flag == 1) { /* for mail out-of-order */
      write_smtp_eml_data(flow, direction, seq, payload, payload_len, NULL);
    } else {
      tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
      tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);
      flow->reassemble_pkt_src2dst_num = 0;
      flow->reassemble_pkt_dst2src_num = 0;
    }
      // reset_session(flow->app_session);
      return PKT_OK;
  }

    if (direction == FLOW_DIR_DST2SRC)
    {
        //mail server 在响应数据包里获取
        if(0 == memcmp(payload,"220 ",4))
        {
            if(4 == flow->ip_version)
                get_iparray_to_string((char*)session->send_host_ip4, sizeof(session->send_host_ip4), direction == FLOW_DIR_SRC2DST ? flow->tuple.inner.ip_src : flow->tuple_reverse.inner.ip_src);
            else
                get_ip6string((char*)session->send_host_ip6, sizeof(session->send_host_ip6), direction == FLOW_DIR_SRC2DST ? flow->tuple.inner.ip_src : flow->tuple_reverse.inner.ip_src);

            if(payload_len > 4)
            {
                const uint8_t *begin = payload + 4;
                uint32_t length  = payload_len - 4;

                const uint8_t* p0 = memchr(begin, ' ',   length);
                const uint8_t* p1 = memstr(begin, "\r\n", length);
                const uint8_t* p2 = memchr(begin, ';',   length);
                if(p0 && p1 && p0 < p1)
                {
                    unsigned min_len = DPI_MIN((unsigned)(p0 - begin), sizeof(session->mail_server_name_ptr) - 1);
                    memcpy(session->mail_server_name_ptr, begin, min_len);
                    session->mail_server_name_ptr[min_len] = 0;

                    if(!p2){
                        min_len = DPI_MIN((unsigned)(p1 - p0 - 1), sizeof(session->mail_server_soft_ptr) - 1);
                        memcpy(session->mail_server_soft_ptr, p0 + 1, min_len);
                        session->mail_server_soft_ptr[min_len] = 0;
                    }
                    else if(p0 < p2 && p2 < p1){
                        min_len = DPI_MIN((unsigned)(p2 - p0 - 1), sizeof(session->mail_server_soft_ptr) - 1);
                        memcpy(session->mail_server_soft_ptr, p0 + 1, min_len);
                        session->mail_server_soft_ptr[min_len] = 0;
                    }
                }
            }
        }

        if(0 == memcmp(payload,"250-",4))
        {
            const uint8_t* p0 = (uint8_t*)strstr((const char*)payload, "[");
            if(p0)
            {
                const uint8_t* p1 = (uint8_t*)strstr((const char*)p0, "]");
                const uint8_t* p2 = (uint8_t*)strstr((const char*)p0, "\r\n");
                if(p1 && p2 && p1 < p2)
                {
                    unsigned min_len = DPI_MIN((unsigned)(p1 - p0 - 1), sizeof(session->mail_recv_host_ip_ptr) - 1);
                    memcpy(session->mail_recv_host_ip_ptr, p0 + 1, min_len);
                    session->mail_recv_host_ip_ptr[min_len] = 0;
                }
            }
        }

        if(0 == memcmp(payload,"235",3)){
            strncpy(session->login_status,"YES",COMMON_STATUS_LEN);
            // write_email_log(flow, direction, NULL);
        }

        if(0 == memcmp(payload,"535",3)){
            strncpy(session->login_status,"NO",COMMON_STATUS_LEN);
            // write_email_log(flow, direction, NULL);
        }

        return PKT_OK; /*response do not need dissect*/
    }

    /* 直接重组以防乱序 */
    if(session->mail_begin_seq && seq >= session->mail_begin_seq)
        goto MAIL_REASSEM;

    if (payload_len < 2 || get_uint16_ntohs(payload, payload_len - 2) != 0x0d0a)
        return PKT_DROP;

    if(  (payload_len > 10 && strncasecmp((const char*)payload, "MAIL FROM:", 10) == 0)
       ||(payload_len > 8  && strncasecmp((const char*)payload, "RCPT TO:",    8) == 0)
       ||(payload_len > 6  && strncasecmp((const char*)payload, "DATA\r\n",    6) == 0)
       ||(payload_len > 6  && strncasecmp((const char*)payload, "Date: ",      6) == 0) )
    {
        flow->reassemble_flag = 1;
        session->mail_begin_seq = seq;
        goto MAIL_REASSEM;
    }

    if(payload_len > 5 && (strncasecmp((const char*)payload, "helo ", 5) == 0 || strncasecmp((const char*)payload, "ehlo ", 5) == 0) )
    {
        session->has_ensure_type = 1;
        if(payload[0] == 'H' || payload[0] == 'h')
            session->session_type = SESSION_TYPE_SMTP;
        else
            session->session_type = SESSION_TYPE_ESMTP;
        const uint8_t* p0 = payload + 4;
        const uint8_t* p1 = memstr(payload, "\r\n", payload_len);
        if(p0 && p1 && p1 > p0){
            unsigned min_len = DPI_MIN((unsigned)(p1 - p0 - 1), sizeof(session->mail_helo_ptr) - 1);
            memcpy(session->mail_helo_ptr, p0 + 1, min_len);
            session->mail_helo_ptr[min_len] = 0;
        }
        //write_smtp_log(flow, direction);
        return PKT_OK;
    }

    if(seq == session->user_seq){
        copy_len = payload_len - 2 > sizeof(session->auth_name) - 1 ? sizeof(session->auth_name) - 1 : payload_len - 2;
        memcpy(session->auth_name, payload, copy_len);
        session->auth_name[copy_len] = 0;
        if(!session->passwd_seq)
            session->passwd_seq = seq + payload_len;
    }
    if(seq == session->passwd_seq){
        copy_len = payload_len - 2 > sizeof(session->auth_passwd) - 1 ? sizeof(session->auth_passwd) - 1 : payload_len - 2;
        memcpy(session->auth_passwd, payload, copy_len);
        session->auth_passwd[copy_len] = 0;
    }
    if(payload_len >= 12 && strncasecmp((const char *)payload, "AUTH PLAIN", 10) == 0){
        if(!session->has_ensure_type)
            session->session_type = SESSION_TYPE_ESMTP;
        if(get_uint16_ntohs(payload, 10) == 0x0d0a)
            session->passwd_seq = session->user_seq = seq + 12;
        else if(payload_len > 13){
            copy_len = payload_len - 13 > sizeof(session->auth_name) - 1 ? sizeof(session->auth_name) - 1 : payload_len - 13;
            memcpy(session->auth_name, payload+11, copy_len);
            session->auth_name[copy_len] = 0;
            copy_len = payload_len - 13 > sizeof(session->auth_passwd) - 1 ? sizeof(session->auth_passwd) - 1 : payload_len - 13;
            memcpy(session->auth_passwd, payload+11, copy_len);
            session->auth_passwd[copy_len] = 0;
        }
        return PKT_OK;
    }
    if(payload_len >= 12 && strncasecmp((const char *)payload, "AUTH LOGIN", 10) == 0){
        if(!session->has_ensure_type)
            session->session_type = SESSION_TYPE_ESMTP;
        if(get_uint16_ntohs(payload, 10) == 0x0d0a)
            session->user_seq = seq + payload_len;
        else if(payload_len > 12){
            copy_len = payload_len - 12 > sizeof(session->auth_name) - 1 ? sizeof(session->auth_name) - 1 : payload_len - 2;
            memcpy(session->auth_name, payload+2, copy_len);
            session->auth_name[copy_len] = 0;
            session->passwd_seq = seq + payload_len;
        }
        return PKT_OK;
    }

    return PKT_OK;

MAIL_REASSEM:
    if (direction == FLOW_DIR_SRC2DST && flow->reassemble_pkt_src2dst_num < g_config.tcp_resseamble_max_num) {
        tcp_reassemble_add_item(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
        flow->reassemble_pkt_src2dst_num++;
    } else if (direction == FLOW_DIR_DST2SRC && flow->reassemble_pkt_dst2src_num < g_config.tcp_resseamble_max_num) {
        tcp_reassemble_add_item(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len, seq, payload, payload_len);
        flow->reassemble_pkt_dst2src_num++;
    } else{
        return PKT_DROP;
    }

    return PKT_OK;
}

static void init_smtp_dissector(void)
{
    dpi_register_proto_schema(smtp_field_array_sdt,EM_SMTP_MAX,"esmtp");
    dpi_register_proto_schema(smtp_field_array_sdt,EM_SMTP_MAX,"smtp");
    port_add_proto_head(IPPROTO_TCP, SMTP_PORT, PROTOCOL_MAIL_ESMTP);
    tcp_detection_array[PROTOCOL_MAIL_ESMTP].identify_type = DPI_IDENTIFY_CONTENT;
    tcp_detection_array[PROTOCOL_MAIL_ESMTP].proto = PROTOCOL_MAIL_ESMTP;
    tcp_detection_array[PROTOCOL_MAIL_ESMTP].identify_func = identify_smtp;
    tcp_detection_array[PROTOCOL_MAIL_ESMTP].dissect_func = dissect_smtp;
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_MAIL_ESMTP].excluded_protocol_bitmask, PROTOCOL_MAIL_ESMTP);
    map_fields_info_register(smtp_field_array_sdt,PROTOCOL_MAIL_SMTP, EM_SMTP_MAX,"smtp");
	return;
}

static __attribute((constructor)) void    before_mail_smtp(void){
    register_tbl_array(TBL_LOG_MAIL_SMTP, 0, "smtp", init_smtp_dissector);
}
