/****************************************************************************************
 * 文 件 名 : dpi_tcp_reassemble.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_TCP_REASSEMBLE_H_
#define _DPI_TCP_REASSEMBLE_H_

#include <sys/types.h>
#include <stdint.h>

#include "list.h"
#include "sdt_types.h"
#include "yaProtoRecord/precord.h"
#include "sdtapp_interface.h"
#include "libsdt/libsdt_interface.h"

#define TCP_PAYLOAD_MAX_LEN      2000 //MDJ 现场是1565(1514+51): sdt_in_pcap, p pkt->pkt_len
#define TCP_PADDING_ELEMEMT_LEN  1500

enum  EM_SDT_ACTUAL_DATA_TYPE{
    SDT_OUT_ACTUAL_DATA_EVENT=0,
    SDT_OUT_ACTUAL_DATA_DUMP_PCAP,
    SDT_OUT_ACTUAL_DATA_SYSLOG,
};

struct tcp_reassemble
{
    uint32_t seq;
    uint32_t ack;
    uint16_t payload_len;
    char payload[TCP_PAYLOAD_MAX_LEN];
    uint8_t  flag;

    struct list_head node;
//    struct tcp_reassemble *next;
};

struct tcp_reassemble_args
{
     uint16_t payload_len;
     uint32_t seq;
     uint32_t ack;
     uint8_t flag;
};

#define MDJ_MAC_POOL_MAX 10 //MDJ 清分输出 MAC 负载均衡 最大值

struct packet_stream
{
    uint8_t          data_type;   /* 区分存储的数据类似是event还是pcap报文*/
    uint8_t          thread_id;
    uint64_t         flow_id;
    uint64_t         timestamp;
    uint32_t         flow_cycle;
    SdtAction_Enum   action_type;

    char             unitID[64];
    char             taskID[64];
    char             groupID[64];
    char             method[64];
    char             topicName[64];

    int  rule_mode;         //来自XML 20241120 牡丹江现场
    int  task_mode;         //来自XML 20241120 牡丹江现场
    int  task_sub_type;     //来自XML 20241120 牡丹江现场
    int  mode_param_num;    //来自XML 20241120 牡丹江现场
    char mode_param[10][6]; //来自XML 20241120 牡丹江现场

    uint32_t         rule_id;

    uint16_t         l3h_start;      /* ip层头部开始位置 */
    uint16_t         l4h_start;      /* 传输层头部开始位置 */
    uint16_t         l4payload_start;/* 传输层payload开始位置 */
    uint16_t         ip_len;         /* ip的数据长度，ipheader+传输header+payload_len 根据该值可以精确算出payload_len*/
    uint16_t         pkt_data_len;   /* 报文总长度，包括trailer数据长度 */
    uint8_t          pkt_data[TCP_PAYLOAD_MAX_LEN]; /* 报文数据存储，从以太层开始，包括trailer数据 */
    uint8_t          direction;
    uint32_t         seq;

    struct list_head node;  /* 双向包链表节点 */
    struct list_head snode; /* 单向包链表节点 */
    struct list_head tnode; /* tcp组包链表节点 */

    uint8_t         mac_hdr[256];  // 特殊mac头， 目前转发时用到 先用256B占位
    uint8_t         mac_flag;      // 1表示有值
    uint8_t         work_on_ringId; //从哪个队列出来的

    uint8_t         first_packet: 1;    // 流输出的第一帧
};


int tcp_reassemble_add_double_item(struct list_head *head, uint32_t *rsm_total_len,struct tcp_reassemble_args tcp_args,const uint8_t *payload);
int tcp_reassemble_add_item(struct list_head *head, uint32_t *rsm_total_len, uint32_t seq, const uint8_t *payload, const uint16_t payload_len);
int tcp_reassemble_do(struct list_head *head, uint8_t *result, uint32_t *result_len);
int tcp_reassemble_do_guesslen(struct list_head *head,  uint32_t *result_len, uint32_t *expect_len);
int tcp_reassemble_do_new(struct list_head *head, uint8_t *result, uint32_t *result_len, uint32_t *expect_len);
int tcp_reassemble_do_padding(struct list_head *head, uint8_t *result, uint32_t *result_len, uint32_t expect_len);
int tcp_reassemble_free(struct list_head *head, uint32_t *rsm_total_len);


int pkt_single_stream_add_item(struct list_head *head, struct packet_stream * new_item);
int pkt_stream_add_item(struct list_head *head,  uint32_t pkt_cnt, ProtoRecord pRec);

int pkt_tcp_rsm_add_item(uint16_t s, uint16_t d ,struct list_head *head, uint32_t pkt_cnt, struct packet_stream * new_item);

int pkt_single_stream_free_limit(struct list_head *head, int limit, int* count);
int pkt_stream_free_limit(struct list_head *head, int limit, int* count);

int pkt_stream_free_node(struct list_head *head);



#endif
