#!/usr/bin/env python3
"""
最终验证 SDX 和 HTTP 字段在 Arkime 前端的显示
"""

import json
import urllib.request
import urllib.parse
from datetime import datetime

def test_field_registration():
    """测试字段注册"""
    print("=== 测试字段注册 ===")
    
    try:
        req = urllib.request.Request("http://localhost:9200/arkime_fields_v30/_search?size=100&pretty")
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            data = json.loads(result)
            
            fields_by_group = {}
            for hit in data['hits']['hits']:
                field_id = hit['_id']
                source = hit['_source']
                group = source.get('group', 'unknown')
                
                if group not in fields_by_group:
                    fields_by_group[group] = []
                fields_by_group[group].append({
                    'id': field_id,
                    'friendlyName': source.get('friendlyName', 'unknown'),
                    'type': source.get('type', 'unknown')
                })
            
            print(f"总共注册了 {data['hits']['total']['value']} 个字段:")
            for group, fields in sorted(fields_by_group.items()):
                print(f"\n组 '{group}' ({len(fields)} 个字段):")
                for field in fields:
                    print(f"  ✅ {field['id']}: {field['friendlyName']} ({field['type']})")
                    
    except Exception as e:
        print(f"❌ 字段注册测试失败: {e}")

def test_session_data():
    """测试会话数据"""
    print("\n=== 测试会话数据 ===")
    
    date_str = datetime.now().strftime("%y%m%d")
    index_name = f"arkime_sessions3-{date_str}"
    
    try:
        req = urllib.request.Request(f"http://localhost:9200/{index_name}/_search?pretty")
        with urllib.request.urlopen(req) as response:
            result = response.read().decode('utf-8')
            data = json.loads(result)
            
            hit_count = data['hits']['total']['value']
            print(f"索引 {index_name} 中有 {hit_count} 个会话")
            
            if hit_count > 0:
                for hit in data['hits']['hits']:
                    session = hit['_source']
                    print(f"\n会话 ID: {hit['_id']}")
                    print(f"  协议: {session.get('protocols', [])}")
                    print(f"  源IP: {session.get('source', {}).get('ip', 'unknown')}")
                    print(f"  目标IP: {session.get('destination', {}).get('ip', 'unknown')}")
                    
                    if 'sdx' in session:
                        print(f"  ✅ SDX 数据: {session['sdx']}")
                    
                    if 'http' in session:
                        http_data = session['http']
                        print(f"  ✅ HTTP 方法: {http_data.get('method', [])}")
                        print(f"  ✅ HTTP 主机: {http_data.get('host', [])}")
                        print(f"  ✅ HTTP URI: {http_data.get('uri', [])}")
            else:
                print("❌ 没有找到会话数据")
                
    except Exception as e:
        print(f"❌ 会话数据测试失败: {e}")

def test_search_queries():
    """测试搜索查询"""
    print("\n=== 测试搜索查询 ===")
    
    date_str = datetime.now().strftime("%y%m%d")
    index_name = f"arkime_sessions3-{date_str}"
    
    test_queries = [
        "protocols:sdx",
        "protocols:http", 
        "sdx.linename1:11111",
        "sdx.linename2:22222",
        "http.method:HEAD",
        "http.host:windowsupdate.microsoft.com"
    ]
    
    for query in test_queries:
        try:
            encoded_query = urllib.parse.quote(query)
            url = f"http://localhost:9200/{index_name}/_search?q={encoded_query}&pretty"
            req = urllib.request.Request(url)
            
            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                data = json.loads(result)
                
                hit_count = data['hits']['total']['value']
                if hit_count > 0:
                    print(f"  ✅ 查询 '{query}': 找到 {hit_count} 个结果")
                else:
                    print(f"  ❌ 查询 '{query}': 未找到结果")
                    
        except Exception as e:
            print(f"  ❌ 查询 '{query}' 失败: {e}")

def test_arkime_frontend():
    """测试 Arkime 前端连接"""
    print("\n=== 测试 Arkime 前端连接 ===")
    
    try:
        req = urllib.request.Request("http://localhost:8005")
        with urllib.request.urlopen(req, timeout=5) as response:
            if response.status == 200:
                print("  ✅ Arkime 前端服务正常运行")
            elif response.status == 401:
                print("  ⚠️  Arkime 前端需要认证（正常）")
            else:
                print(f"  ❌ Arkime 前端状态异常: {response.status}")
                
    except urllib.error.HTTPError as e:
        if e.code == 401:
            print("  ⚠️  Arkime 前端需要认证（正常）")
        else:
            print(f"  ❌ Arkime 前端错误: HTTP {e.code}")
    except Exception as e:
        print(f"  ❌ 无法连接到 Arkime 前端: {e}")

def generate_usage_guide():
    """生成使用指南"""
    print("\n=== 使用指南 ===")
    print("现在您可以在 Arkime 前端使用 SDX 和 HTTP 字段了：")
    print()
    print("🌐 前端访问:")
    print("   URL: http://localhost:8005")
    print("   用户名: admin")
    print("   密码: (请查看用户数据库)")
    print()
    print("📊 SPI View 页面:")
    print("   - 应该会显示 'sdx' 和 'http' 组的字段卡片")
    print("   - SDX 组包含: SDX Line Name 1, SDX Line Name 2")
    print("   - HTTP 组包含: HTTP Method, HTTP Host, HTTP URI")
    print()
    print("🔍 Sessions 页面搜索:")
    print("   - protocols:sdx (查找包含 SDX 协议的会话)")
    print("   - protocols:http (查找包含 HTTP 协议的会话)")
    print("   - sdx.linename1==11111 (精确匹配 SDX 字段)")
    print("   - http.method==HEAD (精确匹配 HTTP 方法)")
    print("   - http.host==windowsupdate.microsoft.com (精确匹配主机)")
    print()
    print("⚙️  列配置:")
    print("   1. 在 Sessions 页面点击列配置按钮")
    print("   2. 在可用字段中找到 SDX 和 HTTP 相关字段")
    print("   3. 添加到显示列中")
    print("   4. 保存配置")
    print()
    print("🔧 故障排除:")
    print("   - 如果字段不显示，请刷新页面")
    print("   - 清除浏览器缓存")
    print("   - 重启 Arkime Viewer 服务")
    print("   - 检查 ES 中的字段定义")

def main():
    """主函数"""
    print("Arkime SDX & HTTP 字段最终验证")
    print("=" * 60)
    
    # 测试字段注册
    test_field_registration()
    
    # 测试会话数据
    test_session_data()
    
    # 测试搜索查询
    test_search_queries()
    
    # 测试前端连接
    test_arkime_frontend()
    
    # 生成使用指南
    generate_usage_guide()
    
    print("\n" + "=" * 60)
    print("✅ 验证完成！")
    print("SDX 和 HTTP 字段应该已经可以在 Arkime 前端正常使用了。")
    print("请打开浏览器访问 http://localhost:8005 查看效果。")

if __name__ == "__main__":
    main()
