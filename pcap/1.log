Jul 11 08:56:30 config.c:181 arkime_config_str(): rotateIndex=daily
Jul 11 08:56:30 config.c:181 arkime_config_str(): nodeClass=(null)
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): dontSaveTags=(null)
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): plugins=(null)
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): rootPlugins=(null)
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): smtpIpHeaders=X-Originating-IP:;X-Barracuda-Apparent-Source-IP:
Jul 11 08:56:30 config.c:181 arkime_config_str(): prefix=arkime_
Jul 11 08:56:30 config.c:181 arkime_config_str(): elasticsearch=http://localhost:9200
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): interface=ens33
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): pcapDir=/opt/arkime/raw
Jul 11 08:56:30 config.c:181 arkime_config_str(): bpf=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): yara=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): rirFile=/opt/arkime/etc/ipv4-address-space.csv
Jul 11 08:56:30 config.c:181 arkime_config_str(): ouiFile=/opt/arkime/etc/oui.txt
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): geoLite2ASN=/var/lib/GeoIP/GeoLite2-ASN.mmdb;/usr/share/GeoIP/GeoLite2-ASN.mmdb;/opt/arkime/etc/GeoLite2-ASN.mmdb
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): geoLite2Country=/var/lib/GeoIP/GeoLite2-Country.mmdb;/usr/share/GeoIP/GeoLite2-Country.mmdb;/opt/arkime/etc/GeoLite2-Country.mmdb
Jul 11 08:56:30 config.c:181 arkime_config_str(): dropUser=nobody
Jul 11 08:56:30 config.c:181 arkime_config_str(): dropGroup=daemon
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): pluginsDir=/opt/arkime/plugins
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): parsersDir=/opt/arkime/parsers
Jul 11 08:56:30 config.c:181 arkime_config_str(): caTrustFile=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): offlineFilenameRegex=(?i)\.(pcap|cap)$
Jul 11 08:56:30 config.c:181 arkime_config_str(): pcapDirTemplate=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): pcapDirAlgorithm=round-robin
Jul 11 08:56:30 config.c:322 arkime_config_double(): maxFileSizeG=0.010000
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxFileTimeM=0
Jul 11 08:56:30 config.c:291 arkime_config_int(): icmpTimeout=10
Jul 11 08:56:30 config.c:291 arkime_config_int(): udpTimeout=30
Jul 11 08:56:30 config.c:291 arkime_config_int(): tcpTimeout=600
Jul 11 08:56:30 config.c:291 arkime_config_int(): sctpTimeout=60
Jul 11 08:56:30 config.c:291 arkime_config_int(): espTimeout=600
Jul 11 08:56:30 config.c:291 arkime_config_int(): tcpSaveTimeout=720
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxStreams=1000000
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxPackets=10000
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxPacketsInQueue=200000
Jul 11 08:56:30 config.c:291 arkime_config_int(): dbBulkSize=1000000
Jul 11 08:56:30 config.c:291 arkime_config_int(): dbFlushTimeout=5
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxESConns=30
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxESRequests=500
Jul 11 08:56:30 config.c:291 arkime_config_int(): logEveryXPackets=100000
Jul 11 08:56:30 config.c:291 arkime_config_int(): pcapBufferSize=300000000
Jul 11 08:56:30 config.c:282 arkime_config_int(): INFO: Reseting pcapWriteSize since 65535 is less then the min 65536
Jul 11 08:56:30 config.c:291 arkime_config_int(): pcapWriteSize=65536
Jul 11 08:56:30 config.c:291 arkime_config_int(): fragsTimeout=480
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxFrags=10000
Jul 11 08:56:30 config.c:291 arkime_config_int(): snapLen=16384
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxMemPercentage=100
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxReqBody=64
Jul 11 08:56:30 config.c:291 arkime_config_int(): packetThreads=2
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): logUnknownProtocols=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): logESRequests=true
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): logFileCreation=true
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): logHTTPConnections=true
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseSMTP=true
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseSMTPHeaderAll=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseSMB=true
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): ja3Strings=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseQSValue=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseCookieValue=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseHTTPHeaderRequestAll=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseHTTPHeaderResponseAll=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): supportSha256=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): reqBodyOnlyUtf8=true
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): compressES=true
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): readTruncatedPackets=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): trackESP=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): yaraEveryPacket=true
Jul 11 08:56:30 config.c:181 arkime_config_str(): autoGenerateId=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): enablePacketLen=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): enablePacketDedup=true
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): saveUnknownPackets=(null)
Jul 11 08:56:30 config.c:1564 arkime_config_init(): maxFileSizeB: 10737418
Jul 11 08:56:30 config.c:291 arkime_config_int(): dedupSeconds=2
Jul 11 08:56:30 config.c:291 arkime_config_int(): dedupPackets=1048575
Jul 11 08:56:30 dedup.c:131 arkime_dedup_init(): seconds = 3 packets = 1048575 slots = 99991 size = 1999820 mem=96291333
Jul 11 08:56:30 config.c:291 arkime_config_int(): offlineDispatchAfter=2500
Jul 11 08:56:30 config.c:291 arkime_config_int(): httpVLanVNI=0
Jul 11 08:56:30 config.c:181 arkime_config_str(): elasticsearchAPIKey=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): elasticsearchBasicAuth=wuby:VictoR#.0.0
Jul 11 08:56:30 config.c:291 arkime_config_int(): esMaxRetries=2
Jul 11 08:56:30 config.c:181 arkime_config_str(): esClientCert=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): esClientKey=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): esClientKeyPass=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): esBulkQuery=/_bulk
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/_template/arkime_sessions3_template?filter_path=**._meta 0/96 1ms 2ms
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 0/127 0ms 2ms
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_stats/_doc/localhost 0/970 0ms 2ms
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_fields/_search?size=3000 0/104254 0ms 5ms
Jul 11 08:56:30 db.c:2844 arkime_db_init(): WARNING - No Geo Country file could be loaded, see https://arkime.com/settings#geolite2country
Jul 11 08:56:30 db.c:2855 arkime_db_init(): WARNING - No Geo ASN file could be loaded, see https://arkime.com/settings#geolite2asn
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): dbEsHealthCheck=true
Jul 11 08:56:30 config.c:181 arkime_config_str(): ecsEventProvider=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): ecsEventDataset=(null)
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): packetDropIpsFiles=(null)
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): yaraFastMode=true
Jul 11 08:56:30 config.c:181 arkime_config_str(): magicMode=both
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): disableParsers=arp.so
Jul 11 08:56:30 parsers.c:738 arkime_parsers_init(): Skipping arp.so in /opt/arkime/parsers since already loaded
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/bgp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/certs.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ciscometadata.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/dhcp.so
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): parseDNSRecordAll=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): dnsOutputAnswers=false
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/dns.so
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): ja4Raw=false
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/dtls.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/erspan.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/esp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/geneve.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/gre.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/gtp.so
Jul 11 08:56:30 config.c:291 arkime_config_int(): parseHTTPHeaderValueMaxLen=1024
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/http.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/http2.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/icmp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/igmp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/irc.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/isis.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/krb5.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ldap.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/lldp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/misc.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/modbus.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/mpls.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/mysql.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/nfs.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/nsh.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/oracle.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ospf.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/pim.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/postgresql.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ppp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/quic.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/radius.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/rpc.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/sctp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/smb.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/smtp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/snmp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/socks.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/ssh.so
Jul 11 08:56:30 config.c:291 arkime_config_int(): maxTcpOutOfOrderPackets=256
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/tcp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/tds.so
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): ja4Raw=false
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/tls.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/udp.so
Jul 11 08:56:30 parsers.c:782 arkime_parsers_init(): Loaded /opt/arkime/parsers/vxlan.so
Jul 11 08:56:30 config.c:291 arkime_config_int(): tcpClosingTimeout=5
Jul 11 08:56:30 config.c:181 arkime_config_str(): sessionIdTracking=none
Jul 11 08:56:30 session.c:925 arkime_session_init(): session hash size 10007 49999 732209 49999 10007 49999
Jul 11 08:56:30 session.c:609 arkime_session_load_stopped(): Load 0
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): overrideIpsFiles=(null)
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): rulesFiles=(null)
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): dontSaveBPFs=(null)
Jul 11 08:56:30 config.c:219 arkime_config_str_list(): minPacketsSaveBPFs=(null)
Jul 11 08:56:30 http.c:605 arkime_http_curl_watch_open_callback(): Connected 1/0 - http://localhost:9200   57492->[::1]:9200 - fd:9
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/1 ASYNC 200 http://localhost:9200/_cat/health?format=json 0/199 2ms 3ms
Jul 11 08:56:30 db.c:1879 arkime_db_health_check_cb(): WARNING - Elasticsearch is yellow and took 83ms to query health, this may cause issues.  See FAQ.
Jul 11 08:56:30 main.c:808 arkime_ready_gfunc(): maxDbField = 147 minInternalField = 995
Jul 11 08:56:30 config.c:181 arkime_config_str(): pcapWriteMethod=simple
Jul 11 08:56:30 config.c:291 arkime_config_int(): simpleMaxQ=2000
Jul 11 08:56:30 config.c:181 arkime_config_str(): simpleEncoding=(null)
Jul 11 08:56:30 config.c:181 arkime_config_str(): simpleCompression=none
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): gapPacketPos=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): simpleShortHeader=false
Jul 11 08:56:30 config.c:348 arkime_config_boolean(): localPcapIndex=false
Jul 11 08:56:30 config.c:291 arkime_config_int(): simpleFreeOutputBuffers=16
Jul 11 08:56:30 config.c:256 arkime_config_str_list(): filenameOps=
Jul 11 08:56:30 reader-libpcap-file.c:209 reader_libpcapfile_process(): Processing /home/<USER>/pcap/HTTP_一条流.pcap
Jul 11 08:56:30 writer-simple.c:675 writer_simple_thread(): THREAD 0x7f4e60d326c0
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 2/181 0ms 5ms
Jul 11 08:56:30 db.c:2225 arkime_db_create_file_full(): Creating file 119 with key >/arkime_files/_doc/localhost-119?refresh=true< using >{"num":119, "name":"/opt/arkime/raw/localhost-250715-00000119.pcap", "first":1752544721, "node":"localhost", "locked":0, "startTimestamp":1752195390365, "firstTimestamp":1752544721399}<
Jul 11 08:56:30 writer-simple.c:603 writer_simple_write(): opened 1 /opt/arkime/raw/localhost-250715-00000119.pcap 11
Jul 11 08:56:30 reader-libpcap-file.c:464 reader_libpcapfile_read(): Waiting to process more packets, es q: 1  best q 1
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/1 ASYNC 201 http://localhost:9200/arkime_files/_doc/localhost-119?refresh=true 184/169 0ms 13ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 27
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 33
Jul 11 08:56:30 db.c:2659 arkime_db_update_file(): Updated localhost-119 with {"doc": {"filesize": 10737526, "packetsSize": 10737526, "packets": 7516, "finishTimestamp":1752195390509, "lastTimestamp":1752544722319}}
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 2/181 0ms 7ms
Jul 11 08:56:30 db.c:2225 arkime_db_create_file_full(): Creating file 120 with key >/arkime_files/_doc/localhost-120?refresh=true< using >{"num":120, "name":"/opt/arkime/raw/localhost-250715-00000120.pcap", "first":1752544722, "node":"localhost", "locked":0, "startTimestamp":1752195390516, "firstTimestamp":1752544722319}<
Jul 11 08:56:30 writer-simple.c:603 writer_simple_write(): opened 1 /opt/arkime/raw/localhost-250715-00000120.pcap 11
Jul 11 08:56:30 http.c:605 arkime_http_curl_watch_open_callback(): Connected 2/1 - http://localhost:9200   57500->[::1]:9200 - fd:12
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 2/2 ASYNC 200 http://localhost:9200/arkime_files/_update/localhost-119 137/158 0ms 8ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 20
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/2 ASYNC 201 http://localhost:9200/arkime_files/_doc/localhost-120?refresh=true 184/168 1ms 14ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 31
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 18
Jul 11 08:56:30 db.c:2659 arkime_db_update_file(): Updated localhost-120 with {"doc": {"filesize": 10738612, "packetsSize": 10738612, "packets": 7514, "finishTimestamp":1752195390665, "lastTimestamp":1752544722936}}
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 2/181 0ms 4ms
Jul 11 08:56:30 db.c:2225 arkime_db_create_file_full(): Creating file 121 with key >/arkime_files/_doc/localhost-121?refresh=true< using >{"num":121, "name":"/opt/arkime/raw/localhost-250715-00000121.pcap", "first":1752544722, "node":"localhost", "locked":0, "startTimestamp":1752195390669, "firstTimestamp":1752544722936}<
Jul 11 08:56:30 writer-simple.c:603 writer_simple_write(): opened 1 /opt/arkime/raw/localhost-250715-00000121.pcap 11
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 2/2 ASYNC 200 http://localhost:9200/arkime_files/_update/localhost-120 137/157 0ms 7ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 31
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/2 ASYNC 201 http://localhost:9200/arkime_files/_doc/localhost-121?refresh=true 184/169 0ms 11ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 36
Jul 11 08:56:30 http.c:605 arkime_http_curl_watch_open_callback(): Connected 4/2 - http://localhost:9200   57516->[::1]:9200 - fd:13
Jul 11 08:56:30 http.c:605 arkime_http_curl_watch_open_callback(): Connected 4/3 - http://localhost:9200   57518->[::1]:9200 - fd:14
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 4/4 ASYNC 201 http://localhost:9200/arkime_dstats/_doc/localhost-678-5 842/158 0ms 8ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 42
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 3/4 ASYNC 200 http://localhost:9200/arkime_stats/_doc/localhost 842/154 0ms 9ms
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 2/4 ASYNC 201 http://localhost:9200/arkime_dstats/_doc/localhost-56-60 843/156 2ms 9ms
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 200 http://localhost:9200/arkime_dstats/_doc/localhost-5-600 844/160 1ms 10ms
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 2/181 0ms 8ms
Jul 11 08:56:30 db.c:2225 arkime_db_create_file_full(): Creating file 122 with key >/arkime_files/_doc/localhost-122?refresh=true< using >{"num":122, "name":"/opt/arkime/raw/localhost-250715-00000122.pcap", "first":1752544723, "node":"localhost", "locked":0, "startTimestamp":1752195390789, "firstTimestamp":1752544723516}<
Jul 11 08:56:30 writer-simple.c:603 writer_simple_write(): opened 1 /opt/arkime/raw/localhost-250715-00000122.pcap 15
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 201 http://localhost:9200/arkime_files/_doc/localhost-122?refresh=true 184/169 0ms 12ms
Jul 11 08:56:30 db.c:2659 arkime_db_update_file(): Updated localhost-121 with {"doc": {"filesize": 10737704, "packetsSize": 10737704, "packets": 7432, "finishTimestamp":1752195390815, "lastTimestamp":1752544723516}}
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 200 http://localhost:9200/arkime_files/_update/localhost-121 137/159 0ms 9ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 37
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 38
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 42
Jul 11 08:56:30 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 2/181 0ms 6ms
Jul 11 08:56:30 db.c:2225 arkime_db_create_file_full(): Creating file 123 with key >/arkime_files/_doc/localhost-123?refresh=true< using >{"num":123, "name":"/opt/arkime/raw/localhost-250715-00000123.pcap", "first":1752544724, "node":"localhost", "locked":0, "startTimestamp":1752195390944, "firstTimestamp":1752544724235}<
Jul 11 08:56:30 writer-simple.c:603 writer_simple_write(): opened 1 /opt/arkime/raw/localhost-250715-00000123.pcap 11
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 201 http://localhost:9200/arkime_files/_doc/localhost-123?refresh=true 184/168 0ms 15ms
Jul 11 08:56:30 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 13
Jul 11 08:56:30 db.c:2659 arkime_db_update_file(): Updated localhost-122 with {"doc": {"filesize": 10737984, "packetsSize": 10737984, "packets": 7404, "finishTimestamp":1752195390979, "lastTimestamp":1752544724235}}
Jul 11 08:56:30 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 200 http://localhost:9200/arkime_files/_update/localhost-122 137/158 0ms 9ms
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 41
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 41
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 16
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 36
Jul 11 08:56:31 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_sequence/_doc/fn-localhost 2/181 0ms 6ms
Jul 11 08:56:31 db.c:2225 arkime_db_create_file_full(): Creating file 124 with key >/arkime_files/_doc/localhost-124?refresh=true< using >{"num":124, "name":"/opt/arkime/raw/localhost-250715-00000124.pcap", "first":1752544724, "node":"localhost", "locked":0, "startTimestamp":1752195391151, "firstTimestamp":1752544724857}<
Jul 11 08:56:31 writer-simple.c:603 writer_simple_write(): opened 1 /opt/arkime/raw/localhost-250715-00000124.pcap 15
Jul 11 08:56:31 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 201 http://localhost:9200/arkime_files/_doc/localhost-124?refresh=true 184/170 0ms 13ms
Jul 11 08:56:31 db.c:2659 arkime_db_update_file(): Updated localhost-123 with {"doc": {"filesize": 10738654, "packetsSize": 10738654, "packets": 7393, "finishTimestamp":1752195391184, "lastTimestamp":1752544724857}}
Jul 11 08:56:31 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 200 http://localhost:9200/arkime_files/_update/localhost-123 137/157 0ms 10ms
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 38
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 16
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 39
Jul 11 08:56:31 reader-libpcap-file.c:457 reader_libpcapfile_read(): Waiting to process more packets, write q: 12
Jul 11 08:56:31 reader-libpcap-file.c:464 reader_libpcapfile_read(): Waiting to process more packets, es q: 2  best q 1
Jul 11 08:56:31 http.c:406 arkime_http_curlm_check_multi_info(): 2/4 ASYNC 200 http://localhost:9200/_bulk 744/234 0ms 6ms
Jul 11 08:56:31 main.c:792 arkime_quit(): Quitting
Jul 11 08:56:31 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 200 http://localhost:9200/_bulk 132483/305 0ms 25ms
Jul 11 08:56:31 packet.c:694 arkime_packet_log(): packets: 43399 current sessions: 0/1 oldest: 0 - recv: 43399 drop: 0 (0.00) queue: 0 disk: 0 packet: 0 close: 1 ns: 0 frags: 0/0 pstats: 43399/0/0/0/0/0/0 ver: 5.7.1-GIT mem: 3.64%
Jul 11 08:56:31 session.c:995 arkime_session_exit(): sessions: 1 tcp: 0 udp: 0 icmp: 0 sctp: 0 esp: 0 other: 0
Jul 11 08:56:31 main.c:754 arkime_quit_gfunc(): Read exit finished
Jul 11 08:56:31 db.c:2732 arkime_db_can_quit(): Can't quit, sJson[1] 28428
Jul 11 08:56:31 main.c:764 arkime_quit_gfunc(): Can't quit, DB is 1
Jul 11 08:56:31 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 200 http://localhost:9200/_bulk 11855/219 0ms 10ms
Jul 11 08:56:31 db.c:2659 arkime_db_update_file(): Updated localhost-124 with {"doc": {"filesize": 8933397, "packetsSize": 8933397, "packets": 6140, "finishTimestamp":1752195391709, "lastTimestamp":1752544725413}}
Jul 11 08:56:31 main.c:776 arkime_quit_gfunc(): Write exit finished
Jul 11 08:56:31 http.c:406 arkime_http_curlm_check_multi_info(): 1/4 ASYNC 200 http://localhost:9200/arkime_files/_update/localhost-124 135/159 0ms 9ms
Jul 11 08:56:31 main.c:1160 main(): Final cleanup
Jul 11 08:56:31 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_stats/_doc/localhost 839/174 0ms 6ms
Jul 11 08:56:31 http.c:318 arkime_http_send_sync(): 1/1 SYNC 200 http://localhost:9200/arkime_*/_refresh 0/51 0ms 55ms
Jul 11 08:56:31 http.c:719 arkime_http_curl_close_callback(): Close 0/3 - http://localhost:9200   57492->[::1]:9200 fd:9 removed: true
Jul 11 08:56:31 http.c:719 arkime_http_curl_close_callback(): Close 0/2 - http://localhost:9200   57500->[::1]:9200 fd:12 removed: true
Jul 11 08:56:31 http.c:719 arkime_http_curl_close_callback(): Close 0/1 - http://localhost:9200   57516->[::1]:9200 fd:13 removed: true
Jul 11 08:56:31 http.c:719 arkime_http_curl_close_callback(): Close 0/0 - http://localhost:9200   57518->[::1]:9200 fd:14 removed: true
Jul 11 08:56:31 db.c:2934 arkime_db_exit(): totalPackets: 43399 totalSessions: 5 writtenBytes: 61929349 unwrittenBytes: 0 pstats: 43399/0/0/0/0/0/0
